# taxiAPI/models.py
from django.db import models
from django.contrib.auth.models import User

# Ride Types
RIDE_TYPES = [
    ("Economy", "Economy"),
    ("Comfort", "Comfort"),
    ("XL", "XL"),
    ("Cargo", "Cargo"),
]

# Driver Status Types
DRIVER_STATUS_CHOICES = [
    ("available", "Available"),
    ("on_ride", "On Ride"),
    ("offline", "Offline"),
    ("inactive", "Inactive"),
]


class UserProfile(models.Model):
    ROLE_CHOICES = [
        ("rider", "Rider"),
        ("driver", "Driver"),
        ("operator", "Operator"),  # Added for dispatchers
        ("staff", "Staff"),
        ("alumni", "Alumni"),
    ]
    STATUS_CHOICES = [
        ("Active", "Active"),
        ("Inactive", "Inactive"),
        ("Pending", "Pending"),
    ]

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name="profile")
    role = models.CharField(max_length=10, choices=ROLE_CHOICES, default="rider")
    phone_number = models.CharField(max_length=15, unique=True, db_index=True)
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default="Active")
    last_visited_page = models.CharField(max_length=255, blank=True, null=True)

    # Rider-specific fields
    total_rides = models.PositiveBigIntegerField(default=0, null=True, blank=True)
    preferred_ride_types = models.CharField(max_length=50, blank=True, null=True)

    # Driver-specific fields
    license_number = models.CharField(max_length=50, unique=True, null=True, blank=True)
    vehicle_type = models.CharField(
        max_length=10, choices=RIDE_TYPES, null=True, blank=True
    )
    approval_date = models.DateTimeField(null=True, blank=True)
    total_trips = models.PositiveBigIntegerField(default=0, null=True, blank=True)
    amount_made_total = models.DecimalField(
        max_digits=10, decimal_places=2, default=0.0, null=True, blank=True
    )
    current_location = models.CharField(max_length=255, blank=True, null=True)
    # New field for total kilometers driven
    total_kilometers_driven = models.DecimalField(
        max_digits=10, decimal_places=2, default=0.0, null=True, blank=True
    )
    # Driver availability status
    driver_status = models.CharField(
        max_length=20, choices=DRIVER_STATUS_CHOICES, default="available", null=True, blank=True
    )

    class Meta:
        indexes = [
            models.Index(fields=["role", "status"]),
            models.Index(fields=["phone_number"]),
        ]

    def __str__(self):
        return f"{self.user.username} ({self.role})"


# Favorite Locations
class FavoriteLocation(models.Model):
    user_id = models.BigIntegerField(db_index=True)
    name = models.CharField(max_length=50)
    address = models.TextField()
    coordinates = models.CharField(max_length=50, blank=True, null=True)

    class Meta:
        indexes = [
            models.Index(fields=["user_id", "name"]),
        ]

    def __str__(self):
        return f"{self.name} - User {self.user_id}"


# # Ride Model #! Do not delete this model, it is a duplicate Ifound and unused, but for safety until im sure keep
# class Ride(models.Model):
#     STATUS_CHOICES = [
#         ("Requested", "Requested"),
#         ("Accepted", "Accepted"),
#         ("Assigned", "Assigned"),  # Changed from Accepted to Assigned to reflect operator action
#         ("In Progress", "In Progress"),
#         ("Completed", "Completed"),
#         ("Cancelled", "Cancelled"),
#     ]

#     ride_id = models.BigAutoField(primary_key=True)
#     rider_id = models.BigIntegerField(db_index=True)
#     driver_id = models.BigIntegerField(null=True, blank=True, db_index=True)
#     assigned_by = models.BigIntegerField(null=True, blank=True, db_index=True)  # To track which operator assigned the ride
#     pickup_location = models.CharField(max_length=255)
#     pickup_coordinates = models.CharField(max_length=50, blank=True, null=True)
#     dropoff_location = models.CharField(max_length=255)
#     dropoff_coordinates = models.CharField(max_length=50, blank=True, null=True)
#     ride_type = models.CharField(max_length=10, choices=RIDE_TYPES, db_index=True)
#     status = models.CharField(
#         max_length=20, choices=STATUS_CHOICES, default="Requested", db_index=True
#     )
#     requested_at = models.DateTimeField(auto_now_add=True, db_index=True)
#     started_at = models.DateTimeField(null=True, blank=True)
#     completed_at = models.DateTimeField(null=True, blank=True)

#     class Meta:
#         indexes = [
#             models.Index(fields=["rider_id", "status"]),
#             models.Index(fields=["driver_id", "status"]),
#             models.Index(fields=["requested_at"]),
#         ]

#     def __str__(self):
#         return f"Ride {self.ride_id} - User {self.rider_id} ({self.status})"


class RideRequest(models.Model):
    STATUS_CHOICES = [
        ("Pending", "Pending"),
        ("Assigned", "Assigned"),  # Driver accepts or operator assigns
        ("Rejected", "Rejected"),
        ("Completed", "Completed"),
        ("Cancelled", "Cancelled"),
        ("PickedUp", "PickedUp"),      # Future mobile app status
        ("InProgress", "InProgress"),  # Future mobile app status
    ]

    pickup_location = models.CharField(max_length=255)
    dropoff_location = models.CharField(max_length=255)
    ride_datetime = models.DateTimeField()
    passenger_count = models.IntegerField()
    luggage_count = models.IntegerField()
    rider_name = models.CharField(max_length=100)
    rider_phone = models.CharField(max_length=20)
    distance_km = models.DecimalField(max_digits=10, decimal_places=2, default=0.0)
    calculated_fare = models.DecimalField(max_digits=10, decimal_places=2, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    assigned_time = models.DateTimeField(null=True, blank=True)  # Time when the ride was assigned to a driver
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default="Pending")
    driver = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="assigned_rides_as_driver",
    )
    # Add this new field
    rider = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="ride_requests",
    )
    assigned_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="assigned_rides",
    )

    def __str__(self):
        return f"Ride for {self.rider_name} on {self.ride_datetime} from {self.pickup_location} to {self.dropoff_location} - {self.distance_km} km"
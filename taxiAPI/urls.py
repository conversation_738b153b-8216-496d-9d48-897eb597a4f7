from django.urls import path
from .views import (
    RideRequestListView,
    AcceptRideView,
    RejectRideView,
    CancelRideView,
    CancelAssignedRideView,
    OperatorCompleteRideView,
    DeleteRideView,
    FinishRideView,
    AssignDriverView,
    ReassignDriverView,
    DriversListView,
    AssignedRidesView,
    RideSearchView,
)
from .scripts.driver.driverHistory import DriverReportView
from .scripts.apiConnection import test_api
from .scripts.auth.loginAIO import LoginView
from .scripts.bookingSystem.bookARide import BookRideView
from .scripts.auth import registerAIO
from .scripts.dashboard import DashboardView
from .scripts.modules.taxiMath.farEstimate import farEstimateSinusta, test_scenarios
from .scripts.modules.geocode.geo import GeocodeView

urlpatterns = [
    path("geocode/", GeocodeView.as_view(), name="geocode"),
    path("book-ride/", BookRideView.as_view(), name="book-ride"),
    path("test/", test_api, name="test_api"),
    path("login/", LoginView.as_view(), name="login"),
    path(
        "register-rider/",
        registerAIO.RegisterRiderView.as_view(),
        name="register-rider",
    ),
    path("dashboard/", DashboardView.as_view(), name="dashboard"),
    path("ride-requests/", RideRequestListView.as_view(), name="ride-requests"),
    path("accept-ride/<int:ride_id>/", AcceptRideView.as_view(), name="accept-ride"),
    path("reject-ride/<int:ride_id>/", RejectRideView.as_view(), name="reject-ride"),
    path("cancel-ride/<int:ride_id>/", CancelRideView.as_view(), name="cancel-ride"),
    path("cancel-assigned-ride/<int:ride_id>/", CancelAssignedRideView.as_view(), name="cancel-assigned-ride"),
    path("operator-complete-ride/<int:ride_id>/", OperatorCompleteRideView.as_view(), name="operator-complete-ride"),
    path("delete-ride/<int:ride_id>/", DeleteRideView.as_view(), name="delete-ride"),
    path("finish-ride/<int:ride_id>/", FinishRideView.as_view(), name="finish-ride"),
    path("assign-driver/<int:ride_id>/", AssignDriverView.as_view(), name="assign-driver"),
    path("reassign-driver/<int:ride_id>/", ReassignDriverView.as_view(), name="reassign-driver"),
    path('driver-report/', DriverReportView.as_view(), name='driver-report'),
    path('drivers/', DriversListView.as_view(), name='drivers-list'),
    path('assigned-rides/', AssignedRidesView.as_view(), name='assigned-rides'),
    path('rides/search/', RideSearchView.as_view(), name='ride-search'),
    # Module as API calls
    path("fareCalc/", farEstimateSinusta.calculate_taxi_fare_api, name="fare-estimate"),
    path(
        "fareCalcCheatSheet/",
        test_scenarios.generate_cheat_sheet,
        name="fare-estimate-test",
    ),
]

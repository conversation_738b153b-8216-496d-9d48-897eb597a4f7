from datetime import datetime, timedelta
from zoneinfo import ZoneInfo
from django.db import transaction
from django.db.models import Q, Sum, F
from django.http import JsonResponse
from django.shortcuts import render
from django.utils import timezone
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken
from .models import RideRequest, UserProfile
from .serializers import RideRequestSerializer
from .scripts.modules.taxiMath.farEstimate.farEstimateSinusta import calculate_taxi_fare
from django.db.models import Sum, <PERSON>, Max
from django.db.models.functions import <PERSON>runcHour, TruncDay, TruncMonth, <PERSON>runcYear
from datetime import datetime, timedelta
from calendar import monthrange
from rest_framework.permissions import AllowAny
from django.contrib.auth.models import User


class RideRequestListView(APIView):
    permission_classes = [IsAuthenticated]

    # Class variable to store the last update timestamp for pending rides
    last_pending_update = None
    pending_rides_cache = None

    def get(self, request):
        user = request.user
        print(f"User making request: {user.username}, ID: {user.id}")
        
        # Check if user is staff (admin) or has specific groups
        role = ""
        if user.is_staff:
            role = "staff"
            print("User determined as staff (is_staff=True)")
        elif hasattr(user, 'groups'):
            if user.groups.filter(name="Operators").exists():
                role = "operator"
                print("User determined as operator (in Operators group)")
            elif user.groups.filter(name="Drivers").exists():
                role = "driver"
                print("User determined as driver (in Drivers group)")
            elif user.groups.filter(name="Riders").exists():
                role = "rider"
                print("User determined as rider (in Riders group)")
        if not role and hasattr(user, 'profile'):
            role = user.profile.role
            print(f"User role determined from UserProfile: {role}")
        if not role:
            print("User role not determined, checking for custom attribute")
            # Check for custom role attribute if it exists
            if hasattr(user, 'role'):
                role = user.role.lower()
                print(f"User role from custom attribute: {role}")
            else:
                print("No custom role attribute found")
        
        print(f"Final determined role: {role}")
        ride_type = request.query_params.get("type", "all")
        count_only = request.query_params.get("count_only", "false").lower() == "true"
        print(f"Ride type filter requested: {ride_type}")
        print(f"Count only requested: {count_only}")

        # IMPORTANT: For pending rides, always check the database first to see if there are new records
        # before relying on timestamp-based caching
        if ride_type == "pending" and role in ["staff", "operator"]:
            # Count current pending rides in database
            current_pending_count = RideRequest.objects.filter(status="Pending").count()
            
            # Get our cached count if available
            cached_count = 0
            if RideRequestListView.pending_rides_cache is not None:
                cached_count = RideRequestListView.pending_rides_cache.count()
            
            # If counts don't match, force a refresh by ignoring the If-Modified-Since header
            if current_pending_count != cached_count:
                print(f"Pending rides count changed (cache: {cached_count}, db: {current_pending_count}), forcing refresh")
                # Will force a refresh below
            else:
                # Only use the If-Modified-Since logic if counts match
                client_last_modified = request.headers.get('If-Modified-Since', None)
                if client_last_modified:
                    try:
                        client_last_modified_dt = datetime.strptime(client_last_modified, '%Y-%m-%dT%H:%M:%S.%fZ')
                        # Ensure client_last_modified_dt is timezone aware (assuming UTC as the format ends with Z)
                        from datetime import timezone as dt_timezone
                        client_last_modified_dt = client_last_modified_dt.replace(tzinfo=dt_timezone.utc)
                        if RideRequestListView.last_pending_update and client_last_modified_dt >= RideRequestListView.last_pending_update:
                            print("No changes since client last modified, returning 304 Not Modified")
                            return Response(status=status.HTTP_304_NOT_MODIFIED)
                    except ValueError:
                        print("Invalid If-Modified-Since header format, proceeding with full response")

        # Added "operator" role to handle operator views
        if role == "staff" or role == "operator":
            # Filter rides based on the requested type
            if ride_type == "pending":
                ride_requests = RideRequest.objects.filter(status="Pending")
                print(f"Query for staff/operator (pending): returning only pending rides, count: {ride_requests.count()}")
                # Always update the timestamp when fetching pending rides to ensure clients get fresh data
                RideRequestListView.last_pending_update = timezone.now()
                RideRequestListView.pending_rides_cache = ride_requests
                print(f"Updated pending rides cache and timestamp: {RideRequestListView.last_pending_update}")
            elif ride_type == "assigned":
                ride_requests = RideRequest.objects.filter(status__in=["Assigned", "PickedUp", "InProgress"])
                print(f"Query for staff/operator (assigned): returning only assigned rides, count: {ride_requests.count()}")
            elif ride_type == "completed":
                ride_requests = RideRequest.objects.filter(status="Completed")
                print(f"Query for staff/operator (completed): returning only completed rides, count: {ride_requests.count()}")

                # Debug: Log the first completed ride's data
                if ride_requests.exists():
                    first_ride = ride_requests.first()
                    print(f"Sample completed ride - ID: {first_ride.id}, fare: {first_ride.calculated_fare}, distance: {first_ride.distance_km}")
            elif ride_type == "all":
                ride_requests = RideRequest.objects.all()
                print(f"Query for staff/operator (all): returning all rides, count: {ride_requests.count()}")
            else:
                # Default behavior - return all rides
                ride_requests = RideRequest.objects.all()
                print(f"Query for staff/operator (default): returning all rides, count: {ride_requests.count()}")
        elif role == "driver":
            if ride_type == "accepted":
                ride_requests = RideRequest.objects.filter(driver=user, status="Assigned")
                print(f"Query for driver (accepted/assigned): count: {ride_requests.count()}")
            elif ride_type == "completed":
                ride_requests = RideRequest.objects.filter(driver=user, status="Completed")
                print(f"Query for driver (completed): count: {ride_requests.count()}")
            elif ride_type == "pending":
                ride_requests = RideRequest.objects.filter(status="Pending")
                print(f"Query for driver (pending): count: {ride_requests.count()}")
            else:
                # Default behavior for drivers - only show pending rides
                ride_requests = RideRequest.objects.filter(status="Pending")
                print(f"Query for driver (default): showing only pending rides, count: {ride_requests.count()}")
        elif role == "rider":
            # Now works with the new rider field
            ride_requests = RideRequest.objects.filter(rider=user)
            print(f"Query for rider: count: {ride_requests.count()}")
        else:
            ride_requests = RideRequest.objects.none()
            print("Query for unknown role: returning none")

        # Handle count_only requests efficiently
        if count_only:
            count = ride_requests.count()
            print(f"Returning count only: {count}")
            return Response({"count": count}, status=status.HTTP_200_OK)
        
        # Normal processing for full data requests
        serializer = RideRequestSerializer(ride_requests, many=True)
        print(f"Serialized data count: {len(serializer.data)}")

        # Debug: Log serialized data for completed rides
        if ride_type == "completed" and serializer.data:
            first_serialized = serializer.data[0]
            print(f"Sample serialized completed ride - ID: {first_serialized.get('id')}, fare: {first_serialized.get('calculated_fare')}, distance: {first_serialized.get('distance_km')}")
        
        # Add Last-Modified header to the response for pending rides
        response = Response(serializer.data)
        if ride_type == "pending" and role in ["staff", "operator"] and RideRequestListView.last_pending_update:
            response['Last-Modified'] = RideRequestListView.last_pending_update.strftime('%Y-%m-%dT%H:%M:%S.%fZ')
            print(f"Added Last-Modified header: {response['Last-Modified']}")
        return response


class AcceptRideView(APIView):
    """Allow drivers to accept a pending ride."""

    permission_classes = [IsAuthenticated]

    def post(self, request, ride_id):
        try:
            with transaction.atomic():
                ride_request = RideRequest.objects.select_for_update().get(
                    id=ride_id, status="Pending"
                )
                if ride_request.driver:
                    return Response(
                        {"error": "Ride already accepted by another driver"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                ride_request.driver = request.user
                ride_request.status = "Assigned"
                ride_request.save()
                
                # Update driver status to 'on_ride'
                if hasattr(request.user, 'profile'):
                    try:
                        driver_profile = request.user.profile
                        driver_profile.driver_status = "on_ride"
                        driver_profile.save()
                        print(f"Updated driver {request.user.username} status to 'on_ride' after accepting ride")
                    except Exception as e:
                        print(f"Error updating driver status on acceptance: {e}")
                else:
                    print(f"Driver {request.user.username} doesn't have a profile, status not updated")
            return Response(
                {"message": "Ride accepted successfully"}, status=status.HTTP_200_OK
            )
        except RideRequest.DoesNotExist:
            return Response(
                {"error": "Ride not found or not pending"},
                status=status.HTTP_404_NOT_FOUND,
            )


class RejectRideView(APIView):
    """Allow drivers to reject a pending ride."""

    permission_classes = [IsAuthenticated]

    def post(self, request, ride_id):
        try:
            ride_request = RideRequest.objects.get(id=ride_id, status="Pending")
            ride_request.status = "Rejected"
            ride_request.save()
            # Update the last_pending_update timestamp since the pending rides list has changed
            RideRequestListView.last_pending_update = timezone.now()
            print(f"Updated last_pending_update timestamp due to ride rejection: {RideRequestListView.last_pending_update}")
            return Response(
                {"message": "Ride rejected successfully"}, status=status.HTTP_200_OK
            )
        except RideRequest.DoesNotExist:
            return Response(
                {"error": "Ride not found or not pending"},
                status=status.HTTP_404_NOT_FOUND,
            )


class CancelRideView(APIView):
    """Allow drivers to cancel an assigned ride."""

    permission_classes = [IsAuthenticated]

    def post(self, request, ride_id):
        try:
            with transaction.atomic():
                ride_request = RideRequest.objects.select_for_update().get(
                    id=ride_id, driver=request.user, status="Assigned"
                )
                ride_request.status = "Pending"
                ride_request.driver = None
                ride_request.save()
                
                # Update driver status to 'available'
                if hasattr(request.user, 'profile'):
                    try:
                        driver_profile = request.user.profile
                        driver_profile.driver_status = "available"
                        driver_profile.save()
                        print(f"Updated driver {request.user.username} status to 'available' after ride cancellation")
                    except Exception as e:
                        print(f"Error updating driver status on cancellation: {e}")
                else:
                    print(f"Driver {request.user.username} doesn't have a profile, status not updated")
            return Response(
                {"message": "Ride canceled successfully"}, status=status.HTTP_200_OK
            )
        except RideRequest.DoesNotExist:
            return Response(
                {"error": "Ride not found or not accepted by you"},
                status=status.HTTP_404_NOT_FOUND,
            )


class DeleteRideView(APIView):
    """Allow drivers to delete an assigned ride."""

    permission_classes = [IsAuthenticated]

    def post(self, request, ride_id):
        try:
            ride_request = RideRequest.objects.get(
                id=ride_id, driver=request.user, status="Assigned"
            )
            ride_request.delete()
            return Response(
                {"message": "Ride deleted successfully"}, status=status.HTTP_200_OK
            )
        except RideRequest.DoesNotExist:
            return Response(
                {"error": "Ride not found or not assigned to you"},
                status=status.HTTP_404_NOT_FOUND,
            )


class FinishRideView(APIView):
    """Mark an assigned ride as completed and update driver's profile."""

    permission_classes = [IsAuthenticated]

    def post(self, request, ride_id):
        try:
            ride_request = RideRequest.objects.get(
                id=ride_id, driver=request.user, status="Assigned"
            )

            # Calculate fare if not already set (distance should already be set when ride is created)
            print(f"Current ride values - fare: {ride_request.calculated_fare}, distance: {ride_request.distance_km}")
            print(f"Passengers: {ride_request.passenger_count}, Luggage: {ride_request.luggage_count}")

            if not ride_request.calculated_fare and ride_request.distance_km > 0:
                try:
                    print(f"Calculating fare for ride {ride_id} with distance {ride_request.distance_km} km")

                    # Use current time for trip_time (you might want to use ride_request.ride_datetime instead)
                    from datetime import datetime
                    trip_time = datetime.now().time()

                    calculated_fare = calculate_taxi_fare(
                        distance_km=ride_request.distance_km,
                        passenger_count=ride_request.passenger_count,
                        luggage_count=ride_request.luggage_count,
                        trip_time=trip_time,
                        is_holiday=False  # You might want to add holiday detection logic
                    )

                    ride_request.calculated_fare = calculated_fare
                    print(f"Set calculated_fare to: {ride_request.calculated_fare}")

                except Exception as e:
                    print(f"Error calculating fare for ride {ride_id}: {e}")
                    import traceback
                    traceback.print_exc()
                    # Set default fare if calculation fails
                    ride_request.calculated_fare = 20.00  # Default minimum fare
                    print(f"Set default calculated_fare to: {ride_request.calculated_fare}")
            elif not ride_request.distance_km or ride_request.distance_km <= 0:
                print(f"Distance not set or invalid ({ride_request.distance_km}), setting default values")
                ride_request.distance_km = 5.0  # Default 5km
                ride_request.calculated_fare = 25.00  # Default fare for 5km
                print(f"Set default distance: {ride_request.distance_km} km, fare: {ride_request.calculated_fare}")
            else:
                print(f"Fare already set ({ride_request.calculated_fare}), skipping calculation")

            ride_request.status = "Completed"
            ride_request.completed_at = timezone.now()  # Set completion time
            ride_request.save()

            # Update driver profile with statistics and set status back to available
            UserProfile.objects.filter(user=ride_request.driver).update(
                total_kilometers_driven=F("total_kilometers_driven") + ride_request.distance_km,
                amount_made_total=F("amount_made_total") + ride_request.calculated_fare,
                total_trips=F("total_trips") + 1,
                driver_status="available"  # Set driver back to available
            )
            
            print(f"Driver {ride_request.driver.username} completed ride and is now available")

            return Response(
                {"message": "Ride finished successfully"}, status=status.HTTP_200_OK
            )
        except RideRequest.DoesNotExist:
            return Response(
                {"error": "Ride not found or not accepted by you"},
                status=status.HTTP_404_NOT_FOUND,
            )


class OperatorCompleteRideView(APIView):
    """Allow operators to mark assigned rides as completed."""
    permission_classes = [IsAuthenticated]

    def post(self, request, ride_id):
        user = request.user
        # Check if user is an operator or staff
        role = ""
        if user.is_staff:
            role = "staff"
        elif hasattr(user, 'profile') and user.profile.role == "staff":
            role = "staff"
        elif hasattr(user, 'groups') and user.groups.filter(name="Operators").exists():
            role = "operator"
        elif hasattr(user, 'profile') and user.profile.role == "operator":
            role = "operator"

        if role not in ["staff", "operator"]:
            return Response(
                {"error": "Only operators or staff can complete rides."},
                status=status.HTTP_403_FORBIDDEN,
            )

        try:
            with transaction.atomic():
                # Find the assigned ride (can be Assigned, PickedUp, or InProgress)
                assigned_statuses = ['Assigned', 'PickedUp', 'InProgress']
                ride_request = RideRequest.objects.select_for_update().get(
                    id=ride_id, status__in=assigned_statuses
                )

                # Store the driver for status update
                assigned_driver = ride_request.driver

                # Calculate fare if not already set (distance should already be set when ride is created)
                print(f"Current ride values - fare: {ride_request.calculated_fare}, distance: {ride_request.distance_km}")
                print(f"Passengers: {ride_request.passenger_count}, Luggage: {ride_request.luggage_count}")

                if not ride_request.calculated_fare and ride_request.distance_km > 0:
                    try:
                        print(f"Calculating fare for ride {ride_id} with distance {ride_request.distance_km} km")

                        # Use current time for trip_time (you might want to use ride_request.ride_datetime instead)
                        from datetime import datetime
                        trip_time = datetime.now().time()

                        calculated_fare = calculate_taxi_fare(
                            distance_km=ride_request.distance_km,
                            passenger_count=ride_request.passenger_count,
                            luggage_count=ride_request.luggage_count,
                            trip_time=trip_time,
                            is_holiday=False  # You might want to add holiday detection logic
                        )

                        ride_request.calculated_fare = calculated_fare
                        print(f"Set calculated_fare to: {ride_request.calculated_fare}")

                    except Exception as e:
                        print(f"Error calculating fare for ride {ride_id}: {e}")
                        import traceback
                        traceback.print_exc()
                        # Set default fare if calculation fails
                        ride_request.calculated_fare = 20.00  # Default minimum fare
                        print(f"Set default calculated_fare to: {ride_request.calculated_fare}")
                elif not ride_request.distance_km or ride_request.distance_km <= 0:
                    print(f"Distance not set or invalid ({ride_request.distance_km}), setting default values")
                    ride_request.distance_km = 5.0  # Default 5km
                    ride_request.calculated_fare = 25.00  # Default fare for 5km
                    print(f"Set default distance: {ride_request.distance_km} km, fare: {ride_request.calculated_fare}")
                else:
                    print(f"Fare already set ({ride_request.calculated_fare}), skipping calculation")

                # Mark ride as completed
                ride_request.status = "Completed"
                ride_request.completed_at = timezone.now()  # Set completion time

                print(f"Before saving - fare: {ride_request.calculated_fare}, distance: {ride_request.distance_km}")
                ride_request.save()
                print(f"After saving - fare: {ride_request.calculated_fare}, distance: {ride_request.distance_km}")

                # Verify the ride was saved correctly
                saved_ride = RideRequest.objects.get(id=ride_id)
                print(f"Verified from DB - fare: {saved_ride.calculated_fare}, distance: {saved_ride.distance_km}, status: {saved_ride.status}")

                # Update driver profile with statistics and set status back to available
                if assigned_driver:
                    UserProfile.objects.filter(user=assigned_driver).update(
                        total_kilometers_driven=F("total_kilometers_driven") + ride_request.distance_km,
                        amount_made_total=F("amount_made_total") + ride_request.calculated_fare,
                        total_trips=F("total_trips") + 1,
                        driver_status="available"  # Set driver back to available
                    )
                    print(f"Updated driver {assigned_driver.username} stats and set status to 'available' after operator completion")

                # Update timestamps for assigned rides cache (since ride is no longer assigned)
                AssignedRidesView.last_assigned_update = timezone.now()
                print(f"Updated last_assigned_update timestamp due to operator ride completion")

                # Invalidate assigned rides cache
                AssignedRidesView.assigned_rides_cache = None
                print("Invalidated assigned rides cache")

            return Response(
                {"message": "Ride completed successfully."},
                status=status.HTTP_200_OK
            )
        except RideRequest.DoesNotExist:
            return Response(
                {"error": "Ride not found or not in an assigned status."},
                status=status.HTTP_404_NOT_FOUND,
            )


class CancelAssignedRideView(APIView):
    """Allow operators to cancel assigned rides and send them back to pending status."""
    permission_classes = [IsAuthenticated]

    def post(self, request, ride_id):
        user = request.user
        # Check if user is an operator or staff
        role = ""
        if user.is_staff:
            role = "staff"
        elif hasattr(user, 'profile') and user.profile.role == "staff":
            role = "staff"
        elif hasattr(user, 'groups') and user.groups.filter(name="Operators").exists():
            role = "operator"
        elif hasattr(user, 'profile') and user.profile.role == "operator":
            role = "operator"

        if role not in ["staff", "operator"]:
            return Response(
                {"error": "Only operators or staff can cancel assigned rides."},
                status=status.HTTP_403_FORBIDDEN,
            )

        try:
            with transaction.atomic():
                # Find the assigned ride (can be Assigned, PickedUp, or InProgress)
                #! Assigned only used pickedUp and inProgress will only work when we make an  App sadly for now :/
                assigned_statuses = ['Assigned', 'PickedUp', 'InProgress']
                ride_request = RideRequest.objects.select_for_update().get(
                    id=ride_id, status__in=assigned_statuses
                )

                # Store the driver for status update
                assigned_driver = ride_request.driver

                # Reset ride to pending status
                ride_request.status = "Pending"
                ride_request.driver = None
                ride_request.assigned_by = None
                ride_request.assigned_time = None
                ride_request.save()

                # Update driver status back to 'available' if there was an assigned driver
                if assigned_driver and hasattr(assigned_driver, 'profile'):
                    try:
                        driver_profile = assigned_driver.profile
                        driver_profile.driver_status = "available"
                        driver_profile.save()
                        print(f"Updated driver {assigned_driver.username} status to 'available' after operator cancellation")
                    except Exception as e:
                        print(f"Error updating driver status on operator cancellation: {e}")
                elif assigned_driver:
                    print(f"Driver {assigned_driver.username} doesn't have a profile, status not updated")

                # Update timestamps for both pending and assigned rides caches
                RideRequestListView.last_pending_update = timezone.now()
                AssignedRidesView.last_assigned_update = timezone.now()
                print(f"Updated cache timestamps due to operator ride cancellation")

                # Invalidate both caches
                RideRequestListView.pending_rides_cache = None
                AssignedRidesView.assigned_rides_cache = None
                print("Invalidated both pending and assigned rides caches")

            return Response(
                {"message": "Ride cancelled and sent back to pending status successfully."},
                status=status.HTTP_200_OK
            )
        except RideRequest.DoesNotExist:
            return Response(
                {"error": "Ride not found or not in an assigned status."},
                status=status.HTTP_404_NOT_FOUND,
            )


class AssignDriverView(APIView):
    """Allow operators to assign drivers to pending rides."""
    permission_classes = [IsAuthenticated]

    def post(self, request, ride_id):
        user = request.user
        # Check if user is an operator or staff
        role = ""
        if user.is_staff:
            role = "staff"
        elif hasattr(user, 'profile') and user.profile.role == "staff":
            role = "staff"
        elif hasattr(user, 'groups') and user.groups.filter(name="Operators").exists():
            role = "operator"
        elif hasattr(user, 'profile') and user.profile.role == "operator":
            role = "operator"

        if role not in ["staff", "operator"]:
            return Response(
                {"error": "Only operators or staff can assign drivers."},
                status=status.HTTP_403_FORBIDDEN,
            )

        driver_id = request.data.get("driver_id")
        print(f"Assignment request: user={user.username}, role={role}, driver_id={driver_id}")

        if not driver_id:
            return Response(
                {"error": "Driver ID is required."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            driver = User.objects.get(id=driver_id)
            # Check if the driver has the correct role
            driver_role = ""
            if hasattr(driver, 'profile'):
                driver_role = driver.profile.role
            elif hasattr(driver, 'groups') and driver.groups.filter(name="Drivers").exists():
                driver_role = "driver"

            print(f"Selected driver: {driver.username}, driver_role={driver_role}, is_staff={driver.is_staff}")
            
            if role == "staff":
                # Staff members can assign any driver (including themselves)
                # If they're assigning themselves, skip driver role validation
                if driver.id != user.id:
                    # Staff member is assigning another driver, validate that user is a driver
                    if driver_role != "driver":
                        return Response(
                            {"error": "Selected user is not a driver."},
                            status=status.HTTP_400_BAD_REQUEST,
                        )
                # If staff member is assigning themselves, no driver role validation needed
            else:
                # For operators, validate that selected user is a driver
                if driver_role != "driver":
                    return Response(
                        {"error": "Selected user is not a driver."},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

            # Check if the driver is already on a ride (for both staff and operators)
            if hasattr(driver, 'profile') and driver.profile.driver_status == "on_ride":
                return Response(
                    {"error": "This driver is currently on another ride and cannot be assigned."},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Check if driver is already assigned to an active ride
            active_rides_with_driver = RideRequest.objects.filter(
                driver=driver,
                status__in=['Assigned', 'PickedUp', 'InProgress']
            ).exists()

            if active_rides_with_driver:
                return Response(
                    {"error": "This driver is already assigned to an active ride."},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            with transaction.atomic():
                ride_request = RideRequest.objects.select_for_update().get(
                    id=ride_id, status="Pending"
                )
                if ride_request.driver:
                    return Response(
                        {"error": "Ride already assigned to a driver."},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                ride_request.driver = driver
                ride_request.assigned_by = user
                ride_request.status = "Assigned"
                ride_request.assigned_time = timezone.now()
                ride_request.save()
                
                # Update driver status to 'on_ride'
                if hasattr(driver, 'profile'):
                    try:
                        driver_profile = driver.profile
                        driver_profile.driver_status = "on_ride"
                        driver_profile.save()
                        print(f"Updated driver {driver.username} status to 'on_ride'")
                    except Exception as e:
                        print(f"Error updating driver status: {e}")
                else:
                    print(f"Driver {driver.username} doesn't have a profile, status not updated")
                
                # Update the last_pending_update timestamp since the pending rides list has changed
                RideRequestListView.last_pending_update = timezone.now()
                print(f"Updated last_pending_update timestamp due to driver assignment: {RideRequestListView.last_pending_update}")
                
                # Also update the last_assigned_update timestamp since the assigned rides list has changed
                AssignedRidesView.last_assigned_update = timezone.now()
                print(f"Updated last_assigned_update timestamp due to driver assignment: {AssignedRidesView.last_assigned_update}")
                
                # Invalidate both caches
                RideRequestListView.pending_rides_cache = None
                AssignedRidesView.assigned_rides_cache = None
                print("Invalidated both pending and assigned rides caches")
            return Response(
                {"message": "Driver assigned successfully."}, status=status.HTTP_200_OK
            )
        except User.DoesNotExist:
            return Response(
                {"error": "Driver not found."},
                status=status.HTTP_404_NOT_FOUND,
            )
        except RideRequest.DoesNotExist:
            return Response(
                {"error": "Ride not found or not pending."},
                status=status.HTTP_404_NOT_FOUND,
            )


class ReassignDriverView(APIView):
    """Allow operators to reassign drivers for already assigned rides."""
    permission_classes = [IsAuthenticated]

    def post(self, request, ride_id):
        user = request.user
        # Check if user is an operator or staff
        role = ""
        if user.is_staff:
            role = "staff"
        elif hasattr(user, 'profile') and user.profile.role == "staff":
            role = "staff"
        elif hasattr(user, 'groups') and user.groups.filter(name="Operators").exists():
            role = "operator"
        elif hasattr(user, 'profile') and user.profile.role == "operator":
            role = "operator"

        if role not in ["staff", "operator"]:
            return Response(
                {"error": "Only operators or staff can reassign drivers."},
                status=status.HTTP_403_FORBIDDEN,
            )

        driver_id = request.data.get("driver_id")
        if not driver_id:
            return Response(
                {"error": "Driver ID is required."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            new_driver = User.objects.get(id=driver_id)
            # Check if the new driver has the correct role
            driver_role = ""
            if hasattr(new_driver, 'profile'):
                driver_role = new_driver.profile.role
            elif hasattr(new_driver, 'groups') and new_driver.groups.filter(name="Drivers").exists():
                driver_role = "driver"

            if driver_role != "driver":
                return Response(
                    {"error": "Selected user is not a driver."},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Check if the driver is already on a ride
            if hasattr(new_driver, 'profile') and new_driver.profile.driver_status == "on_ride":
                return Response(
                    {"error": "This driver is currently on another ride and cannot be assigned."},
                    status=status.HTTP_400_BAD_REQUEST,
                )
                
            # Check if driver is already assigned to an active ride
            active_rides_with_driver = RideRequest.objects.filter(
                driver=new_driver, 
                status__in=['Assigned', 'PickedUp', 'InProgress']
            ).exists()
            
            if active_rides_with_driver:
                return Response(
                    {"error": "This driver is already assigned to an active ride."},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            with transaction.atomic():
                # Find the assigned ride (can be Assigned, PickedUp, or InProgress)
                assigned_statuses = ['Assigned', 'PickedUp', 'InProgress']
                ride_request = RideRequest.objects.select_for_update().get(
                    id=ride_id, status__in=assigned_statuses
                )

                # Store the old driver for status update
                old_driver = ride_request.driver

                # Update ride with new driver
                ride_request.driver = new_driver
                ride_request.assigned_by = user
                ride_request.assigned_time = timezone.now()  # Update assignment time
                ride_request.save()

                # Update old driver status back to 'available' if there was one
                if old_driver and hasattr(old_driver, 'profile'):
                    try:
                        old_driver_profile = old_driver.profile
                        old_driver_profile.driver_status = "available"
                        old_driver_profile.save()
                        print(f"Updated old driver {old_driver.username} status to 'available' after reassignment")
                    except Exception as e:
                        print(f"Error updating old driver status on reassignment: {e}")
                elif old_driver:
                    print(f"Old driver {old_driver.username} doesn't have a profile, status not updated")

                # Update new driver status to 'on_ride'
                if hasattr(new_driver, 'profile'):
                    try:
                        new_driver_profile = new_driver.profile
                        new_driver_profile.driver_status = "on_ride"
                        new_driver_profile.save()
                        print(f"Updated new driver {new_driver.username} status to 'on_ride'")
                    except Exception as e:
                        print(f"Error updating new driver status: {e}")
                else:
                    print(f"New driver {new_driver.username} doesn't have a profile, status not updated")

                # Update timestamps for assigned rides cache
                AssignedRidesView.last_assigned_update = timezone.now()
                print(f"Updated last_assigned_update timestamp due to driver reassignment")

                # Invalidate assigned rides cache
                AssignedRidesView.assigned_rides_cache = None
                print("Invalidated assigned rides cache")

            return Response(
                {"message": "Driver reassigned successfully."},
                status=status.HTTP_200_OK
            )
        except User.DoesNotExist:
            return Response(
                {"error": "Driver not found."},
                status=status.HTTP_404_NOT_FOUND,
            )
        except RideRequest.DoesNotExist:
            return Response(
                {"error": "Ride not found or not in an assigned status."},
                status=status.HTTP_404_NOT_FOUND,
            )


class AssignedRidesView(APIView):
    """Dedicated endpoint to list only assigned rides for operators.
    This endpoint is optimized for the operator dashboard to show only rides that have been assigned to drivers.
    """
    permission_classes = [IsAuthenticated]
    
    # Class variable to store the last update timestamp for assigned rides
    last_assigned_update = None
    assigned_rides_cache = None
    
    def get(self, request):
        try:
            user = request.user
            
            # Check if user is an operator or staff - simplified logic
            is_authorized = False

            # Check Django built-in staff status
            if user.is_staff:
                is_authorized = True
                print(f"User {user.username} authorized as Django staff")
            else:
                # Check profile role for staff
                try:
                    if hasattr(user, 'profile') and user.profile.role == 'staff':
                        is_authorized = True
                        print(f"User {user.username} authorized as staff (profile)")
                except Exception as e:
                    print(f"Error checking staff role in profile: {e}")

                # Check if user is in Operators group
                if not is_authorized:
                    try:
                        if user.groups.filter(name='Operators').exists():
                            is_authorized = True
                            print(f"User {user.username} authorized as operator (group)")
                    except Exception as e:
                        print(f"Error checking operator group: {e}")

                # Check if user has operator role in profile
                if not is_authorized:
                    try:
                        if hasattr(user, 'profile') and user.profile.role == 'operator':
                            is_authorized = True
                            print(f"User {user.username} authorized as operator (profile)")
                    except Exception as e:
                        print(f"Error checking operator role in profile: {e}")

            if not is_authorized:
                print(f"User {user.username} is not authorized to view assigned rides")
                return Response(
                    {"error": "You do not have permission to view assigned rides"},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            # Check if this is a count-only request
            count_only = request.query_params.get("count_only", "false").lower() == "true"
            print(f"Count only requested for assigned rides: {count_only}")
            
            # Get all rides that have been assigned to drivers but not yet completed
            # This includes Assigned, PickedUp, and InProgress statuses
            assigned_statuses = ['Assigned', 'PickedUp', 'InProgress']
            rides = RideRequest.objects.filter(
                status__in=assigned_statuses
            ).select_related('driver').order_by('-ride_datetime')
            
            # If this is a count-only request, return just the count
            if count_only:
                count = rides.count()
                print(f"Returning assigned rides count only: {count}")
                return Response({"count": count}, status=status.HTTP_200_OK)
            
            # Check if the client sent a last-modified timestamp
            client_last_modified = request.headers.get('If-Modified-Since', None)
            if client_last_modified and AssignedRidesView.last_assigned_update:
                try:
                    client_last_modified_dt = datetime.strptime(client_last_modified, '%Y-%m-%dT%H:%M:%S.%fZ')
                    # Ensure client_last_modified_dt is timezone aware (assuming UTC as the format ends with Z)
                    from datetime import timezone as dt_timezone
                    client_last_modified_dt = client_last_modified_dt.replace(tzinfo=dt_timezone.utc)
                    if client_last_modified_dt >= AssignedRidesView.last_assigned_update:
                        print("No changes to assigned rides since client last modified, returning 304 Not Modified")
                        return Response(status=status.HTTP_304_NOT_MODIFIED)
                except ValueError:
                    print("Invalid If-Modified-Since header format, proceeding with full response")
            
            print(f"Found {rides.count()} assigned rides")
            
            # Update the cache and last update timestamp
            AssignedRidesView.last_assigned_update = timezone.now()
            AssignedRidesView.assigned_rides_cache = rides
            
            # Serialize the rides with detailed driver information
            response_data = []
            for ride in rides:
                # Convert UTC times to America/Curacao timezone for display
                curacao_tz = ZoneInfo("America/Curacao")
                utc_zone = ZoneInfo('UTC')
                
                # Safely handle assigned_time
                if ride.assigned_time and ride.assigned_time.tzinfo:
                    assigned_time_local = ride.assigned_time.astimezone(curacao_tz)
                elif ride.assigned_time:  # Naive datetime
                    assigned_time_local = ride.assigned_time.replace(tzinfo=utc_zone).astimezone(curacao_tz)
                elif ride.created_at and ride.created_at.tzinfo:
                    assigned_time_local = ride.created_at.astimezone(curacao_tz)
                else:  # Naive datetime
                    assigned_time_local = ride.created_at.replace(tzinfo=utc_zone).astimezone(curacao_tz)
                
                # Safely handle created_at
                if ride.created_at and ride.created_at.tzinfo:
                    created_at_local = ride.created_at.astimezone(curacao_tz)
                else:  # Naive datetime
                    created_at_local = ride.created_at.replace(tzinfo=utc_zone).astimezone(curacao_tz)
                
                # Safely handle ride_datetime
                if ride.ride_datetime and ride.ride_datetime.tzinfo:
                    ride_datetime_local = ride.ride_datetime.astimezone(curacao_tz)
                else:  # Naive datetime
                    ride_datetime_local = ride.ride_datetime.replace(tzinfo=utc_zone).astimezone(curacao_tz)
                
                ride_data = {
                    'id': ride.id,
                    'pickup_location': ride.pickup_location,
                    'dropoff_location': ride.dropoff_location,
                    'passenger_count': ride.passenger_count,
                    'luggage_count': ride.luggage_count,
                    'status': ride.status,
                    'assigned_time': assigned_time_local.strftime('%I:%M %p'),
                    'created_at': created_at_local.strftime('%Y-%m-%d %I:%M %p'),
                    'ride_datetime': ride_datetime_local.strftime('%Y-%m-%d %I:%M %p'),
                    'distance_km': float(ride.distance_km),
                    'calculated_fare': float(ride.calculated_fare) if ride.calculated_fare else None,
                    'estimated_arrival': None,  # Placeholder for future feature
                    'driver_id': None,
                    'driver_name': None,
                    'driver_phone': None,
                    'driver_vehicle': None,
                    'rider_name': ride.rider_name,
                    'rider_phone': ride.rider_phone
                }
                
                # Add driver information if available
                if ride.driver:
                    driver = ride.driver
                    ride_data['driver_id'] = driver.id
                    ride_data['driver_name'] = driver.get_full_name() or driver.username
                    
                    # Try to get additional driver info from profile
                    if hasattr(driver, 'profile'):
                        profile = driver.profile
                        ride_data['driver_phone'] = getattr(profile, 'phone_number', 'N/A')
                        ride_data['driver_vehicle'] = getattr(profile, 'vehicle_type', 'N/A')
                
                response_data.append(ride_data)
            
            print(f"Returning {len(response_data)} assigned rides")
            
            # Add Last-Modified header to the response
            response = Response(response_data, status=status.HTTP_200_OK)
            if AssignedRidesView.last_assigned_update:
                response['Last-Modified'] = AssignedRidesView.last_assigned_update.strftime('%Y-%m-%dT%H:%M:%S.%fZ')
                print(f"Added Last-Modified header: {response['Last-Modified']}")
            return response
        
        except Exception as e:
            print(f"Error in AssignedRidesView: {str(e)}")
            import traceback
            traceback.print_exc()
            return Response(
                {"error": f"An unexpected error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class DriversListView(APIView):
    """List all drivers for operator assignment and management."""
    permission_classes = [IsAuthenticated]
    
    # Class variable to store the last update timestamp for drivers list
    last_drivers_update = None
    drivers_cache = None
    
    def get(self, request):
        try:
            user = request.user
            
            # Check if the user is an operator or staff - simplified logic
            is_authorized = False

            # Check Django built-in staff status
            if user.is_staff:
                is_authorized = True
                print(f"User {user.username} authorized as Django staff")
            else:
                # Check profile role for staff
                try:
                    if hasattr(user, 'profile') and user.profile.role == 'staff':
                        is_authorized = True
                        print(f"User {user.username} authorized as staff (profile)")
                except Exception as e:
                    print(f"Error checking staff role in profile: {e}")

                # Check if user is in Operators group
                if not is_authorized:
                    try:
                        if user.groups.filter(name='Operators').exists():
                            is_authorized = True
                            print(f"User {user.username} authorized as operator (group)")
                    except Exception as e:
                        print(f"Error checking operator group: {e}")

                # Check if user has operator role in profile
                if not is_authorized:
                    try:
                        if hasattr(user, 'profile') and user.profile.role == 'operator':
                            is_authorized = True
                            print(f"User {user.username} authorized as operator (profile)")
                    except Exception as e:
                        print(f"Error checking operator role in profile: {e}")

            if not is_authorized:
                print(f"User {user.username} is not authorized to view drivers list")
                return Response(
                    {"error": "You do not have permission to view drivers list"},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            # Check if this is a count-only request
            count_only = request.query_params.get("count_only", "false").lower() == "true"
            print(f"Count only requested for drivers list: {count_only}")
            
            # Query all users who are drivers
            print(f"Fetching drivers for operator {user.username}")
            drivers_query = Q()
            try:
                drivers_query = drivers_query | Q(groups__name="Drivers")
            except Exception as e:
                print(f"Error querying groups: {e}")
            
            try:
                drivers_query = drivers_query | Q(profile__role="driver")
            except Exception as e:
                print(f"Error querying profile roles: {e}")
            
            # If the requester is a staff member, include their own user ID so they can self-assign
            if user.is_staff:
                drivers_query = drivers_query | Q(pk=user.pk)
            
            drivers = User.objects.filter(drivers_query).distinct()
            
            # If this is a count-only request, return just the count
            if count_only:
                count = drivers.count()
                print(f"Returning drivers count only: {count}")
                return Response({"count": count}, status=status.HTTP_200_OK)
            
            # Check if the client sent a last-modified timestamp
            client_last_modified = request.headers.get('If-Modified-Since', None)
            if client_last_modified and DriversListView.last_drivers_update:
                try:
                    client_last_modified_dt = datetime.strptime(client_last_modified, '%Y-%m-%dT%H:%M:%S.%fZ')
                    # Ensure client_last_modified_dt is timezone aware (assuming UTC as the format ends with Z)
                    from datetime import timezone as dt_timezone
                    client_last_modified_dt = client_last_modified_dt.replace(tzinfo=dt_timezone.utc)
                    if client_last_modified_dt >= DriversListView.last_drivers_update:
                        print("No changes to drivers list since client last modified, returning 304 Not Modified")
                        return Response(status=status.HTTP_304_NOT_MODIFIED)
                except ValueError:
                    print("Invalid If-Modified-Since header format, proceeding with full response")
            
            print(f"Found {drivers.count()} drivers in database")
            
            # Update the cache and last update timestamp
            DriversListView.last_drivers_update = timezone.now()
            DriversListView.drivers_cache = drivers
            
            driver_data = []
            for driver in drivers:
                try:
                    # Basic driver information with minimal processing
                    driver_info = {
                        "id": str(driver.id),
                        "name": driver.get_full_name() or driver.username,
                        "phone": "No phone",
                        "vehicle": "N/A",
                        "status": "offline",  # Default status
                        "rating": 4.5,  # Default placeholder
                        "ridesCompleted": 0,
                        "location": "Unknown",
                        "lastActive": "Unknown"
                    }
                    
                    # Try to get profile data safely
                    if hasattr(driver, 'profile'):
                        profile = driver.profile
                        try:
                            if profile.phone_number:
                                driver_info["phone"] = profile.phone_number
                        except Exception as e:
                            print(f"Error getting phone for driver {driver.id}: {e}")
                        
                        try:
                            if profile.vehicle_type:
                                driver_info["vehicle"] = f"{profile.vehicle_type} Vehicle"
                        except Exception as e:
                            print(f"Error getting vehicle for driver {driver.id}: {e}")
                        
                        try:
                            if profile.current_location:
                                driver_info["location"] = profile.current_location
                        except Exception as e:
                            print(f"Error getting location for driver {driver.id}: {e}")
                        
                        try:
                            # First check for driver_status which takes precedence
                            if hasattr(profile, 'driver_status') and profile.driver_status:
                                driver_info["status"] = profile.driver_status
                                print(f"Using driver_status for {driver.username}: {profile.driver_status}")
                            # Fall back to the general status field if driver_status not set
                            elif profile.status:
                                if profile.status == "Active":
                                    driver_info["status"] = "available"
                                elif profile.status == "Inactive":
                                    driver_info["status"] = "inactive"
                                print(f"Using general status for {driver.username}: {profile.status}")
                        except Exception as e:
                            print(f"Error getting status for driver {driver.id}: {e}")
                    
                    # Count completed rides safely
                    try:
                        completed_rides = RideRequest.objects.filter(
                            driver=driver, status="Completed"
                        ).count()
                        driver_info["ridesCompleted"] = completed_rides
                    except Exception as e:
                        print(f"Error counting rides for driver {driver.id}: {e}")
                    
                    driver_data.append(driver_info)
                except Exception as e:
                    print(f"Error processing driver {driver.id}: {e}")
                    continue
            
            print(f"Returning data for {len(driver_data)} drivers")
            
            # Add Last-Modified header to the response
            response = Response(driver_data, status=status.HTTP_200_OK)
            if DriversListView.last_drivers_update:
                response['Last-Modified'] = DriversListView.last_drivers_update.strftime('%Y-%m-%dT%H:%M:%S.%fZ')
                print(f"Added Last-Modified header: {response['Last-Modified']}")
            return response
        
        except Exception as e:
            print(f"Unexpected error in DriversListView: {str(e)}")
            import traceback
            traceback.print_exc()
            return Response(
                {"error": f"An unexpected error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

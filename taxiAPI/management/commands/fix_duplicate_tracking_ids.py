from django.core.management.base import BaseCommand
from django.db import transaction
from taxiAPI.models import RideRequest
from datetime import datetime
import uuid
from collections import defaultdict


class Command(BaseCommand):
    help = 'Fix duplicate tracking IDs by generating unique ones'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be updated without making changes',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No changes will be made')
            )
        
        # Find duplicate tracking IDs
        tracking_id_counts = defaultdict(list)
        
        for ride in RideRequest.objects.all():
            tracking_id_counts[ride.tracking_id].append(ride)
        
        duplicates = {tid: rides for tid, rides in tracking_id_counts.items() if len(rides) > 1}
        
        if not duplicates:
            self.stdout.write(
                self.style.SUCCESS('No duplicate tracking IDs found!')
            )
            return
        
        total_duplicates = sum(len(rides) - 1 for rides in duplicates.values())
        
        self.stdout.write(
            f'Found {len(duplicates)} duplicate tracking IDs affecting {total_duplicates} rides'
        )
        
        if dry_run:
            for tracking_id, rides in duplicates.items():
                self.stdout.write(f'Tracking ID "{tracking_id}" used by {len(rides)} rides:')
                for ride in rides:
                    sample_new_id = self.generate_unique_tracking_id(ride.created_at)
                    self.stdout.write(f'  Ride #{ride.id} (created: {ride.created_at}) -> would become {sample_new_id}')
            return
        
        # Fix duplicates
        updated_count = 0
        failed_count = 0
        
        with transaction.atomic():
            for tracking_id, rides in duplicates.items():
                # Keep the first ride with the original tracking ID
                # Update all others with new unique IDs
                for ride in rides[1:]:  # Skip the first one
                    try:
                        # Generate a unique tracking ID based on the ride's creation date
                        new_tracking_id = self.generate_unique_tracking_id(ride.created_at)
                        
                        # Ensure uniqueness
                        while RideRequest.objects.filter(tracking_id=new_tracking_id).exists():
                            new_tracking_id = self.generate_unique_tracking_id(ride.created_at)
                        
                        old_id = ride.tracking_id
                        ride.tracking_id = new_tracking_id
                        ride.save(update_fields=['tracking_id'])
                        updated_count += 1
                        
                        self.stdout.write(f'Updated ride #{ride.id}: {old_id} -> {new_tracking_id}')
                        
                    except Exception as e:
                        self.stdout.write(
                            self.style.ERROR(f'Failed to update ride #{ride.id}: {e}')
                        )
                        failed_count += 1
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully updated {updated_count} ride requests with unique tracking IDs'
            )
        )
        
        if failed_count > 0:
            self.stdout.write(
                self.style.WARNING(f'Failed to update {failed_count} ride requests')
            )
    
    def generate_unique_tracking_id(self, created_at=None):
        """Generate a unique tracking ID in format: YYYYMMDD-XXXX"""
        if created_at:
            date_str = created_at.strftime('%Y%m%d')
        else:
            date_str = datetime.now().strftime('%Y%m%d')
        
        # Generate a random 4-digit suffix
        random_suffix = str(uuid.uuid4().int)[:4].zfill(4)
        return f"{date_str}-{random_suffix}"

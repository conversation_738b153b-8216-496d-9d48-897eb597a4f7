from django.core.management.base import BaseCommand
from django.db import transaction
from taxiAPI.models import RideRequest, generate_tracking_id
from datetime import datetime
import uuid


class Command(BaseCommand):
    help = 'Generate unique tracking IDs for existing ride requests'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be updated without making changes',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No changes will be made')
            )
        
        # Find all ride requests without tracking IDs
        rides_without_tracking = RideRequest.objects.filter(
            tracking_id__isnull=True
        ) | RideRequest.objects.filter(tracking_id='')
        
        total_rides = rides_without_tracking.count()
        
        if total_rides == 0:
            self.stdout.write(
                self.style.SUCCESS('All ride requests already have tracking IDs!')
            )
            return
        
        self.stdout.write(
            f'Found {total_rides} ride requests without tracking IDs'
        )
        
        if dry_run:
            self.stdout.write('Would generate tracking IDs for:')
            for ride in rides_without_tracking[:10]:  # Show first 10
                sample_id = self.generate_unique_tracking_id(ride.created_at)
                self.stdout.write(f'  Ride #{ride.id} -> {sample_id}')
            if total_rides > 10:
                self.stdout.write(f'  ... and {total_rides - 10} more')
            return
        
        # Generate tracking IDs for existing rides
        updated_count = 0
        failed_count = 0
        
        with transaction.atomic():
            for ride in rides_without_tracking:
                try:
                    # Generate a unique tracking ID based on the ride's creation date
                    tracking_id = self.generate_unique_tracking_id(ride.created_at)
                    
                    # Ensure uniqueness
                    while RideRequest.objects.filter(tracking_id=tracking_id).exists():
                        tracking_id = self.generate_unique_tracking_id(ride.created_at)
                    
                    ride.tracking_id = tracking_id
                    ride.save(update_fields=['tracking_id'])
                    updated_count += 1
                    
                    if updated_count % 100 == 0:
                        self.stdout.write(f'Updated {updated_count} rides...')
                        
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f'Failed to update ride #{ride.id}: {e}')
                    )
                    failed_count += 1
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully updated {updated_count} ride requests with tracking IDs'
            )
        )
        
        if failed_count > 0:
            self.stdout.write(
                self.style.WARNING(f'Failed to update {failed_count} ride requests')
            )
    
    def generate_unique_tracking_id(self, created_at=None):
        """Generate a unique tracking ID in format: YYYYMMDD-XXXX"""
        if created_at:
            date_str = created_at.strftime('%Y%m%d')
        else:
            date_str = datetime.now().strftime('%Y%m%d')
        
        # Generate a random 4-digit suffix
        random_suffix = str(uuid.uuid4().int)[:4].zfill(4)
        return f"{date_str}-{random_suffix}"

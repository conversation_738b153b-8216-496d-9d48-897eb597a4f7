# taxiAPI/admin.py
from django.contrib import admin
from django.contrib.auth.models import User
from django.contrib.auth.admin import UserAdmin

from .models import UserProfile, FavoriteLocation, RideRequest #, Ride

# 1) Unregister the built-in User admin:
admin.site.unregister(User)

# 2) Re-register it with search_fields:
@admin.register(User)
class CustomUserAdmin(UserAdmin):
    # Add whichever fields you want to search. Usually username, email, first/last name.
    search_fields = ('username', 'email', 'first_name', 'last_name')
    # You can keep or modify other UserAdmin options as needed.

# 3) Now, customize UserProfile admin to use autocomplete:
@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = (
        'get_username',
        'role',
        'get_first_name',
        'get_last_name',
        'phone_number',
        'get_email',
        'status',
    )
    list_filter = ('role', 'status')
    search_fields = (
        'user__username',
        'user__email',
        'user__first_name',
        'user__last_name',
        'phone_number',
    )

    # The key line: enable autocomplete on the "user" foreign key.
    autocomplete_fields = ['user']

    # Example custom actions remain:
    actions = ['show_riders_only', 'show_drivers_only', 'show_staff_only', 'show_alumni_only']

    def get_username(self, obj):
        return obj.user.username
    get_username.short_description = 'Username'

    def get_email(self, obj):
        return obj.user.email
    get_email.short_description = 'Email'

    def get_first_name(self, obj):
        return obj.user.first_name
    get_first_name.short_description = 'First Name'

    def get_last_name(self, obj):
        return obj.user.last_name
    get_last_name.short_description = 'Last Name'

    def show_riders_only(self, request, queryset):
        return queryset.filter(role='rider')
    show_riders_only.short_description = "Show Riders Only"

    def show_drivers_only(self, request, queryset):
        return queryset.filter(role='driver')
    show_drivers_only.short_description = "Show Drivers Only"

    def show_staff_only(self, request, queryset):
        return queryset.filter(role='staff')
    show_staff_only.short_description = "Show Staff Only"

    def show_alumni_only(self, request, queryset):
        return queryset.filter(role='alumni')
    show_alumni_only.short_description = "Show Alumni Only"

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        action_name = request.POST.get('action')
        if action_name in self.actions:
            action_func = getattr(self, action_name)
            return action_func(request, qs)
        return qs



class RideRequestAdmin(admin.ModelAdmin):
    list_display = ('rider_name', 'status' ,'calculated_fare','pickup_location', 'dropoff_location', 'ride_datetime',  'distance_km', 'rider_phone')
    search_fields = ('rider_name', 'rider_phone')


admin.site.register(RideRequest, RideRequestAdmin)
class RideAdmin(admin.ModelAdmin):
    list_display = ('ride_id', 'rider_id', 'driver_id', 'assigned_by', 'status', 'ride_type', 'requested_at')
    search_fields = ('ride_id', 'rider_id', 'driver_id', 'assigned_by')
    list_filter = ('status', 'ride_type')


import requests
import json

# Define the API URL
# Use 'http://localhost:8000/api/' for local testing
# Replace with your production URL (e.g., 'https://yourdomain.com/api/') if needed
API_URL = 'http://hinamizawa.app/api/'  # Ensure this matches your Django server

# Sample ride details including all required fields
ride_details = {
    "pickup_location": "Hato International Airport, Curaçao",
    "dropoff_location": "Blue Bay, Curaçao",
    "ride_datetime": "2023-10-25T14:30:00Z",  # ISO format with UTC timezone
    "passenger_count": 2,
    "luggage_count": 6,
    "rider_name": "Jebb <PERSON><PERSON>",
    "rider_phone": "+59991234567",
    "distance_km": 29.0  # Added required field with a sample distance in kilometers
}

# Send POST request to the /book-ride/ endpoint
try:
    response = requests.post(f"{API_URL}book-ride/", json=ride_details)
    response_data = response.json()

    # Check if the ride was booked successfully
    if response.status_code == 201:
        print("Ride booked successfully!")
        print("Ride ID:", response_data.get("ride_id"))
        print("Calculated Fare:", response_data.get("calculated_fare"))
    else:
        print("Failed to book ride.")
        print("Status Code:", response.status_code)
        print("Response:", response_data)

except requests.exceptions.RequestException as e:
    print("Error connecting to the server:", e)
from rest_framework import serializers
from .models import RideRequest

class DriverSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    username = serializers.CharField()
    first_name = serializers.CharField()
    last_name = serializers.CharField()

class RideRequestSerializer(serializers.ModelSerializer):
    driver_name = serializers.CharField(source='driver.username', read_only=True, allow_null=True)
    driver_phone = serializers.SerializerMethodField()
    driver = serializers.SerializerMethodField()

    class Meta:
        model = RideRequest
        fields = [
            'id', 'tracking_id', 'pickup_location', 'dropoff_location', 'ride_datetime',
            'passenger_count', 'luggage_count', 'distance_km', 'calculated_fare',
            'created_at', 'completed_at', 'status', 'driver_name', 'driver_phone',
            'rider_name', 'rider_phone', 'driver'  # Added driver field
        ]

    def get_driver_phone(self, obj):
        if obj.driver and hasattr(obj.driver, 'profile') and obj.driver.profile:
            return obj.driver.profile.phone_number
        return None

    def get_driver(self, obj):
        if obj.driver:
            return {
                'id': obj.driver.id,
                'username': obj.driver.username,
                'first_name': obj.driver.first_name or '',
                'last_name': obj.driver.last_name or ''
            }
        return None
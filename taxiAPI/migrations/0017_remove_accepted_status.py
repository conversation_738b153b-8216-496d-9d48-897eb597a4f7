# Generated by Django 5.1.6 on 2025-06-10 14:23

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('taxiAPI', '0016_alter_userprofile_driver_status'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name='riderequest',
            name='driver',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_rides_as_driver', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='riderequest',
            name='status',
            field=models.CharField(choices=[('Pending', 'Pending'), ('Assigned', 'Assigned'), ('Rejected', 'Rejected'), ('Completed', 'Completed'), ('Cancelled', 'Cancelled'), ('PickedUp', 'PickedUp'), ('InProgress', 'InProgress')], default='Pending', max_length=20),
        ),
    ]

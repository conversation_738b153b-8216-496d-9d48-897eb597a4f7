# Generated by Django 5.1.6 on 2025-05-19 13:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('taxiAPI', '0010_riderequest_rider'),
    ]

    operations = [
        migrations.AddField(
            model_name='ride',
            name='assigned_by',
            field=models.BigIntegerField(blank=True, db_index=True, null=True),
        ),
        migrations.AlterField(
            model_name='ride',
            name='status',
            field=models.CharField(choices=[('Requested', 'Requested'), ('Accepted', 'Accepted'), ('Assigned', 'Assigned'), ('In Progress', 'In Progress'), ('Completed', 'Completed'), ('Cancelled', 'Cancelled')], db_index=True, default='Requested', max_length=20),
        ),
        migrations.AlterField(
            model_name='riderequest',
            name='status',
            field=models.CharField(choices=[('Pending', 'Pending'), ('Accepted', 'Accepted'), ('Assigned', 'Assigned'), ('Rejected', 'Rejected'), ('Completed', 'Completed'), ('Cancelled', 'Cancelled')], default='Pending', max_length=20),
        ),
        migrations.AlterField(
            model_name='userprofile',
            name='role',
            field=models.CharField(choices=[('rider', 'Rider'), ('driver', 'Driver'), ('operator', 'Operator'), ('staff', 'Staff'), ('alumni', 'Alumni')], default='rider', max_length=10),
        ),
    ]

# Generated by Django 5.1.6 on 2025-03-14 19:29

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('taxiAPI', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='DriverProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.BigIntegerField(db_index=True, unique=True)),
                ('phone_number', models.CharField(db_index=True, max_length=15, unique=True)),
                ('status', models.CharField(choices=[('Active', 'Active'), ('Inactive', 'Inactive'), ('Alumni', 'Alumni')], default='Active', max_length=10)),
                ('last_visited_page', models.CharField(blank=True, max_length=255, null=True)),
                ('driver_status', models.CharField(choices=[('Pending', 'Pending'), ('Approved', 'Approved'), ('Suspended', 'Suspended')], db_index=True, default='Pending', max_length=10)),
                ('license_number', models.CharField(db_index=True, max_length=50, unique=True)),
                ('vehicle_type', models.CharField(choices=[('Economy', 'Economy'), ('Comfort', 'Comfort'), ('XL', 'XL'), ('Cargo', 'Cargo')], max_length=10)),
                ('approval_date', models.DateTimeField(blank=True, null=True)),
                ('total_trips', models.PositiveBigIntegerField(default=0)),
                ('current_location', models.CharField(blank=True, max_length=255, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='RiderProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.BigIntegerField(db_index=True, unique=True)),
                ('phone_number', models.CharField(db_index=True, max_length=15, unique=True)),
                ('status', models.CharField(choices=[('Active', 'Active'), ('Inactive', 'Inactive'), ('Alumni', 'Alumni')], default='Active', max_length=10)),
                ('last_visited_page', models.CharField(blank=True, max_length=255, null=True)),
                ('total_rides', models.PositiveBigIntegerField(default=0)),
                ('preferred_ride_types', models.CharField(blank=True, max_length=50, null=True)),
            ],
        ),
        migrations.RemoveField(
            model_name='userprofile',
            name='user',
        ),
        migrations.RemoveField(
            model_name='favoritelocation',
            name='user',
        ),
        migrations.RemoveField(
            model_name='ride',
            name='current_location',
        ),
        migrations.RemoveField(
            model_name='ride',
            name='driver',
        ),
        migrations.RemoveField(
            model_name='ride',
            name='id',
        ),
        migrations.RemoveField(
            model_name='ride',
            name='rider',
        ),
        migrations.AddField(
            model_name='favoritelocation',
            name='coordinates',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='favoritelocation',
            name='user_id',
            field=models.BigIntegerField(db_index=True, default=0),
        ),
        migrations.AddField(
            model_name='ride',
            name='driver_id',
            field=models.BigIntegerField(blank=True, db_index=True, null=True),
        ),
        migrations.AddField(
            model_name='ride',
            name='dropoff_coordinates',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='ride',
            name='pickup_coordinates',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='ride',
            name='ride_id',
            field=models.BigAutoField(default=0, primary_key=True, serialize=False),
        ),
        migrations.AddField(
            model_name='ride',
            name='rider_id',
            field=models.BigIntegerField(db_index=True, default=0),
        ),
        migrations.AlterField(
            model_name='ride',
            name='dropoff_location',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='ride',
            name='pickup_location',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='ride',
            name='requested_at',
            field=models.DateTimeField(auto_now_add=True, db_index=True),
        ),
        migrations.AlterField(
            model_name='ride',
            name='ride_type',
            field=models.CharField(choices=[('Economy', 'Economy'), ('Comfort', 'Comfort'), ('XL', 'XL'), ('Cargo', 'Cargo')], db_index=True, max_length=10),
        ),
        migrations.AlterField(
            model_name='ride',
            name='status',
            field=models.CharField(choices=[('Requested', 'Requested'), ('Accepted', 'Accepted'), ('In Progress', 'In Progress'), ('Completed', 'Completed'), ('Cancelled', 'Cancelled')], db_index=True, default='Requested', max_length=20),
        ),
        migrations.AddIndex(
            model_name='favoritelocation',
            index=models.Index(fields=['user_id', 'name'], name='taxiAPI_fav_user_id_4592f0_idx'),
        ),
        migrations.AddIndex(
            model_name='ride',
            index=models.Index(fields=['rider_id', 'status'], name='taxiAPI_rid_rider_i_f755f7_idx'),
        ),
        migrations.AddIndex(
            model_name='ride',
            index=models.Index(fields=['driver_id', 'status'], name='taxiAPI_rid_driver__9bd9a0_idx'),
        ),
        migrations.AddIndex(
            model_name='ride',
            index=models.Index(fields=['requested_at'], name='taxiAPI_rid_request_a11690_idx'),
        ),
        migrations.AddIndex(
            model_name='driverprofile',
            index=models.Index(fields=['driver_status', 'vehicle_type'], name='taxiAPI_dri_driver__05b365_idx'),
        ),
        migrations.AddIndex(
            model_name='driverprofile',
            index=models.Index(fields=['total_trips'], name='taxiAPI_dri_total_t_df05e6_idx'),
        ),
        migrations.AddIndex(
            model_name='riderprofile',
            index=models.Index(fields=['total_rides'], name='taxiAPI_rid_total_r_84ac75_idx'),
        ),
        migrations.DeleteModel(
            name='UserProfile',
        ),
    ]

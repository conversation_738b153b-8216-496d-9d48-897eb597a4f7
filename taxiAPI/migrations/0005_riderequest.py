# Generated by Django 5.1.6 on 2025-04-07 17:59

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('taxiAPI', '0004_userprofile_delete_driverprofile_delete_riderprofile_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='RideRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('pickup_location', models.CharField(max_length=255)),
                ('dropoff_location', models.CharField(max_length=255)),
                ('ride_datetime', models.DateTimeField()),
                ('passenger_count', models.IntegerField()),
                ('luggage_count', models.IntegerField()),
                ('rider_name', models.CharField(max_length=100)),
                ('rider_phone', models.CharField(max_length=20)),
                ('calculated_fare', models.DecimalField(decimal_places=2, max_digits=10, null=True)),
                ('created_at', models.DateTime<PERSON>ield(auto_now_add=True)),
            ],
        ),
    ]

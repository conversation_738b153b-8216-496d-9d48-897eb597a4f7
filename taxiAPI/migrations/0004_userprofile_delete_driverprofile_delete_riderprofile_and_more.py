# Generated by Django 5.1.6 on 2025-03-17 16:49

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('taxiAPI', '0003_alter_favoritelocation_user_id_alter_ride_ride_id_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(choices=[('rider', 'Rider'), ('driver', 'Driver'), ('staff', 'Staff'), ('alumni', 'Alumni')], default='rider', max_length=10)),
                ('phone_number', models.CharField(db_index=True, max_length=15, unique=True)),
                ('status', models.CharField(choices=[('Active', 'Active'), ('Inactive', 'Inactive'), ('Pending', 'Pending')], default='Active', max_length=10)),
                ('last_visited_page', models.<PERSON>r<PERSON><PERSON>(blank=True, max_length=255, null=True)),
                ('total_rides', models.PositiveBigIntegerField(blank=True, default=0, null=True)),
                ('preferred_ride_types', models.CharField(blank=True, max_length=50, null=True)),
                ('license_number', models.CharField(blank=True, max_length=50, null=True, unique=True)),
                ('vehicle_type', models.CharField(blank=True, choices=[('Economy', 'Economy'), ('Comfort', 'Comfort'), ('XL', 'XL'), ('Cargo', 'Cargo')], max_length=10, null=True)),
                ('approval_date', models.DateTimeField(blank=True, null=True)),
                ('total_trips', models.PositiveBigIntegerField(blank=True, default=0, null=True)),
                ('current_location', models.CharField(blank=True, max_length=255, null=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.DeleteModel(
            name='DriverProfile',
        ),
        migrations.DeleteModel(
            name='RiderProfile',
        ),
        migrations.AddIndex(
            model_name='userprofile',
            index=models.Index(fields=['role', 'status'], name='taxiAPI_use_role_4dc830_idx'),
        ),
        migrations.AddIndex(
            model_name='userprofile',
            index=models.Index(fields=['phone_number'], name='taxiAPI_use_phone_n_d3bc79_idx'),
        ),
    ]

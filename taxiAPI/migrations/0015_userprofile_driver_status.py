# Generated by Django 5.1.6 on 2025-05-27 16:18

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('taxiAPI', '0014_riderequest_assigned_time'),
    ]

    operations = [
        migrations.AddField(
            model_name='userprofile',
            name='driver_status',
            field=models.CharField(blank=True, choices=[('available', 'Available'), ('on_ride', 'On Ride'), ('offline', 'Offline'), ('inactive', 'Inactive')], default='offline', max_length=20, null=True),
        ),
    ]

# taxiAPI/scripts/auth/loginAIO.py
import logging
from django.contrib.auth import authenticate
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from taxiAPI.models import UserProfile

logger = logging.getLogger(__name__)

class LoginView(APIView):
    """
    API view for handling user authentication and generating JWT tokens.
    """

    def post(self, request):
        """
        Authenticate the user and return JWT tokens on successful login.

        Returns:
            200 OK with refresh token, access token, user role, and last visited page.
            401 Unauthorized if credentials are invalid.
            400 Bad Request if username/password is missing.
        """
        username = request.data.get("username")
        password = request.data.get("password")

        # Basic input validation
        if not username or not password:
            return Response(
                {"error": "Username and password are required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        user = authenticate(username=username, password=password)
        if user is None:
            logger.warning(f"Failed login attempt for username: {username}")
            return Response(
                {"error": "Invalid credentials"},
                status=status.HTTP_401_UNAUTHORIZED
            )

        # Determine user role - prioritize staff status
        role = ""
        last_visited_page = "/"

        if user.is_staff:
            role = "staff"
            logger.info(f"User {username} determined as staff (is_staff=True)")
        else:
            # Check profile for non-staff users
            try:
                profile = user.profile
                role = profile.role
                last_visited_page = profile.last_visited_page or "/"
            except UserProfile.DoesNotExist:
                # Create a default profile if missing
                profile = UserProfile.objects.create(
                    user=user,
                    role="rider",  # Default role
                    phone_number=""  # Add a default or require it elsewhere
                )
                role = profile.role
                last_visited_page = "/"
            except AttributeError:
                logger.info(f"User {username} has no role, using default.")
                role = "Unknown"
                last_visited_page = "/"

        # Generate JWT tokens
        refresh = RefreshToken.for_user(user)
        logger.info(f"User {username} logged in successfully.")

        return Response(
            {
                "refresh": str(refresh),
                "access": str(refresh.access_token),
                "role": role,
                "last_visited_page": last_visited_page,
            },
            status=status.HTTP_200_OK
        )
# taxiAPI/scripts/auth/registerAIO.py
from django.contrib.auth.models import User
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.db import transaction
from taxiAPI.models import UserProfile
from rest_framework_simplejwt.tokens import RefreshToken

class RegisterRiderView(APIView):
    @transaction.atomic
    def post(self, request):
        username = request.data.get("username")
        password = request.data.get("password")
        phone_number = request.data.get("phone_number")
        email = request.data.get("email")
        first_name = request.data.get("first_name")
        last_name = request.data.get("last_name")
        
        if not all([username, password, phone_number, email, first_name, last_name]):
            return Response(
                {"error": "All fields (username, password, phone number, email, first name, last name) are required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if User.objects.filter(username=username).exists():
            return Response(
                {"error": "Username already taken"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if User.objects.filter(email=email).exists():
            return Response(
                {"error": "Email already registered"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if UserProfile.objects.filter(phone_number=phone_number).exists():
            return Response(
                {"error": "Phone number already registered"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        user = User.objects.create_user(
            username=username,
            password=password,
            email=email,
            first_name=first_name,
            last_name=last_name
        )
        
        UserProfile.objects.create(
            user=user,
            phone_number=phone_number,
            role="rider",
            status="Active"
        )
        
        refresh = RefreshToken.for_user(user)
        
        return Response(
            {
                "message": "Rider registered successfully",
                "refresh": str(refresh),
                "access": str(refresh.access_token),
                "role": "rider",
                "last_visited_page": "/"
            },
            status=status.HTTP_201_CREATED
        )
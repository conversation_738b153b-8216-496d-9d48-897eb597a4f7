# taxiAPI/scripts/dashboard.py
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from taxiAPI.models import UserProfile

class DashboardView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user

        # Determine user role - prioritize staff status
        if user.is_staff:
            role = "staff"
        else:
            # Check profile for non-staff users
            try:
                profile = user.profile
                role = profile.role
            except UserProfile.DoesNotExist:
                # Create a default profile if missing
                profile = UserProfile.objects.create(
                    user=user,
                    role="rider",  # Default role
                    phone_number=""  # Adjust as needed
                )
                role = profile.role
            except AttributeError:
                role = "Unknown"

        return Response({
            "username": user.username,
            "role": role
        })
import logging
from django.core.management.base import BaseCommand
from django.contrib.auth.models import User, Group
from django.db.models import Q
from taxiAPI.models import UserProfile, RideRequest

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Updates driver status for all drivers based on their current ride assignments'

    def handle(self, *args, **kwargs):
        # Set up logging
        self.stdout.write(self.style.SUCCESS('Starting driver status update...'))
        
        # Step 1: Get all users who are drivers
        driver_group = Group.objects.filter(name='Drivers').first()
        driver_users = []
        
        if driver_group:
            driver_users = User.objects.filter(groups=driver_group)
            self.stdout.write(f'Found {driver_users.count()} drivers in Drivers group')
        
        # Also get users whose profile role is 'driver'
        profile_drivers = User.objects.filter(profile__role='driver')
        self.stdout.write(f'Found {profile_drivers.count()} drivers with profile role="driver"')
        
        # Combine both sets
        all_drivers = list(set(list(driver_users) + list(profile_drivers)))
        self.stdout.write(f'Total unique drivers: {len(all_drivers)}')
        
        # Step 2: For each driver, check if they have active rides
        assigned_count = 0
        available_count = 0
        
        for driver in all_drivers:
            # Get active ride assignments (assigned, in progress, etc.)
            active_rides = RideRequest.objects.filter(
                driver=driver,
                status__in=['Assigned', 'Accepted', 'PickedUp', 'InProgress']
            )
            
            if active_rides.exists():
                # This driver is on a ride
                if hasattr(driver, 'profile'):
                    driver.profile.driver_status = 'on_ride'
                    driver.profile.save()
                    self.stdout.write(f'Set {driver.username} to "on_ride" status')
                    assigned_count += 1
            else:
                # This driver is available
                if hasattr(driver, 'profile'):
                    driver.profile.driver_status = 'available'
                    driver.profile.save()
                    self.stdout.write(f'Set {driver.username} to "available" status')
                    available_count += 1
        
        # Final report
        self.stdout.write(self.style.SUCCESS(f'Updated {assigned_count} drivers to "on_ride" status'))
        self.stdout.write(self.style.SUCCESS(f'Updated {available_count} drivers to "available" status'))
        self.stdout.write(self.style.SUCCESS('Driver status update complete!'))

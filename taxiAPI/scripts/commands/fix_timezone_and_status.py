import logging
from django.core.management.base import BaseCommand
from django.contrib.auth.models import User, Group
from django.db.models import Q
from django.utils import timezone
from zoneinfo import ZoneInfo
from taxiAPI.models import UserProfile, RideRequest

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Fixes timezone issues in ride requests and updates driver statuses'

    def handle(self, *args, **kwargs):
        self.stdout.write(self.style.SUCCESS('Starting timezone and driver status fixes...'))
        
        # 1. Ensure all ride datetimes have timezone info
        curacao_tz = ZoneInfo("America/Curacao")
        utc_zone = ZoneInfo('UTC')  # Use standard UTC timezone from zoneinfo
        updated_rides = 0
        
        for ride in RideRequest.objects.all():
            # Only update rides with naive datetimes (no timezone)
            if ride.ride_datetime.tzinfo is None:
                original_time = ride.ride_datetime
                # Assume the naive datetime is in Curaçao time
                aware_dt = timezone.make_aware(original_time, timezone=curacao_tz)
                # Store as UTC in the database
                ride.ride_datetime = aware_dt.astimezone(utc_zone)
                ride.save(update_fields=['ride_datetime'])
                updated_rides += 1
                self.stdout.write(f'Updated ride #{ride.id} datetime from {original_time} to {ride.ride_datetime}')
        
        self.stdout.write(self.style.SUCCESS(f'Fixed timezone for {updated_rides} ride requests'))
        
        # 2. Update driver status based on current rides
        driver_group = Group.objects.filter(name='Drivers').first()
        driver_users = []
        
        if driver_group:
            driver_users = User.objects.filter(groups=driver_group)
            self.stdout.write(f'Found {driver_users.count()} drivers in Drivers group')
        
        # Also get users whose profile role is 'driver'
        profile_drivers = User.objects.filter(profile__role='driver')
        self.stdout.write(f'Found {profile_drivers.count()} drivers with profile role="driver"')
        
        # Combine both sets
        all_drivers = list(set(list(driver_users) + list(profile_drivers)))
        self.stdout.write(f'Total unique drivers: {len(all_drivers)}')
        
        # Set all drivers to available first
        UserProfile.objects.filter(user__in=all_drivers).update(driver_status='available')
        self.stdout.write('Reset all drivers to available status')
        
        # Then find drivers with active rides and set them to on_ride
        active_statuses = ['Assigned', 'Accepted']
        drivers_with_rides = 0
        
        for driver in all_drivers:
            # Get active ride assignments
            active_rides = RideRequest.objects.filter(
                driver=driver,
                status__in=active_statuses
            )
            
            if active_rides.exists():
                # This driver is on a ride
                if hasattr(driver, 'profile'):
                    driver.profile.driver_status = 'on_ride'
                    driver.profile.save()
                    self.stdout.write(f'Set {driver.username} to "on_ride" status (has {active_rides.count()} active rides)')
                    drivers_with_rides += 1
        
        self.stdout.write(self.style.SUCCESS(f'Updated {drivers_with_rides} drivers with active rides to "on_ride" status'))
        self.stdout.write(self.style.SUCCESS('Timezone and driver status fix complete!'))

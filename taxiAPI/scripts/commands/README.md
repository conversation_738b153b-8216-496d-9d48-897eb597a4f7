# All commands are in the taxiAPI/management/commands directory.

update_driver_status: Updates driver status based on active rides.
fix_timezone_and_status: Fixes timezone and status of rides.

To run a command, use the following format:

python3 manage.py <command_name>

For example, to run the update_driver_status command, use the following:

python3 manage.py update_driver_status

To run the fix_timezone_and_status command, use the following:

python3 manage.py fix_timezone_and_status

from datetime import datetime, timedelta
from django.db import transaction
from django.db.models import <PERSON><PERSON>, <PERSON>, <PERSON>
from django.http import JsonResponse
from django.shortcuts import render
from django.utils import timezone
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from django.db.models.functions import <PERSON><PERSON><PERSON><PERSON><PERSON>, Trunc<PERSON>ay, Trunc<PERSON>ont<PERSON>, <PERSON>runc<PERSON>ear
from calendar import monthrange
from taxiAPI.models import RideRequest
from taxiAPI.models import UserProfile

class DriverReportView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user
        if user.profile.role != 'driver':
            return Response({"error": "Only drivers can access this report"}, status=status.HTTP_403_FORBIDDEN)

        # Get the period and date filters from the frontend
        period = request.query_params.get('period', 'all')
        start_date_str = request.query_params.get('start_date')
        end_date_str = request.query_params.get('end_date')

        # Parse start_date and end_date from frontend (in YYYY-MM-DD format)
        if start_date_str and end_date_str:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
            rides = RideRequest.objects.filter(
                driver=user,
                status="Completed",
                completed_at__gte=start_date,
                completed_at__lt=end_date
            )
            accepted_rides = RideRequest.objects.filter(
                driver=user,
                status="Assigned",
                created_at__gte=start_date,
                created_at__lt=end_date
            )
        else:  # 'all' period
            start_date = None
            end_date = None
            rides = RideRequest.objects.filter(driver=user, status="Completed")
            accepted_rides = RideRequest.objects.filter(driver=user, status="Assigned")

        # Calculate basic stats
        total_trips = rides.count()
        total_distance = rides.aggregate(total_distance=Sum('distance_km'))['total_distance'] or 0
        total_earnings = rides.aggregate(total_earnings=Sum('calculated_fare'))['total_earnings'] or 0
        average_fare = total_earnings / total_trips if total_trips > 0 else 0

        total_accepted = accepted_rides.count() + total_trips
        completion_rate = (total_trips / total_accepted * 100) if total_accepted > 0 else 0
        acceptance_rate = 75.0  # Placeholder, as we lack offered ride data

        # Earnings trend based on actual ride data
        earnings_trend = {'labels': [], 'data': []}

        if period == 'daily' and start_date:
            # Group by hour for 24-hour period
            start_hour = start_date.replace(hour=0, minute=0, second=0, microsecond=0)
            hours = [start_hour + timedelta(hours=i) for i in range(24)]
            earnings = rides.annotate(hour=TruncHour('completed_at')).values('hour').annotate(
                total_earnings=Sum('calculated_fare')
            ).order_by('hour')
            earnings_dict = {e['hour']: float(e['total_earnings']) for e in earnings}
            earnings_trend['labels'] = [h.strftime('%H:00') for h in hours]
            earnings_trend['data'] = [earnings_dict.get(h, 0.0) for h in hours]

        elif period == 'weekly' and start_date:
            # Group by day for 7-day period
            days = [start_date + timedelta(days=i) for i in range(7)]
            earnings = rides.annotate(day=TruncDay('completed_at')).values('day').annotate(
                total_earnings=Sum('calculated_fare')
            ).order_by('day')
            earnings_dict = {e['day']: float(e['total_earnings']) for e in earnings}
            earnings_trend['labels'] = [d.strftime('%a %d') for d in days]
            earnings_trend['data'] = [earnings_dict.get(d, 0.0) for d in days]

        elif period == 'monthly' and start_date:
            # Group by day for the month
            num_days = monthrange(start_date.year, start_date.month)[1]
            days = [start_date + timedelta(days=i) for i in range(num_days)]
            earnings = rides.annotate(day=TruncDay('completed_at')).values('day').annotate(
                total_earnings=Sum('calculated_fare')
            ).order_by('day')
            earnings_dict = {e['day']: float(e['total_earnings']) for e in earnings}
            earnings_trend['labels'] = [d.strftime('%d %b') for d in days]
            earnings_trend['data'] = [earnings_dict.get(d, 0.0) for d in days]

        elif period == 'yearly' and start_date:
            # Group by month for the year
            months = [start_date.replace(month=m, day=1) for m in range(1, 13)]
            earnings = rides.annotate(month=TruncMonth('completed_at')).values('month').annotate(
                total_earnings=Sum('calculated_fare')
            ).order_by('month')
            earnings_dict = {e['month']: float(e['total_earnings']) for e in earnings}
            earnings_trend['labels'] = [m.strftime('%b') for m in months]
            earnings_trend['data'] = [earnings_dict.get(m, 0.0) for m in months]

        elif period == 'all':
            # Group by year for all time
            year_range = rides.aggregate(min_year=Min('completed_at__year'), max_year=Max('completed_at__year'))
            min_year, max_year = year_range['min_year'], year_range['max_year']
            if min_year and max_year:
                years = [datetime(y, 1, 1) for y in range(min_year, max_year + 1)]
                earnings = rides.annotate(year=TruncYear('completed_at')).values('year').annotate(
                    total_earnings=Sum('calculated_fare')
                ).order_by('year')
                earnings_dict = {e['year']: float(e['total_earnings']) for e in earnings}
                earnings_trend['labels'] = [y.strftime('%Y') for y in years]
                earnings_trend['data'] = [earnings_dict.get(y, 0.0) for y in years]

        #! Placeholder ride types breakdown (update this if ride_type is added later)
        ride_types_breakdown = {
            'economy': total_trips // 2,
            'comfort': total_trips // 4,
            'xl': total_trips // 6,
            'cargo': total_trips // 6
        }

        # Assemble the report data
        profile = user.profile
        report_data = {
            'profile': {
                'username': user.username,
                'license_number': profile.license_number,
                'vehicle_type': profile.vehicle_type,
                'approval_date': profile.approval_date.isoformat() if profile.approval_date else None,
                'current_location': profile.current_location,
            },
            'ride_statistics': {
                'total_trips': total_trips,
                'total_distance': float(total_distance),
                'total_earnings': float(total_earnings),
                'average_fare': float(average_fare),
                'completion_rate': float(completion_rate),
                'acceptance_rate': float(acceptance_rate),
                'ride_types_breakdown': ride_types_breakdown,
                'earnings_trend': earnings_trend
            },
        }

        return Response(report_data)
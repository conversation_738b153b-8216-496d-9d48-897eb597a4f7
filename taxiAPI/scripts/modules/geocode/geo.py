# taxiAPI/modules/geocode/geo.py
from django.http import JsonResponse
from django.views import View
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
import json

# Geocoding View
@method_decorator(csrf_exempt, name='dispatch')
class GeocodeView(View):
    def post(self, request):
        try:
            # Parse JSON from request body
            data = json.loads(request.body)
            lat = data.get('latitude')
            lng = data.get('longitude')

            # Validate input
            if lat is None or lng is None:
                return JsonResponse({'error': 'Missing latitude or longitude'}, status=400)

            # Mock address for testing
            address = f"Mock Address for {lat}, {lng}"
            return JsonResponse({'address': address})

        except json.JSONDecodeError:
            return JsonResponse({'error': 'Invalid JSON'}, status=400)

# taxiAPI/modules/taxiMath/gasWebCrawler/main.py
import requests
from bs4 import BeautifulSoup
import sqlite3
from datetime import datetime
import logging
import re

# Set up logging
logging.basicConfig(filename='fuel_price_fetch.log', level=logging.INFO)

# Define headers to mimic a browser request
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3'
}

def parse_price(price_str):
    """Parse a price string like 'XCG 2.085' into currency and value."""
    parts = price_str.split()
    currency = parts[0]
    value = float(parts[1])
    return currency, value

def fetch_fuel_prices_curoil():
    """Fetch fuel prices from Curoil's homepage."""
    url = 'https://www.curoil.com'
    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        soup = BeautifulSoup(response.text, 'html.parser')
        table = soup.find('table')  # Assumes the first table contains the prices
        if not table:
            logging.error("No table found on Curoil page")
            return []
        rows = table.find_all('tr')
        
        data = []
        for row in rows[1:]:  # Skip header row
            cells = row.find_all('td')
            if len(cells) < 5:
                continue
            location = cells[0].text.strip()
            effective_date = cells[1].text.strip()
            mogas_95_str = cells[2].text.strip()
            diesel_str = cells[3].text.strip()
            kerosene_str = cells[4].text.strip()
            
            currency, mogas_95 = parse_price(mogas_95_str)
            diesel = float(diesel_str.split()[1])
            kerosene = float(kerosene_str.split()[1])
            
            data.append({
                'source': 'Curoil',
                'location': location,
                'effective_date': effective_date,
                'mogas_95': mogas_95,
                'diesel': diesel,
                'kerosene': kerosene,
                'currency': currency
            })
        return data
    except Exception as e:
        logging.error(f"Error fetching Curoil prices: {e}")
        return []

def fetch_fuel_price_global(fuel_type):
    """Fetch a specific fuel price from GlobalPetrolPrices.com."""
    if fuel_type not in ['gasoline', 'diesel']:
        logging.error(f"Invalid fuel type: {fuel_type}")
        return None
    url = f"https://www.globalpetrolprices.com/Curacao/{fuel_type}_prices/"
    try:
        response = requests.get(url, headers=HEADERS, timeout=10)
        response.raise_for_status()
        soup = BeautifulSoup(response.text, 'html.parser')
        price_pattern = re.compile(r"ANG \d+\.\d+")
        price_match = soup.find(string=price_pattern)
        if price_match:
            price_text = price_match.strip()
            price = float(price_pattern.search(price_text).group().split()[1])
            return price
        else:
            logging.warning(f"{fuel_type.capitalize()} price not found")
            return None
    except Exception as e:
        logging.error(f"Error fetching {fuel_type} price from GlobalPetrolPrices: {e}")
        return None

def fetch_fuel_prices_global():
    """Fetch fuel prices from GlobalPetrolPrices.com for Curacao."""
    gasoline_price = fetch_fuel_price_global('gasoline')
    diesel_price = fetch_fuel_price_global('diesel')
    data = [{
        'source': 'GlobalPetrolPrices',
        'location': 'Curacao',
        'effective_date': None,
        'mogas_95': gasoline_price,
        'diesel': diesel_price,
        'kerosene': None,
        'currency': 'ANG'
    }]
    return data

def setup_database():
    """Set up the SQLite database with a table for fuel prices."""
    with sqlite3.connect('fuel_prices.db') as db:
        db.execute('''CREATE TABLE IF NOT EXISTS fuel_prices (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            source TEXT,
            location TEXT,
            effective_date TEXT,
            mogas_95 REAL,
            diesel REAL,
            kerosene REAL,
            currency TEXT,
            timestamp TEXT
        )''')

def update_database(data):
    """Update the database with new fuel prices if they have changed."""
    with sqlite3.connect('fuel_prices.db') as db:
        timestamp = datetime.now().isoformat()
        for entry in data:
            source = entry['source']
            location = entry['location']
            latest = db.execute(
                'SELECT effective_date, mogas_95, diesel, kerosene FROM fuel_prices '
                'WHERE source = ? AND location = ? ORDER BY timestamp DESC LIMIT 1',
                (source, location)
            ).fetchone()
            
            insert_new = False
            if latest:
                if source == 'Curoil' and latest[0] != entry['effective_date']:
                    insert_new = True
                elif source == 'GlobalPetrolPrices':
                    if (entry['mogas_95'] is not None and latest[1] != entry['mogas_95']) or \
                       (entry['diesel'] is not None and latest[2] != entry['diesel']):
                        insert_new = True
            else:
                insert_new = True
            
            if insert_new:
                db.execute(
                    'INSERT INTO fuel_prices (source, location, effective_date, mogas_95, diesel, kerosene, currency, timestamp) '
                    'VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
                    (source, location, entry['effective_date'], entry['mogas_95'], entry['diesel'], entry['kerosene'], entry['currency'], timestamp)
                )
                logging.info(f"Inserted new record for {source} - {location} at {timestamp}")
        db.commit()

def get_latest_prices(source, location):
    """Retrieve the latest fuel prices for a given source and location."""
    with sqlite3.connect('fuel_prices.db') as db:
        record = db.execute(
            'SELECT mogas_95, diesel, kerosene, currency, effective_date, timestamp FROM fuel_prices '
            'WHERE source = ? AND location = ? ORDER BY timestamp DESC LIMIT 1',
            (source, location)
        ).fetchone()
        if record:
            return {
                'mogas_95': record[0],
                'diesel': record[1],
                'kerosene': record[2],
                'currency': record[3],
                'effective_date': record[4],
                'timestamp': record[5]
            }
    return None

if __name__ == '__main__':
    logging.info(f"Fetching data at {datetime.now().isoformat()}")
    setup_database()
    
    curoil_data = fetch_fuel_prices_curoil()
    global_data = fetch_fuel_prices_global()
    all_data = curoil_data + global_data
    update_database(all_data)
    
    # Display latest prices
    for source, loc in [('Curoil', 'Curaçao'), ('GlobalPetrolPrices', 'Curacao')]:
        prices = get_latest_prices(source, loc)
        if prices:
            print(f"\nLatest prices for {loc} from {source}:")
            print(f"  Mogas 95: {prices['mogas_95']} {prices['currency']}")
            print(f"  Diesel: {prices['diesel']} {prices['currency']}")
            if prices['kerosene']:
                print(f"  Kerosene: {prices['kerosene']} {prices['currency']}")
            print(f"  Effective: {prices['effective_date'] or 'N/A'} (Fetched: {prices['timestamp']})")
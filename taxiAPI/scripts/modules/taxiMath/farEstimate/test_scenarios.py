# taxiAPI/modules/taxiMath/farEstimate/test_scenarios.py
from django.http import JsonResponse
from datetime import datetime
from .farEstimateSinusta import calculate_taxi_fare
def generate_cheat_sheet(request):
    """
    Returns a JSON array of all fare combos, each with:
      { distance, passengers, luggage, isHoliday, isNight, fare }
    """
    # We'll define the same ranges as before:
    distance_range = [1, 2.4, 2.5, 5.3, 5.4, 9.3, 9.4, 11, 11.1, 14, 14.1, 16, 
                      16.1, 18, 18.1, 20, 20.1, 37]
    passenger_range = range(1, 9)  # 1..8
    luggage_range = range(0, 9)    # 0..8
    holiday_options = [False, True]
    # We'll do 2 "times" to represent day vs. night
    time_options = [
        ("12:00:00", False),  # midday -> isNight=False
        ("23:30:00", True),   # late night -> isNight=True
    ]

    results = []

    for dist in distance_range:
        for pass_count in passenger_range:
            for lug_count in luggage_range:
                for holiday_flag in holiday_options:
                    for (time_str, is_night) in time_options:
                        # Calculate the fare using your existing logic:
                        # e.g. reuse calculate_taxi_fare from fareEstimateSinusta.py
                        fare_val = calculate_taxi_fare(
                            distance_km=dist,
                            passenger_count=pass_count,
                            luggage_count=lug_count,
                            trip_time=datetime.strptime(time_str, "%H:%M:%S").time(),
                            is_holiday=holiday_flag,
                        )
                        row = {
                            "distance": dist,
                            "passengers": pass_count,
                            "luggage": lug_count,
                            "isHoliday": holiday_flag,
                            "isNight": is_night,
                            "fare": fare_val,
                        }
                        results.append(row)

    # Return as a JSON array
    return JsonResponse({"results": results}, safe=False)

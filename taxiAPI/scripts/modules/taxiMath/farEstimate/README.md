# Fare Estimation Calculation

This module provides a **fare estimation calculation** based on a reference image and Python scripts. It is part of the `taxiAPI` system, designed to calculate taxi fares efficiently and accurately.

---

## Overview

- **Reference Image**:  
  The fare estimation logic is based on the following image:  
  ![Fare Estimation Reference](farEstimate2025.jpeg)

- **Core Script**:  
  The main script for fare estimation is located at:  
  `taxiAPI/scripts/taxiMath/farEstimateSinusta.py`

- **Test Script**:  
  Unit tests for the fare estimation logic are available at:  
  `taxiAPI/scripts/taxiMath/test_farEstimate.py`

---

## Features

- **Accurate Fare Calculation**:  
  Implements a robust algorithm for calculating taxi fares based on predefined rules.

- **Easy Integration**:  
  Can be integrated seamlessly into the larger `taxiAPI` system.

- **Test Coverage**:  
  Includes a dedicated test script to ensure reliability and correctness.

---

## How to Use

1. **Run the Fare Estimation Script**:
    ```bash
    python3 taxiAPI/scripts/taxiMath/farEstimateSinusta.py
    ```

2. **Run Tests**:
    ```bash
    python3 -m unittest taxiAPI/scripts/taxiMath/test_farEstimate.py
    ```

---

## Notes

- Ensure all dependencies are installed before running the scripts.
- The reference image (`farEstimate2025.jpeg`) provides the basis for the fare calculation logic. Keep it updated if the fare rules change.
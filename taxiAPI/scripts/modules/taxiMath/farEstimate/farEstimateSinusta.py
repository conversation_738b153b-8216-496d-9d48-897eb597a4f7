# taxiAPI/modules/taxiMath/fareEstimate/fareEstimateSinusta.py
"""
Taxi fare calculation module for Curaçao taxi web app.
Calculates fares from/to Hato International Airport based on distance,
passenger count, time of day, holiday status, and luggage.
Designed to work with Google Maps distance estimates.
Enforces America's Curacao (UTC-4) time zone for night surcharge checks.
"""

from datetime import datetime, time
from zoneinfo import ZoneInfo
from django.http import JsonResponse  # Added for API response
import decimal
import logging
from decimal import Decimal
from typing import Union, Optional, Tuple

# Configure logging
logger = logging.getLogger(__name__)

# Set up a precise decimal context for monetary calculations
decimal.getcontext().prec = 10
decimal.getcontext().rounding = decimal.ROUND_HALF_UP

# Distance-based fare table (KM ranges and base fares in USD for 1-4 passengers)
DISTANCE_FARE_TABLE = [
    (Decimal('0'),   Decimal('0.9'), Decimal('20.00')),  # 0 – 1 KM
     (Decimal('1.0'), <PERSON><PERSON><PERSON>('2.4'), <PERSON><PERSON><PERSON>('20.00')),
    (Dec<PERSON><PERSON>('2.5'), <PERSON><PERSON><PERSON>('5.3'), <PERSON><PERSON><PERSON>('25.00')),
    (Decimal('5.4'), Decimal('9.3'), Decimal('30.00')),
    (Decimal('9.4'), Decimal('11.0'), Decimal('35.00')),
    (Decimal('11.1'), Decimal('14.0'), Decimal('40.00')),
    (Decimal('14.1'), Decimal('16.0'), Decimal('45.00')),
    (Decimal('16.1'), Decimal('18.0'), Decimal('50.00')),
    (Decimal('18.1'), Decimal('20.0'), Decimal('55.00')),
    (Decimal('20.1'), Decimal('37.0'), Decimal('70.00')),
]

# Passenger surcharge rules (percentage increase applied to base fare)
PASSENGER_SURCHARGES = {
    5: Decimal('0.25'),  # +25% for 5 passengers
    6: Decimal('0.50'),  # +50% for 6 passengers
    7: Decimal('0.75'),  # +75% for 7 passengers
    8: Decimal('1.00'),  # +100% for 8 passengers
}

# Time and holiday surcharges
NIGHT_SURCHARGE      = Decimal('0.25')  # +25% between 11 PM and 6 AM
HOLIDAY_SURCHARGE    = Decimal('0.25')
EXTRA_LUGGAGE_FEE    = Decimal('5.00')

CURACAO_TZ = ZoneInfo("America/Curacao")

def get_base_fare(distance_km: Union[float, Decimal]) -> Decimal:
    """Returns the base fare in USD based on the distance in kilometers."""
    # Convert to Decimal if passed as float
    if not isinstance(distance_km, Decimal):
        distance_km = Decimal(str(distance_km))
        
    if distance_km < 0:
        raise ValueError("Distance must be non-negative")

    for start_km, end_km, fare in DISTANCE_FARE_TABLE:
        if start_km <= distance_km <= end_km:
            return fare

    # Explicitly price trips > 37 km (future-proof)
    return Decimal('70.00') + Decimal('2.0') * (distance_km - Decimal('37'))
def get_passenger_surcharge(passenger_count: int) -> Decimal:
    """Returns the passenger surcharge factor based on passenger count."""
    if passenger_count in PASSENGER_SURCHARGES:
        return PASSENGER_SURCHARGES[passenger_count]
    if passenger_count > 8:
        return Decimal('1.00')
    return Decimal('0')

def is_night_time(trip_time: time) -> bool:
    """
    Returns True if trip time in Curaçao is between 11 PM (23:00) and 6 AM (06:00).
    
    We interpret the given naive `time` as if it's local time in Curaçao
    for the current date. If you pass a naive time, it will be combined with
    today's date in "America/Curacao" time zone for checking hours.
    """
    # 1. Get the current date in Curacao
    now_in_curacao = datetime.now(CURACAO_TZ)
    # 2. Replace the hour/min/sec with `trip_time`, still pinned to "America/Curacao"
    local_trip_dt = now_in_curacao.replace(
        hour=trip_time.hour,
        minute=trip_time.minute,
        second=trip_time.second,
        microsecond=0
    )
    # 3. Check if local time is >= 23:00 or < 06:00
    hour = local_trip_dt.hour
    return (hour >= 23) or (hour < 6)

def calculate_taxi_fare(
    distance_km: Union[float, Decimal],
    passenger_count: int,
    luggage_count: int,
    trip_time: time,
    is_holiday: bool = False
) -> Decimal:
    """
    Calculates total taxi fare, ensuring the trip_time is considered in
    America's Curacao time zone for night surcharge checks.
    """
    # Validate inputs
    if not isinstance(passenger_count, int):
        raise TypeError("passenger_count must be an integer")
    if passenger_count < 1:
        raise ValueError("passenger_count must be a positive integer (minimum 1)")
    
    if not isinstance(luggage_count, int):
        raise TypeError("luggage_count must be an integer")
    if luggage_count < 0:
        raise ValueError("luggage_count must be a non-negative integer")
    
    if not isinstance(trip_time, time):
        raise TypeError("trip_time must be a datetime.time object")
        
    # Convert to Decimal if passed as float
    if not isinstance(distance_km, Decimal):
        distance_km = Decimal(str(distance_km))
    
    # Get base fare
    base_fare = get_base_fare(distance_km)

    # Passenger surcharge
    passenger_factor = get_passenger_surcharge(passenger_count)
    fare = base_fare * (Decimal('1.0') + passenger_factor)

    # Night Surcharge
    if is_night_time(trip_time):
        fare *= (Decimal('1.0') + NIGHT_SURCHARGE)

    # Holiday Surcharge
    if is_holiday:
        fare *= (Decimal('1.0') + HOLIDAY_SURCHARGE)

    # Luggage fees (1 free per passenger)
    free_luggage = passenger_count
    extra_luggage = max(0, luggage_count - free_luggage)
    luggage_fee = Decimal(str(extra_luggage)) * EXTRA_LUGGAGE_FEE

    total_fare = fare + luggage_fee
    return total_fare.quantize(Decimal('0.01'), rounding=decimal.ROUND_HALF_UP)

# New function to handle API requests
def calculate_taxi_fare_api(request):
    """
    API endpoint to calculate taxi fare based on GET parameters from the frontend.
    Expects: distance_km, passenger_count, luggage_count, trip_time (HH:MM or HH:MM:SS), is_holiday.
    Returns: JSON response with the calculated fare or an error message.
    """
    # Log the API request
    logger.info(f"Fare calculation request received: {request.GET}")
    
    try:
        # Extract parameters from the GET request
        distance_km_str = request.GET.get("distance_km")
        passenger_count_str = request.GET.get("passenger_count")
        luggage_count_str = request.GET.get("luggage_count")
        trip_time_str = request.GET.get("trip_time", "00:00:00")  # Default to midnight if not provided
        is_holiday_str = request.GET.get("is_holiday", "false")
        
        # Validate required parameters are present
        if not all([distance_km_str, passenger_count_str, luggage_count_str]):
            missing = []
            if not distance_km_str: missing.append("distance_km")
            if not passenger_count_str: missing.append("passenger_count")
            if not luggage_count_str: missing.append("luggage_count")
            raise ValueError(f"Missing required parameters: {', '.join(missing)}")
        
        # Convert parameters with proper error handling
        try:
            distance_km = Decimal(distance_km_str)
        except (decimal.InvalidOperation, TypeError):
            raise ValueError(f"Invalid distance value: {distance_km_str}")
            
        try:
            passenger_count = int(passenger_count_str)
        except ValueError:
            raise ValueError(f"Invalid passenger count: {passenger_count_str}")
            
        try:
            luggage_count = int(luggage_count_str)
        except ValueError:
            raise ValueError(f"Invalid luggage count: {luggage_count_str}")
            
        is_holiday = is_holiday_str.lower() == "true"
        
        # Log parsed parameters for troubleshooting
        logger.info(
            f"Parameters parsed: distance={distance_km}km, passengers={passenger_count}, "
            f"luggage={luggage_count}, trip_time={trip_time_str}, holiday={is_holiday}"
        )
        
        # Parse trip_time string with flexible format support
        trip_time = None
        formats_to_try = ["%H:%M:%S", "%H:%M", "%I:%M %p", "%I:%M%p"]
        
        for fmt in formats_to_try:
            try:
                trip_time = datetime.strptime(trip_time_str, fmt).time()
                break
            except ValueError:
                continue
                
        if trip_time is None:
            raise ValueError(
                f"Invalid time format: {trip_time_str}. "
                f"Please use one of the formats: HH:MM:SS, HH:MM, HH:MM AM/PM"
            )

        # Calculate the fare using the existing logic
        fare = calculate_taxi_fare(
            distance_km=distance_km,
            passenger_count=passenger_count,
            luggage_count=luggage_count,
            trip_time=trip_time,
            is_holiday=is_holiday,
        )

        # Log the result
        logger.info(f"Calculated fare: ${fare}")
        
        # Return the result as JSON
        return JsonResponse({"fare": format(fare, '.2f')})
    except ValueError as e:
        logger.warning(f"Value error in fare calculation: {str(e)}")
        return JsonResponse({"error": f"Invalid input: {str(e)}"}, status=400)
    except TypeError as e:
        logger.warning(f"Type error in fare calculation: {str(e)}")
        return JsonResponse({"error": f"Invalid input type: {str(e)}"}, status=400)
    except decimal.DecimalException as e:
        logger.warning(f"Decimal error in fare calculation: {str(e)}")
        return JsonResponse({"error": f"Calculation error: {str(e)}"}, status=400)
    except Exception as e:
        logger.error(f"Unexpected error in fare calculation: {str(e)}", exc_info=True)
        return JsonResponse({"error": f"Server error: {str(e)}"}, status=500)

"""
Integration with Google Maps
To use this with Google Maps:

Call the Google Maps Distance Matrix API to get the distance (in kilometers) between Hato Airport and the destination.
Pass that distance as distance_km to calculate_taxi_fare.
Collect user inputs for passenger_count, luggage_count, trip_time, and is_holiday via your web app’s frontend.
Example (pseudo-code):

import googlemaps
from zoneinfo import ZoneInfo
from datetime import datetime

gmaps = googlemaps.Client(key='YOUR_API_KEY')
distance_result = gmaps.distance_matrix("Hato International Airport", "Blue Bay, Curaçao")
distance_km = distance_result['rows'][0]['elements'][0]['distance']['value'] / 1000

# Example usage:
trip_time = datetime.now(ZoneInfo("America/Curacao")).time()
fare = calculate_taxi_fare(distance_km, 4, 5, trip_time, False)
"""
# taxiAPI/modules/taxiMath/fareEstimate/test_farEstimate.py

from datetime import time
from .farEstimateSinusta import calculate_taxi_fare
from datetime import datetime
from zoneinfo import ZoneInfo

devMode = 0
if devMode == 0:
    curacao_time = datetime.now(ZoneInfo("America/Curacao")) # Automatic
elif devMode == 1:
    curacao_time = time(12,0) #*datetime(12, 0) or time(12,0)   # Noon, so no night surcharge
# print(curacao_time.strftime("%Y-%m-%d %H:%M:%S %Z"))
def main():
    distance_km = 2.0         # 2 KM
    passenger_count = 2
    luggage_count = 2     # 1 luggage
    trip_time = curacao_time
    is_holiday = False

    fare = calculate_taxi_fare(
        distance_km=distance_km,
        passenger_count=passenger_count,
        luggage_count=luggage_count,
        trip_time=trip_time,
        is_holiday=is_holiday
    )
    print(f"Fare for {distance_km} KM, {passenger_count} passengers, {luggage_count} luggage @ {trip_time}: ${fare:.2f}")

if __name__ == "__main__":
    main()

# taxiAPI/modules/taxiMath/fareEstimate/fareEstimateSinusta.py
"""
Taxi fare calculation module for Curaçao taxi web app.
Calculates fares from/to Hato International Airport based on distance,
passenger count, time of day, holiday status, and luggage.
Designed to work with Google Maps distance estimates.
Enforces America's Curacao (UTC-4) time zone for night surcharge checks.
"""

from datetime import datetime, time
from zoneinfo import ZoneInfo

# Distance-based fare table (KM ranges and base fares in USD for 1-4 passengers)
DISTANCE_FARE_TABLE = [
    (1.0, 2.4, 20.00),   # 1 m to 2.4 KM
    (2.5, 5.3, 25.00),
    (5.4, 9.3, 30.00),
    (9.4, 11.0, 35.00),
    (11.1, 14.0, 40.00),
    (14.1, 16.0, 45.00),
    (16.1, 18.0, 50.00),
    (18.1, 20.0, 55.00),
    (20.1, 37.0, 70.00),
]

# Passenger surcharge rules (percentage increase applied to base fare)
PASSENGER_SURCHARGES = {
    5: 0.25,  # +25% for 5 passengers
    6: 0.50,  # +50% for 6 passengers
    7: 0.75,  # +75% for 7 passengers
    8: 1.00,  # +100% for 8 passengers
}

# Time and holiday surcharges
NIGHT_SURCHARGE      = 0.25  # +25% between 11 PM and 6 AM
HOLIDAY_SURCHARGE    = 0.25
EXTRA_LUGGAGE_FEE    = 5.00

CURACAO_TZ = ZoneInfo("America/Curacao")

def get_base_fare(distance_km: float) -> float:
    """Returns the base fare in USD based on the distance in kilometers."""
    for start_km, end_km, fare in DISTANCE_FARE_TABLE:
        if start_km <= distance_km <= end_km:
            return fare
    if distance_km > 37.0:
        return 70.00
    return 20.00

def get_passenger_surcharge(passenger_count: int) -> float:
    """Returns the passenger surcharge factor based on passenger count."""
    if passenger_count in PASSENGER_SURCHARGES:
        return PASSENGER_SURCHARGES[passenger_count]
    if passenger_count > 8:
        return 1.00
    return 0.0

def is_night_time(trip_time: time) -> bool:
    """
    Returns True if trip time in Curaçao is between 11 PM (23:00) and 6 AM (06:00).
    
    We interpret the given naive `time` as if it's local time in Curaçao
    for the current date. If you pass a naive time, it will be combined with
    today's date in "America/Curacao" time zone for checking hours.
    """
    # 1. Get the current date in Curacao
    now_in_curacao = datetime.now(CURACAO_TZ)
    # 2. Replace the hour/min/sec with `trip_time`, still pinned to "America/Curacao"
    local_trip_dt = now_in_curacao.replace(
        hour=trip_time.hour,
        minute=trip_time.minute,
        second=trip_time.second,
        microsecond=0
    )
    # 3. Check if local time is >= 23:00 or < 06:00
    hour = local_trip_dt.hour
    return (hour >= 23) or (hour < 6)

def calculate_taxi_fare(
    distance_km: float,
    passenger_count: int,
    luggage_count: int,
    trip_time: time,
    is_holiday: bool = False
) -> float:
    """
    Calculates total taxi fare, ensuring the trip_time is considered in
    America's Curacao time zone for night surcharge checks.
    """
    base_fare = get_base_fare(distance_km)

    # Passenger surcharge
    passenger_factor = get_passenger_surcharge(passenger_count)
    fare = base_fare * (1.0 + passenger_factor)

    # Night Surcharge
    if is_night_time(trip_time):
        fare *= (1.0 + NIGHT_SURCHARGE)

    # Holiday Surcharge
    if is_holiday:
        fare *= (1.0 + HOLIDAY_SURCHARGE)

    # Luggage fees (1 free per passenger)
    free_luggage = passenger_count
    extra_luggage = max(0, luggage_count - free_luggage)
    luggage_fee = extra_luggage * EXTRA_LUGGAGE_FEE

    total_fare = fare + luggage_fee
    return round(total_fare, 2)

"""
Integration with Google Maps
To use this with Google Maps:

Call the Google Maps Distance Matrix API to get the distance (in kilometers) between Hato Airport and the destination.
Pass that distance as distance_km to calculate_taxi_fare.
Collect user inputs for passenger_count, luggage_count, trip_time, and is_holiday via your web app’s frontend.
Example (pseudo-code):

import googlemaps
from zoneinfo import ZoneInfo
from datetime import datetime

gmaps = googlemaps.Client(key='YOUR_API_KEY')
distance_result = gmaps.distance_matrix("Hato International Airport", "Blue Bay, Curaçao")
distance_km = distance_result['rows'][0]['elements'][0]['distance']['value'] / 1000

# Example usage:
trip_time = datetime.now(ZoneInfo("America/Curacao")).time()
fare = calculate_taxi_fare(distance_km, 4, 5, trip_time, False)
"""
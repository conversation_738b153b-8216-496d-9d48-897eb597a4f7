from datetime import datetime
from django.utils import timezone
from zoneinfo import ZoneInfo
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import AllowAny
from django.contrib.auth.models import User
from taxiAPI.models import RideRequest
from taxiAPI.scripts.modules.taxiMath.farEstimate.farEstimateSinusta import calculate_taxi_fare
# Import RideRequestListView to update its timestamp
from taxiAPI.views import RideRequestListView

class BookRideView(APIView):
    """Handle ride booking requests from both guest and logged-in users."""
    permission_classes = [AllowAny]

    def post(self, request):
        try:
            data = request.data
            required_fields = [
                "pickup_location",
                "dropoff_location",
                "ride_datetime",
                "passenger_count",
                "luggage_count",
                "distance_km",
            ]

            # Check for required fields
            if not all(field in data for field in required_fields):
                return Response(
                    {"error": "Missing required fields"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Handle authenticated vs guest users
            if request.user.is_authenticated:
                user = request.user
                rider_name = user.username
                try:
                    rider_phone = user.profile.phone_number
                    if not rider_phone:
                        return Response(
                            {"error": "User profile must have a phone number"},
                            status=status.HTTP_400_BAD_REQUEST,
                        )
                except AttributeError:
                    return Response(
                        {"error": "User profile not found"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
            else:
                # Guest users must provide name and phone
                if "rider_name" not in data or "rider_phone" not in data:
                    return Response(
                        {"error": "Guest users must provide rider_name and rider_phone"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                rider_name = data["rider_name"]
                rider_phone = data["rider_phone"]
                user = None  # No user account for guests

            # Extract and validate data
            pickup_location = data["pickup_location"]
            dropoff_location = data["dropoff_location"]
            ride_datetime_str = data["ride_datetime"]

            try:
                passenger_count = int(data["passenger_count"])
                if passenger_count <= 0:
                    raise ValueError("Passenger count must be positive")
                luggage_count = int(data["luggage_count"])
                if luggage_count < 0:
                    raise ValueError("Luggage count cannot be negative")
                distance_km = float(data["distance_km"])
                if distance_km <= 0:
                    raise ValueError("Distance must be positive")
            except (ValueError, TypeError) as e:
                return Response(
                    {"error": f"Invalid numeric input: {str(e)}"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            try:
                # Parse the datetime string - this handles ISO format with or without timezone
                if 'Z' in ride_datetime_str:
                    # UTC time with Z suffix
                    ride_datetime = datetime.fromisoformat(ride_datetime_str.replace("Z", "+00:00"))
                elif ('+' in ride_datetime_str or '-' in ride_datetime_str) and 'T' in ride_datetime_str:
                    # ISO format with timezone offset
                    ride_datetime = datetime.fromisoformat(ride_datetime_str)
                else:
                    # Naive datetime - assume it's in America/Curacao timezone
                    naive_dt = datetime.fromisoformat(ride_datetime_str)
                    curacao_tz = ZoneInfo("America/Curacao")
                    ride_datetime = naive_dt.replace(tzinfo=curacao_tz)
                
                # Store as UTC in the database
                # Use standard UTC timezone from zoneinfo instead of django.utils.timezone.utc
                utc_zone = ZoneInfo('UTC')
                ride_datetime = ride_datetime.astimezone(utc_zone)
                
                print(f"Original datetime string: {ride_datetime_str}")
                print(f"Parsed and converted to UTC: {ride_datetime}")
            except ValueError as e:
                print(f"Datetime parsing error: {str(e)}")
                return Response(
                    {"error": "Invalid datetime format"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            trip_time = ride_datetime.time()
            is_holiday = False  # TODO: Implement proper holiday detection

            # Calculate fare (assumes calculate_taxi_fare is defined elsewhere)
            fare = calculate_taxi_fare(
                distance_km=distance_km,
                passenger_count=passenger_count,
                luggage_count=luggage_count,
                trip_time=trip_time,
                is_holiday=is_holiday,
            )

            # Create and save the ride request
            ride_request = RideRequest(
                pickup_location=pickup_location,
                dropoff_location=dropoff_location,
                ride_datetime=ride_datetime,
                passenger_count=passenger_count,
                luggage_count=luggage_count,
                rider_name=rider_name,
                rider_phone=rider_phone,
                calculated_fare=fare,
                distance_km=distance_km,
                status="Pending",
                rider=user,  # None for guests
            )
            ride_request.save()

            # Update the timestamp in RideRequestListView to ensure caching system 
            # is aware of the new pending ride
            RideRequestListView.last_pending_update = timezone.now()
            print(f"Updated RideRequestListView.last_pending_update timestamp to: {RideRequestListView.last_pending_update}")
            
            # Also invalidate the cache
            RideRequestListView.pending_rides_cache = None
            print("Invalidated RideRequestListView pending_rides_cache")

            return Response(
                {
                    "message": "Ride booked successfully",
                    "ride_id": ride_request.id,
                    "tracking_id": ride_request.tracking_id,  # Include tracking ID for guest users
                    "calculated_fare": float(ride_request.calculated_fare),
                },
                status=status.HTTP_201_CREATED,
            )

        except (ValidationError, IntegrityError) as e:
            return Response(
                {"error": "Data validation error", "details": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            # Log the full exception for debugging
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Unexpected error in BookRideView: {str(e)}", exc_info=True)
            return Response(
                {"error": "An unexpected error occurred"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

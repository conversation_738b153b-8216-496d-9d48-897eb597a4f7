---
trigger: always_on
---

# Coding Guidelines for Frontend

<coding_guidelines>
- Use functional components with hooks.
- Follow Airbnb React style guide.
- Uber is the closest company/website that we are trying to recreate but in our own version.
- Define TypeScript interfaces for component props and state.
- Organize components into folders with component files, styles (.scss), and tests. (e.g., `src/features/featureName/Component.tsx`, `Component.css`, `Component.test.tsx`).
- Use environment variables for configuration settings like API URLs.
- by default my api urls in .env is webname/api/ which means in a post/fetch in frontend you don't have to write /api after the env.
- Implement error boundaries to handle runtime errors.
- Be cautious with third-party libraries; verify reputation and keep updated.
- Ensure all network requests are made over HTTPS.
- Avoid inline scripts to prevent XSS.
- Optimize data fetching/posting for minimal traffic and fast loading.
- For API communication:
  - Use JSON for data exchange.
  - Handle API errors appropriately (check status codes, display error messages).
  - Use authentication tokens (e.g., JWT) for API requests.
  - Use /wip for frontend URLs that don’t exist.
- Add comments or documentation to explain code purpose and functionality.
- Structure frontend in a feature-based manner with components, hooks, and utilities per feature.
</coding_guidelines>

<design_guidelines>
- Always before editing .scss files make sure to review "variables.scss" for the defined variables.
  - Location: frontend/src/styles/variables.scss
- Drivers Dashboard design should always prioritize mobile layout
- Operator Dashboard should always prioritize on Laptop > Computer > Tablet > Mobile  for design
- Rider Dashboard has to be Mobile focussed and Laptop.
- When possible for computer/laptop designs you are allowed to add animations but add them in a copy of the .scss file name with Animation name in front example: animationOperatorDashboard.scss for the file OperatorDashboard.scss this is an example.
</design_guidelines>
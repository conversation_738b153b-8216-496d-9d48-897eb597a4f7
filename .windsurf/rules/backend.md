---
trigger: always_on
---

# Coding Guidelines for Backend

<coding_guidelines>
- Follow Django coding style guide.
- Use class-based views for view logic.
- Implement comprehensive error handling and logging.
- Utilize Django’s built-in authentication and authorization systems.
- Validate and sanitize all user input to prevent injection attacks.
- Store sensitive information in environment variables.
- Leverage Django’s security features like CSRF protection.
- Regularly update dependencies to patch vulnerabilities.
- For API communication:
  - Design APIs following RESTful principles (e.g., meaningful endpoints, HTTP methods).
  - Use JSON for data exchange.
  - Implement robust error handling for API requests (return appropriate status codes and messages).
  - Secure API requests using authentication tokens (e.g., JWT).
- Add comments or documentation to explain code purpose and functionality.
- Organize backend code into multiple Django scripts when needed, each handling specific functionality.
</coding_guidelines>
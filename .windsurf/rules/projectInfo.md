---
trigger: always_on
---

# Project Coding Guidelines

<project_debriefing>
Website/WebApp for a taxi company called Sinusta location Curaçao, they have no internet presence and this is their introduction, they need users/guests to register rides for the operator to assign for the drivers. This project is using Google Maps and Geocode for determining locations and calculating price. 
This project is ran by 1 developer @Author: Hinamizawa, Any help or suggestion for design/functionality for frontend or even backend is acceptable.
</project_debriefing>

<important>
Backend Info:
- We are using Fish Terminal not .zsh/bash/etc
- taxiAPI/scripts, modulerized backend parts (directory for scripts modulerized)
- /taxiAPI/scripts/commands, my python scripts for fixing database.
- taxiAPI/views.py, is usually the big part that has to be modulerize in the future.
Frontend Info:
- Predefined design variables: frontend/src/styles/variables.scss
Project Info:
- Frontend = TypescriptReact && SCSS
- Backend = Django (Python)
- When fetching/posting data between the frontend and backend the codes that are made and used should always keep in mind traffic space and how to keep it as low as possible so we don't burn too much data but also to prevent slow loading speed in the long run.
</important>
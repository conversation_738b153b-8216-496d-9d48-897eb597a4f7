#!/bin/bash

set -e
echo "Stopping Django & React with <PERSON><PERSON>..."
docker-compose down -v

# echo "Running Django migrations..."
# if [ ! -d "venv" ]; then
#   python3 -m venv venv
# fi
# source venv/bin/activate.fish
pip install -r requirements.txt
python manage.py makemigrations && python manage.py migrate
python manage.py collectstatic --noinput
# deactivate

echo "Building Backend & Frontend..."
docker-compose build --no-cache backend frontend  # Force rebuild both

echo "Starting Container"
docker-compose up -d

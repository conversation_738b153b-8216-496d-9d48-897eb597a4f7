# Use an official Node.js image
FROM node:18 AS build

WORKDIR /app
COPY frontend/package.json frontend/package-lock.json ./
RUN npm install
COPY frontend/ ./
RUN npm run build

# Serve the app using Nginx
FROM nginx:latest
COPY --from=build /app/dist/ /usr/share/nginx/html
COPY frontend/nginx.conf /etc/nginx/conf.d/default.conf
RUN find /usr/share/nginx/html -type d -exec chmod 755 {} \; \
    && find /usr/share/nginx/html -type f -exec chmod 644 {} \; \
    && echo "Permissions fixed"
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
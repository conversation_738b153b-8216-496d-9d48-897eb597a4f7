"""
Django settings for taxiWebApp project.

Generated by 'django-admin startproject' using Django 5.1.6.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.1/ref/settings/
"""

import os
from pathlib import Path
from dotenv import load_dotenv
from datetime import timedelta

SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(hours=8),  # Access token lasts 1 hour
    "REFRESH_TOKEN_LIFETIME": timedelta(days=7),  # Refresh token lasts 7 days
}
# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent
dotenv_path = BASE_DIR / "frontend" / ".env"  # ^ Path to the .env file
load_dotenv(dotenv_path)  # ^ Load the environment variables from the .env file


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure-k6r7=58rl*0h_@5@@fk_bsm^^n)#54hu0ybb8w!_plvk+shkh8"
GOOGLE_MAPS_API_KEY = os.getenv("VITE_GOOGLE_MAPS_API_KEY")
# SECURITY WARNING: don't run with debug turned on in production!
devMode = os.getenv("DEBUG_BACKEND")

if devMode == "True":
    devMode = True
    DEBUG = True
elif devMode == "False":
    devMode = False
    DEBUG = False

if devMode == True:
    print(
        f"Debug values found: DEBUG={DEBUG}, devMode={devMode}, GOOGLE_MAPS_API_KEY={GOOGLE_MAPS_API_KEY}"
    )
ALLOWED_HOSTS = [
    "hinamizawa.dev",
    "www.hinamizawa.dev",
    "hinamizawa.app",
    "www.hinamizawa.app",
    "localhost",
    "http://127.0.0.1:8000",  # Localhost for p3manage.py
    "127.0.0.1:8000",
    "127.0.0.1",
]

CSRF_TRUSTED_ORIGINS = [
    "https://hinamizawa.dev",
    "https://www.hinamizawa.dev",
    "https://hinamizawa.app",
    "https://www.hinamizawa.app",
]

CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",  # React Frontend (Prod Dev)
    "http://localhost:5173",  # React Frontend (Local Dev)
    "https://hinamizawa.dev",  # Production Domain
    "https://www.hinamizawa.dev",
    "https://hinamizawa.app",
    "https://www.hinamizawa.app",
]

# ✅ Allow All Methods (GET, POST, OPTIONS, etc.)
CORS_ALLOW_METHODS = ["GET", "POST", "PUT", "DELETE", "OPTIONS"]

# ✅ Allow Specific Headers
if devMode == True:  #! Issue for false atm need to fix for live
    CORS_ALLOW_HEADERS = ["*"]

elif devMode == False:
    CORS_ALLOW_HEADERS = [
        "Accept",
        "Authorization",
        "Content-Type",
        "X-CSRFToken",
        "X-Requested-With",
        "Authorization",
        "Content-Type",
    ]

# ✅ Allow Cookies & Authentication
CORS_ALLOW_CREDENTIALS = True

# Application definition
INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "taxiAPI",  # Custom app
    "corsheaders",  # For connecting React to Backend
    "rest_framework",
]
REST_FRAMEWORK = {
    "DEFAULT_AUTHENTICATION_CLASSES": (
        "rest_framework_simplejwt.authentication.JWTAuthentication",
    ),
}

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",  # Add this line
    "django.contrib.sessions.middleware.SessionMiddleware",
    "corsheaders.middleware.CorsMiddleware",  # ✅ Add this before CommonMiddleware
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

ROOT_URLCONF = "taxiWebApp.urls"


TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "taxiWebApp.wsgi.application"


# Database
# https://docs.djangoproject.com/en/5.1/ref/settings/#databases
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.sqlite3",
        "NAME": BASE_DIR / "db.sqlite3",
    }
}


# Password validation
# https://docs.djangoproject.com/en/5.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.1/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.1/howto/static-files/

# Static files (CSS, JavaScript, Images)
STATIC_URL = "/static/"  # This is the public URL used by Django
STATIC_ROOT = os.path.join(
    BASE_DIR, "staticfiles"
)  # Directory where collectstatic places files

# Ensure Django finds static files inside Docker
if os.getenv("DOCKERIZED"):  # Inside Docker
    STATICFILES_DIRS = [
        os.path.join(BASE_DIR, "static")
    ]  # Use "static" for local development
else:  # Production or outside Docker
    STATICFILES_DIRS = []  # Avoid conflicts when collectstatic is used
# https://docs.djangoproject.com/en/5.1/ref/settings/#default-auto-field
# Add both the Django-specific and Webpack-specific static folders
DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

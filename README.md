# Taxi Web App

A web application for taxi services, featuring a React frontend and a Python backend, powered by Dock<PERSON> and exposed via ngrok for development.

## Prerequisites
This project is pre-configured to run with <PERSON><PERSON> and a setup script. You only need the following installed on your system:
- **Docker**: Ensure Docker Desktop (or Docker Engine) is installed and running.
- **Git**: To clone the repository.
- **Python**: Backend
- **NodeJs**: Frontend
- **NGROK**: Optional
Everything else (Python, pip, Node.js, React, ngrok) is handled by <PERSON><PERSON> and the `hina.sh` script.

## How to Use
1. **Clone the Repository**:
```bash
git clone https://github.com/Sinusta/Sinusta-TaxiWebApp
cd projectfoldername
```

2. **Run the Setup Script**:
- Make the script executable (if needed):
```bash
chmod +x hina.sh
```
- Execute the script:
```bash
./hina.sh
```

3. **Access the App**:
- **Local Frontend**: Open `http://localhost:3000` in your browser (React app).
- **Local Backend**: API available at `http://localhost:8000`.
- **Public URL**: Check the terminal output or `http://localhost:4040` (ngrok dashboard) for the ngrok URL (e.g., `https://hinamizawa.app`).

## What Happens?
- The `hina.sh` script uses Docker Compose to:
- Build and run the `backend` (Python) and `frontend` (React) services.
- Start an `ngrok` tunnel to expose the frontend publicly via a custom domain (e.g., `hinamizawa.app`).
- No manual installation of Python, Node.js, or ngrok is required—Docker images handle it all. **doublecheck**

## Notes
- **Ngrok**: A custom domain is pre-configured (e.g., `hinamizawa.app`). Ensure your ngrok authtoken is set in `.env` (see `docker-compose.yml`).
- **Development Only**: This setup is for development and testing, not production.
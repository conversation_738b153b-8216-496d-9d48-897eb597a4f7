services:
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    ports:
      - "8000:8000"
    volumes:
      - ./taxiWebApp:/app/taxiWebApp
      - ./staticfiles:/app/staticfiles  # Static files collected here
      - type: bind
        source: ./db.sqlite3
        target: /app/db.sqlite3
    environment:
      - DEBUG=True
    networks:
      - app-network

  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    ports:
      - "3000:80"
    volumes:
      - ./frontend:/app
      - ./staticfiles:/usr/share/nginx/html/staticfiles  # Mount static files here
    depends_on:
      - backend
    networks:
      - app-network

  ngrok:
    image: ngrok/ngrok
    platform: linux/amd64
    command: http --hostname=hinamizawa.app frontend:80
    environment:
      - NGROK_AUTHTOKEN=*************************************************
    ports:
      - "4040:4040"
    depends_on:
      - frontend
    networks:
      - app-network

networks:
  app-network:
    driver: bridge
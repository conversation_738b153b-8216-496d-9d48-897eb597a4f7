#!/bin/bash

# Define source and destination paths
SRC_DIR="/root/projects/taxiWebApp/staticfiles"
DEST_DIR="/var/www/taxiWebApp/staticfiles"

# Ensure the destination directory exists
echo "Creating destination directory..."
sudo mkdir -p "$DEST_DIR"

# Move static files
echo "Moving static files..."
sudo rsync -av --delete "$SRC_DIR/" "$DEST_DIR/"

# Set correct ownership and permissions
echo "Setting permissions..."
sudo chown -R www-data:www-data "$DEST_DIR"
sudo chmod -R 755 "$DEST_DIR"

# Confirm completion
echo "✅ Static files moved successfully to $DEST_DIR"

#!/bin/bash

set -e  # Exit script immediately on any command failure

pip3 install -r requirements.txt
echo "🚀 Running collectstatic..."
python3 manage.py makemigrations
python3 manage.py migrate
python3 manage.py collectstatic --noinput

# Define source and destination paths
SRC_DIR="$HOME/projects/taxiWebApp/staticfiles"
DEST_DIR="/var/www/taxiWebApp/staticfiles"

# Ensure the destination directory exists
echo "📁 Creating destination directory..."
sudo mkdir -p "$DEST_DIR"

# Move static files
echo "📂 Moving static files..."
sudo rsync -av "$SRC_DIR/" "$DEST_DIR/"

# Set correct ownership and permissions for Django static files
echo "🔧 Setting permissions for static files..."
sudo chown -R www-data:www-data "$DEST_DIR"
sudo chmod -R 755 "$DEST_DIR"

# 🚀 Build React Frontend
echo "⚡ Building React frontend..."
cd "$HOME/projects/taxiWebApp/frontend"
npm install
npm run build

# Move React Build to Nginx Serving Path
FRONTEND_SRC="$HOME/projects/taxiWebApp/frontend/dist/"
FRONTEND_DEST="/var/www/taxiWebApp/frontend/"

echo "📂 Moving React build to $FRONTEND_DEST..."
sudo mkdir -p "$FRONTEND_DEST"
sudo rsync -av "$FRONTEND_SRC" "$FRONTEND_DEST"

# Set correct ownership and permissions for React files
echo "🔧 Setting permissions for React files..."
sudo chown -R www-data:www-data "$FRONTEND_DEST"
sudo chmod -R 755 "$FRONTEND_DEST"

# Restart services
echo "🔄 Restarting Gunicorn..."
sudo systemctl restart gunicorn

echo "🔄 Restarting Nginx..."
sudo systemctl restart nginx

echo "🎉 Deployment complete! Frontend & Backend updated!"

I need to go give an explanation of the website that will later become a web app.
I want to explain what a website is and how it works.
I want to explain the current state of the Sinusta website.

Currently, the Sinusta website is mostly finished when it comes to the frontend; there is a lot of mockup data and designs but for general view for guest it is completed.
Just there is a lot of mockup data that needs to be verified in contact us/ FAQ/ About us etc.


I want to explain each feature in this website and how they will function, that is, what their purpose is:

Tours
Driver WebApp View
 - consists of
    - Dashboard
    - Accepted Rides
    - Ride history
    - Tour history
    - Profile

Tourists/locals can book a ride (fully implemented)
Tourists/locals can book a tour (Not implemented fully)
Can view the estimated price (Based on PDF formula)


The website will have multiple user types:
 - Drivers
 - Riders/Tourist
 - Guest (none logged in users)
 - Dispatchers
 - Staff

Explain these.

What is still needed for it to be operational:

Captcha
Designs/mockup data checkup.
Dashboard for riders and Dispatchers (Not implemented)
A short introduction to the new platform.
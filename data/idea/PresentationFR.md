Sinusta Website Presentation

# Table of Contents

- [Introduction](#introduction)
- [Current State of the Sinusta Website](#current-state-of-the-sinusta-website)
- [Features of the Sinusta Website](#features-of-the-sinusta-website)
  - [Driver WebApp View](#driver-webapp-view)
  - [Booking](#booking)
  - [Estimated Price](#estimated-price)
- [User Types](#user-types)
- [What’s Still Needed for the Website to Be Operational](#whats-still-needed-for-the-website-to-be-operational)
- [Conclusion: Transition to Web App](#conclusion-transition-to-web-app)

# Introduction

Today, I will be presenting an overview of the Sinusta website, which is set to become a fully functional web app. I’ll explain what a website is, how it works, and then dive into the specifics of the Sinusta platform—its current state, features, user types, and what’s still needed to make it operational. Finally, I’ll introduce the new platform and its benefits.

# Current State of the Sinusta Website

The Sinusta website is currently in an advanced stage of development. The frontend—the part users see and interact with—is mostly complete. It features designs and mockup data, giving a clear picture of what the final product will look like. However, some sections, such as Contact Us, FAQ, and About Us, still contain mockup data that needs to be verified and finalized. Overall, the website is ready for guest users to explore, but there are a few areas that require attention before it can be fully operational.
It is possible to hide the booking form etc, so users will only be able to visit the website for now like a normal website.

# Features of the Sinusta Website

The Sinusta website offers several key features designed to enhance the user experience for both tourists and local users. Let’s explore each one:

## Driver WebApp View

This is a dedicated section for drivers, consisting of:

    - Dashboard: Provides an overview of the driver’s activities and upcoming riders request.

    - Accepted Rides: Shows the rides the driver has committed to.

    - Ride History: A record of past rides for reference.

    - Tour History: A record of past tours conducted.

    - Profile: Allows drivers to manage their personal and professional information. (Not implemented)

## Booking

Rides: Users can book rides, and this feature is fully implemented, allowing seamless booking for tourists and locals.
It it assumed currently that all riders will pay within the taxi car or cash.

## Estimated Price

Users can view the estimated cost of rides or tours based on a predefined formula, detailed in a PDF. This transparency helps users plan their trips effectively.

# User Types

The Sinusta website supports multiple user types, each with distinct roles and permissions:

- Drivers: Responsible for providing ride and tour services.

- Riders/Tourists: Users who book rides or tours.

- Guests: Non-logged-in users who can browse the website but cannot book services.

- Dispatchers: Manage ride and tour assignments, ensuring smooth operations. (Not implemented, but will be added)

- Staff: Handle administrative tasks, such as managing content and user accounts.


# What’s Still Needed for the Website to Be Operational

While the Sinusta website is nearing completion, a few critical elements are still required:

- Captcha: To enhance security and prevent automated abuse.

- Designs and Mockup Data Checkup: Final verification of designs and replacement of mockup data in sections like Contact Us, FAQ, and About Us.

- Dashboards for Riders and Dispatchers: These are not yet implemented but are essential for riders to view their bookings/history and for dispatchers to oversee operations and assign manually based on request or phone calls.


# Conclusion: Transition to Web App

The Sinusta website is set to transition into a web app, offering an interactive, app-like experience directly in the browser. This new platform will provide enhanced functionality, such as real-time updates, smoother navigation, and a more engaging user interface. The transition to a web app will make Sinusta more accessible and user-friendly, benefiting both service providers and users.
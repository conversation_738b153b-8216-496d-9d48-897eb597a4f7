server {
    listen 80;
    server_name localhost;

    # Serve React frontend
    root /usr/share/nginx/html;
    index index.html;

    # Serve static files
    location /static/ {
        alias /usr/share/nginx/html/staticfiles/;
        autoindex on;  # Optional, for debugging
        try_files $uri $uri/ =404;  # Return 404 if file not found
    }

    # Proxy /api/ to backend
    location /api/ {
        proxy_pass http://backend:8000/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Proxy /admin/ to backend
    location /admin/ {
        proxy_pass http://backend:8000/admin/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Serve React frontend (catch-all)
    location / {
        try_files $uri /index.html;
    }
}
server {
    listen 80;
    server_name hinamizawa.dev www.hinamizawa.dev;

    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl;
    server_name hinamizawa.dev www.hinamizawa.dev;

    ssl_certificate /etc/letsencrypt/live/hinamizawa.dev/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/hinamizawa.dev/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

    # Serve React Frontend
    root /var/www/taxiWebApp/frontend;
    index index.html;
    location / {
        try_files $uri /index.html;
    }

    # Proxy Django API
    location /api/ {
        proxy_pass http://unix:/var/www/taxiWebApp/gunicorn.sock;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Proxy Django Admin
    location /admin/ {
        proxy_pass http://unix:/var/www/taxiWebApp/gunicorn.sock;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Serve Static Files
    location /static/ {
        alias /var/www/taxiWebApp/staticfiles/;
        autoindex on;
    }
}
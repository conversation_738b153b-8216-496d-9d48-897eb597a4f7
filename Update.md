# Learn DevOps - System Admins (devOps new ERA, System Admins Original Title real deal also):
 - Containerization
 - System Expertise
 - Don't trust serverless, it's not a solution, it's a problem cost more in long run
   - most server will not see more then 1000 users per month.

# Future:
Creating AI is better then developement now a days.
- noCode specialist and backend integrators is the current future.
    - our salary will become lower in the future.
- Learning how to architect systems is more important then coding.
  - Modulerizing is more important now a days for AI debugging.

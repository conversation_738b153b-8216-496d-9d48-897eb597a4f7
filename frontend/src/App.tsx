// frontend/src/App.tsx
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import Home from './pages/Home';
import WIP from './pages/WIP';
import ContactUs from './pages/about/ContactUs';
import AboutUs from './pages/about/AboutUs';
import Login from './components/auth/Login';
import RegisterRider from './components/auth/RegisterRider';
import FarCalc from './components/modules/FarCalc';
import Faq from './pages/about/FAQ';
import OurFleet from './pages/about/OurFleet';
import PrivateTours from './pages/PrivateTours';
import AirportPage from './pages/AirportPage';
import BookARideNow from './pages/BookARideNow';
import BookingConfirmation from './pages/BookingConfirmation';
import FindMyRide from './components/FindMyRide';
import DashboardDriver from './components/auth/pages-driver/DashboardDriver';
import ActiveRides from './components/auth/pages-driver/ActiveRides';
import DriverHistory from './components/auth/pages-driver/DriverHistory';
import DriverReport from './components/auth/pages-driver/DriverReportPage';
import { AuthProvider } from './components/auth/AuthContext';
import MyRideHistory from './components/auth/pages-rider/MyRideHistory';
import OperatorDashboard from './components/auth/pages-operator/OperatorDashboard';
import DashboardPage from './components/auth/pages-operator/DashboardPage';
import AssignedRides from './components/auth/pages-operator/AssignedRides';
import DriversList from './components/auth/pages-operator/DriversList';
import OperatorRidersList from './components/auth/pages-operator/OperatorRidersList';
import OperatorHistoryView from './components/auth/pages-operator/OperatorHistoryView';

const App: React.FC = () => {
  return (
    <BrowserRouter>
    <AuthProvider>
      <Routes>
          {/* Default Pages */}
          <Route path="/" element={<Home />} />
          <Route path="/book-your-ride" element={<BookARideNow />} /> {/* No onLocationUpdate needed */}
          <Route path="/booking-confirmation" element={<BookingConfirmation />} />
          <Route path="/find-my-ride" element={<FindMyRide />} />
          <Route path="/airport" element={<AirportPage />} /> {/* No onLocationUpdate needed */}
          <Route path="/privatetours" element={<PrivateTours />} />
          <Route path="/contactus" element={<ContactUs />} />
          <Route path="/aboutus" element={<AboutUs />} />
          <Route path="/faq" element={<Faq />} />
          <Route path="/ourfleet" element={<OurFleet />} />
          {/* Driver Routes */}
          <Route path="/driverpanel" element={<DashboardDriver />} />
          <Route path="/activerides" element={<ActiveRides />} />
          <Route path='/driverhistory' element={<DriverHistory />} />
          <Route path="/driverreport" element={<DriverReport />} />
          {/* Operator Routes */}
          <Route path="/operator" element={<OperatorDashboard />}>
            <Route index element={<DashboardPage />} />
            <Route path="assigned-rides" element={<AssignedRides />} />
            <Route path="drivers" element={<DriversList />} />
            <Route path="pending-rides" element={<OperatorRidersList />} />
            <Route path="history" element={<OperatorHistoryView />} />
          </Route>
          {/* Rider Routes */}
          <Route path="/myridehistory" element={<MyRideHistory />} />
          {/* Authentication */}
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<RegisterRider />} />

          {/* Custom Pages */}
          <Route path="/fare" element={<FarCalc />} />
          <Route path="/wip" element={<WIP />} />

          {/* Catch-all for 404 */}
          <Route path="*" element={<h1>404 Not Found</h1>} />
      </Routes>
    </AuthProvider>
    </BrowserRouter>
  );
};

export default App;
// src/components/WIP/StarField.ts
export const createStarField = (
    backgroundContainer: HTMLElement,
    midgroundContainer: HTMLElement,
    foregroundContainer: HTMLElement,
    numberOfStars: number,
    shootingStarsEnabled: boolean = true  //! Toggle shooting stars here
  ) => {
    const colors = ['#ffffff', '#e0e0e0', '#d0d0ff', '#ffffe0'];
  
    const createStars = (container: HTMLElement, size: number, speed: string) => {
      for (let i = 0; i < numberOfStars; i++) {
        const star = document.createElement('div');
        star.classList.add('star');
        const color = colors[Math.floor(Math.random() * colors.length)];
        star.style.width = `${size}px`;
        star.style.height = `${size}px`;
        star.style.background = color;
        star.style.top = `${Math.random() * 100}vh`;
        star.style.left = `${Math.random() * 100}vw`;
        star.style.animationDuration = speed;
        star.style.animationDelay = `${Math.random() * 2}s`; // Twinkling delay
        container.appendChild(star);
      }
    };
  
    // Create stars for each layer with different sizes and speeds
    createStars(backgroundContainer, 1, '60s'); // Slow background stars
    createStars(midgroundContainer, 2, '40s');  // Medium midground stars
    createStars(foregroundContainer, 3, '20s'); // Fast foreground stars
  
    // Mouse move handler for foreground layer
    const handleMouseMove = (e: MouseEvent) => {
      const speed = 2;
      const x = (window.innerWidth - e.pageX * speed) / 100;
      const y = (window.innerHeight - e.pageY * speed) / 100;
      foregroundContainer.style.transform = `translateX(${x}px) translateY(${y}px)`;
    };
  
    document.addEventListener('mousemove', handleMouseMove);
  
    // Shooting star functionality (only enabled if shootingStarsEnabled is true)
    let shootingStarInterval: number | undefined = undefined;
    const SHOOTING_STAR_DURATION = 1000;
    const SHOOTING_STAR_INTERVAL = 2500;

    if (shootingStarsEnabled) {
      const createShootingStar = () => {
      const shootingStar = document.createElement('div');
      shootingStar.classList.add('shooting-star');
      shootingStar.style.top = `${Math.random() * 50}vh`; // Start in top half
      shootingStar.style.left = `${Math.random() * 100}vw`;
      
      // Append to .wip-container for proper layering
      const wipContainer = document.querySelector('.wip-container') as HTMLElement;
      if (!wipContainer) {
        console.error('WIP container not found');
        return;
      }
      wipContainer.appendChild(shootingStar);
    
      setTimeout(() => shootingStar.remove(), SHOOTING_STAR_DURATION); // Remove after animation
      };
    
      shootingStarInterval = window.setInterval(createShootingStar, SHOOTING_STAR_INTERVAL); // Every 2.5 seconds
    }
  
    // Return cleanup function
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      if (shootingStarInterval !== undefined) {
        clearInterval(shootingStarInterval);
      }
    };
  };
  
// src/components/WIP/CountDownRedirect.ts
import confetti from 'canvas-confetti';

/**
 * Starts the countdown and updates the countdown state.
 * @param initialCountdown The initial countdown value.
 * @param setCountdown The state setter function for the countdown.
 * @param progressBar The progress bar element to update.
 * @param redirectUrl The URL to redirect to when the countdown reaches zero.
 */
export function startCountdown(
  initialCountdown: number,
  setCountdown: React.Dispatch<React.SetStateAction<number>>,
  progressBar: HTMLElement,
  redirectUrl: string
) {
  let countdown = initialCountdown;

  const interval = setInterval(() => {
    countdown -= 1;
    setCountdown(countdown); // Update the state

    // Update progress bar width
    const progress = ((initialCountdown - countdown) / initialCountdown) * 100;
    progressBar.style.width = `${progress}%`;

    if (countdown === 0) {
      clearInterval(interval);
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 },
      });
      setTimeout(() => {
        window.location.href = redirectUrl;
      }, 500);
    }
  }, 1000);

  return () => clearInterval(interval);
}
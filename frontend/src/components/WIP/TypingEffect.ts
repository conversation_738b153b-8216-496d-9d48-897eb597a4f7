// src/components/WIP/TypingEffect.ts

export const startTypingEffect = (element: HTMLElement, text: string, speed: number) => {
  if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
    console.log('Starting typing effect');
  }
  element.innerHTML = '';
  let index = 0;
  const intervalId = setInterval(() => {
    if (index < text.length) {
      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Appending:', text.charAt(index));
      }
      element.innerHTML += text.charAt(index);
      index++;
    } else {
      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Typing finished');
      }
      clearInterval(intervalId);
    }
  }, speed);
  return () => {
    if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
      console.log('Cleaning up typing effect');
    }
    clearInterval(intervalId);
  };
};
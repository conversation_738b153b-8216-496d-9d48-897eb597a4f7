// frontend/src/components/GoogleMapAirport.tsx
import React, { useEffect, useRef, useState, useCallback } from 'react';

const DEBUG = true;

interface GoogleMapProps {
  onLocationUpdate: (location: { lat: number; lng: number }) => void;
  userOrigin?: string; // Made optional with '?'
  showRoute?: boolean; // Made optional with '?'
}

const AIRPORT_COORDS = { lat: 12.18889, lng: -68.95972 }; // Hato Airport coordinates

const GoogleMap: React.FC<GoogleMapProps> = ({ onLocationUpdate, userOrigin, showRoute }) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const [userLocation, setUserLocation] = useState<{ lat: number; lng: number; accuracy?: number; timestamp?: number } | null>(null);
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const [isApiLoaded, setIsApiLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [locationStatus, setLocationStatus] = useState<'requesting' | 'success' | 'failed' | null>(null);
  const scriptLoadedRef = useRef(false);
  const directionsRendererRef = useRef<google.maps.DirectionsRenderer | null>(null);

  const log = (...args: [string, ...unknown[]]) => {
    if (DEBUG) console.log(...args);
  };

  // --- Refactored Script Loading --- 
  useEffect(() => {
    const MAPS_SCRIPT_ID = 'google-maps-api-script';
    const CALLBACK_NAME = 'googleMapsApiLoadedCallback'; // Unique callback name

    // Check if API is already loaded or script exists
    if (window.google?.maps) {
      log('Google Maps API already available');
      setIsApiLoaded(true);
      return;
    }
    if (document.getElementById(MAPS_SCRIPT_ID)) {
        log('Google Maps script tag already exists.');
        // If script exists but API not loaded, maybe wait or assign callback?
        // For now, assume if script exists, it will load or has failed.
        // Assign the callback in case it loads after this check
        if (!window[CALLBACK_NAME]) {
            window[CALLBACK_NAME] = () => {
                log(`${CALLBACK_NAME} called after script existed.`);
                setIsApiLoaded(true);
                delete window[CALLBACK_NAME]; // Clean up after call
            };
        }
        return;
    }
    
    // Check if loading is already in progress (using a global flag)
    if (window.googleMapsApiLoading) {
        log('Google Maps script is already loading elsewhere.');
        // Optionally, attach callback if needed, similar to above
         if (!window[CALLBACK_NAME]) {
            window[CALLBACK_NAME] = () => {
                log(`${CALLBACK_NAME} called after finding loading flag.`);
                setIsApiLoaded(true);
                delete window[CALLBACK_NAME]; // Clean up after call
            };
        }
        return;
    }

    const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;
    if (!apiKey) {
      log('Google Maps API key not found');
      setError('Google Maps API key is missing.');
      return;
    }

    log('Attempting to load Google Maps script...');
    window.googleMapsApiLoading = true; // Set global flag

    // Define the callback function globally
    window[CALLBACK_NAME] = () => {
      log(`${CALLBACK_NAME} called successfully.`);
      setIsApiLoaded(true);
      window.googleMapsApiLoading = false; // Reset flag
      delete window[CALLBACK_NAME]; // Clean up callback
    };

    const script = document.createElement('script');
    script.id = MAPS_SCRIPT_ID;
    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places&callback=${CALLBACK_NAME}`;
    script.async = true; // Add async attribute
    script.defer = true; // Also add defer for good measure
    
    const timeoutId = setTimeout(() => {
      log('Google Maps script load timed out');
      setError('Google Maps API load timed out. Please refresh.');
      window.googleMapsApiLoading = false; // Reset flag on timeout
       // Clean up callback if timeout occurs before load
      if(window[CALLBACK_NAME]) delete window[CALLBACK_NAME];
    }, 15000); // Increased timeout to 15s

    script.onerror = (error) => {
      log('Google Maps script failed to load:', error);
      setError('Failed to load Google Maps API.');
      clearTimeout(timeoutId);
      window.googleMapsApiLoading = false; // Reset flag on error
       // Clean up callback on error
       if(window[CALLBACK_NAME]) delete window[CALLBACK_NAME];
    };
    
    // No need for script.onload as callback handles success
    
    document.head.appendChild(script);
    scriptLoadedRef.current = true; // Still use ref for component instance awareness if needed

    // Cleanup function
    return () => {
      clearTimeout(timeoutId); // Clear timeout if component unmounts
      log('Cleaning up Google Maps script effect');
      // Don't remove the script itself, as other components might need it.
      // Only remove the callback if it hasn't fired yet and belongs to this instance somehow (tricky)
      // Safer to let the callback clean itself up upon firing.
      // If the component unmounts *before* the callback fires, the global callback might still exist.
      // Consider if a component-specific callback management is needed if this becomes an issue.
    };
  }, []); // Empty dependency array ensures this runs only once on mount
  // --- End Refactored Script Loading ---

  // Request user location
  const requestLocation = useCallback(() => {
    log('Requesting user location at:', new Date().toISOString());
    setLocationStatus('requesting');
    setError(null);

    if (!navigator.geolocation) {
      log('Geolocation not supported');
      setError('Geolocation not supported by your browser.');
      setUserLocation({ lat: 12.1696, lng: -68.9900 });
      setLocationStatus('failed');
      onLocationUpdate({ lat: 12.1696, lng: -68.9900 });
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const location = {
          lat: position.coords.latitude,
          lng: position.coords.longitude,
          accuracy: position.coords.accuracy,
          timestamp: position.timestamp,
        };
        log('Location acquired:', location);
        setUserLocation(location);
        setLocationStatus('success');
        onLocationUpdate({ lat: location.lat, lng: location.lng });
      },
      (error) => {
        log('Location error:', error.message);
        setError(error.message);
        setUserLocation({ lat: 12.1696, lng: -68.9900 });
        setLocationStatus('failed');
        onLocationUpdate({ lat: 12.1696, lng: -68.9900 });
      },
      { timeout: 10000, maximumAge: 0, enableHighAccuracy: false }
    );
  }, [onLocationUpdate]);

  // Initial location request
  useEffect(() => {
    requestLocation();
  }, [requestLocation]);

  // Initialize map when API and location are ready
  useEffect(() => {
    if (!isApiLoaded || !userLocation || !mapRef.current || map || !window.google?.maps) {
      log('Map not ready:', { isApiLoaded, userLocation, mapRef: !!mapRef.current, map: !!map });
      return;
    }

    log('Initializing map with:', userLocation);
    try {
      const mapInstance = new window.google.maps.Map(mapRef.current, {
        center: userLocation,
        zoom: 12,
        mapTypeId: 'roadmap',
      });
      new window.google.maps.Marker({
        position: userLocation,
        map: mapInstance,
        title: 'Your Location',
      });
      new window.google.maps.Marker({
        position: AIRPORT_COORDS,
        map: mapInstance,
        title: 'Curaçao International Airport',
      });
      setMap(mapInstance);

      // Initialize DirectionsRenderer only if we might need it
      directionsRendererRef.current = new window.google.maps.DirectionsRenderer({
        map: mapInstance,
        suppressMarkers: true, // Use our own markers
      });
    } catch (err) {
      log('Map init error:', err);
      setError(`Map initialization failed: ${err instanceof Error ? err.message : String(err)}`);
    }
  }, [isApiLoaded, userLocation, map]);

  // Show route when showRoute is true
  useEffect(() => {
    if (!map || !isApiLoaded || !userOrigin || !showRoute || !window.google?.maps || !directionsRendererRef.current) {
      log('Route not ready:', { map: !!map, isApiLoaded, userOrigin, showRoute });
      return;
    }

    const directionsService = new window.google.maps.DirectionsService();
    const [originLat, originLng] = userOrigin.split(',').map(Number);

    directionsService.route(
      {
        origin: { lat: originLat, lng: originLng },
        destination: AIRPORT_COORDS,
        travelMode: google.maps.TravelMode.DRIVING,
      },
      (result, status) => {
        if (status === google.maps.DirectionsStatus.OK && result) {
          directionsRendererRef.current?.setDirections(result);
          const bounds = new window.google.maps.LatLngBounds();
          bounds.extend({ lat: originLat, lng: originLng });
          bounds.extend(AIRPORT_COORDS);
          map.fitBounds(bounds);
          log('Route rendered successfully');
        } else {
          log('Directions request failed:', status);
          setError('Failed to load directions.');
        }
      }
    );
  }, [map, isApiLoaded, userOrigin, showRoute]);

  return (
    <div className="google-map-container">
      <div ref={mapRef} className="map-canvas" />
      {/* Status messages moved below map div for better layout control */}
      {!isApiLoaded && <div className="map-status map-loading">Loading map...</div>}
      {isApiLoaded && locationStatus === 'requesting' && <div className="map-status map-locating">Getting your location...</div>}
      {isApiLoaded && locationStatus === 'failed' && (
        <div className="map-status map-error">
          {error || 'Using default location (Curaçao)'}
          <button
            onClick={() => requestLocation()}
            className="retry-location-button"
          >
            Retry
          </button>
        </div>
      )}
      {isApiLoaded && locationStatus === 'success' && userLocation && (
        <div className="map-status map-success">Location set: {userLocation.lat.toFixed(6)}, {userLocation.lng.toFixed(6)}</div>
      )}
      {showRoute && <div className="map-status map-info">Route to airport displayed on map</div>}
    </div>
  );
};

export default GoogleMap;
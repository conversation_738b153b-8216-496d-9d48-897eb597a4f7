// frontend/src/components/InstantRideForm.tsx
import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import '../styles/components/maps/InstantRideForm.scss';
import { MdMyLocation } from 'react-icons/md';

interface InstantRideFormProps {
  initialPickupLocation?: string;
}

const InstantRideForm: React.FC<InstantRideFormProps> = ({ initialPickupLocation = '' }) => {
  const navigate = useNavigate();
  const [fullPickupLocation, setFullPickupLocation] = useState<string>(initialPickupLocation);
  const [dropoffLocation, setDropoffLocation] = useState<string>('');
  const [passengerCount, setPassengerCount] = useState<number>(1);
  const [luggageCount, setLuggageCount] = useState<number>(0);
  const [isFetchingLocation, setIsFetchingLocation] = useState<boolean>(false);
  const [rideDateTime, setRideDateTime] = useState<Date>(new Date());
  const [fare, setFare] = useState<number | null>(null);
  const [isFareCalculated, setIsFareCalculated] = useState<boolean>(false);
  const [riderName, setRiderName] = useState<string>('');
  const [riderPhone, setRiderPhone] = useState<string>('');
  const [isCalculating, setIsCalculating] = useState<boolean>(false);
  const [isBooking, setIsBooking] = useState<boolean>(false);
  const [error, setError] = useState<string>('');

  const pickupInputRef = useRef<HTMLInputElement | null>(null);
  const dropoffInputRef = useRef<HTMLInputElement | null>(null);

  // Initialize Google Maps Autocomplete for both pickup and drop-off locations with Curaçao restriction
  useEffect(() => {
    const initializeAutocomplete = (inputRef: React.RefObject<HTMLInputElement | null>, setLocation: (address: string) => void) => {
      if (inputRef.current && window.google && window.google.maps && window.google.maps.places) {
        const autocomplete = new window.google.maps.places.Autocomplete(inputRef.current, {
          types: ['geocode'],
          componentRestrictions: { country: 'CW' }, // Restrict to Curaçao
        });
        autocomplete.addListener('place_changed', () => {
          const place = autocomplete.getPlace();
          if (place && place.formatted_address) {
            setLocation(place.formatted_address);
          }
        });
      }
    };

    if (window.googleMapsLoaded) {
      initializeAutocomplete(pickupInputRef, setFullPickupLocation);
      initializeAutocomplete(dropoffInputRef, setDropoffLocation);
    } else {
      const interval = setInterval(() => {
        if (window.googleMapsLoaded) {
          clearInterval(interval);
          initializeAutocomplete(pickupInputRef, setFullPickupLocation);
          initializeAutocomplete(dropoffInputRef, setDropoffLocation);
        }
      }, 100);
      return () => clearInterval(interval);
    }
  }, []);

  // Get current location using Geolocation API
  const handleGetCurrentLocation = () => {
    if (!navigator.geolocation) {
      alert('Geolocation is not supported by your browser.');
      return;
    }

    setIsFetchingLocation(true);
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords;
        const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;
        if (!apiKey) {
          alert('API key is missing.');
          setIsFetchingLocation(false);
          return;
        }

        fetch(`https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${apiKey}`)
          .then((response) => response.json())
          .then((data) => {
            if (data.status === 'OK' && data.results?.length > 0) {
              setFullPickupLocation(data.results[0].formatted_address);
            } else {
              alert('No address found for your location.');
            }
          })
          .catch(() => alert('Error fetching your location.'))
          .finally(() => setIsFetchingLocation(false));
      },
      (error) => {
        setIsFetchingLocation(false);
        alert(error.code === error.PERMISSION_DENIED
          ? 'Please allow location access.'
          : 'Unable to access your location.');
      },
      { timeout: 10000 }
    );
  };

  // Calculate distance between pickup and drop-off using Google Maps Distance Matrix
  const calculateDistance = async (pickup: string, dropoff: string): Promise<number> => {
    return new Promise((resolve, reject) => {
      const service = new google.maps.DistanceMatrixService();
      service.getDistanceMatrix(
        {
          origins: [pickup],
          destinations: [dropoff],
          travelMode: google.maps.TravelMode.DRIVING,
        },
        (response, status) => {
          if (status === 'OK' && response?.rows[0]?.elements[0]?.status === 'OK') {
            const distance = response.rows[0].elements[0].distance.value / 1000; // Convert meters to kilometers
            resolve(distance);
          } else {
            reject('Unable to calculate distance.');
          }
        }
      );
    });
  };

  // Handle fare calculation (frontend only for display)
  const handleCalculateFare = async () => {
    if (!fullPickupLocation || !dropoffLocation) {
      setError('Please enter both pickup and drop-off locations.');
      return;
    }
    if (!rideDateTime) {
      setError('Please select a date and time.');
      return;
    }
    if (passengerCount < 1 || passengerCount > 8) {
      setError('Passenger count must be between 1 and 8.');
      return;
    }
    if (luggageCount < 0) {
      setError('Luggage count cannot be negative.');
      return;
    }

    setIsCalculating(true);
    setError('');

    try {
      const calculatedDistance = await calculateDistance(fullPickupLocation, dropoffLocation);
      const tripTime = rideDateTime.toLocaleTimeString('en-US', { hour12: false }); // HH:MM:SS

      const params = new URLSearchParams({
        distance_km: calculatedDistance.toString(),
        passenger_count: passengerCount.toString(),
        luggage_count: luggageCount.toString(),
        trip_time: tripTime,
        is_holiday: 'false',
      });

      const apiUrl = import.meta.env.VITE_BACKEND_API_URL;
      const response = await fetch(`${apiUrl}/fareCalc?${params}`);

      if (!response.ok) {
        throw new Error(`Server error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      // Ensure fare is a valid number
      if (!data || typeof data.fare === 'undefined') {
        throw new Error('No fare data received from server');
      }

      const fareValue = parseFloat(data.fare);
      if (isNaN(fareValue) || fareValue < 0) {
        throw new Error('Invalid fare calculation received from server');
      }

      setFare(fareValue); // Store fare for display only
      setIsFareCalculated(true);
    } catch (err) {
      setError('Failed to calculate fare. Please try again.');
    } finally {
      setIsCalculating(false);
    }
  };

  // Clear form data after successful booking
  const clearFormData = () => {
    setFullPickupLocation('');
    setDropoffLocation('');
    setPassengerCount(1);
    setLuggageCount(0);
    setRideDateTime(new Date());
    setFare(null);
    setIsFareCalculated(false);
    setRiderName('');
    setRiderPhone('');
    setError('');
  };

  // Handle booking by sending data to backend, including distance
  const handleBookRide = async () => {
    if (!riderName || !riderPhone) {
      setError('Please enter your full name and phone number.');
      return;
    }
    if (!rideDateTime) {
      setError('Please select a date and time.');
      return;
    }
    // Validate phone number is exactly 7 digits and contains only numbers
    if (riderPhone.length !== 7 || !/^[0-9]{7}$/.test(riderPhone)) {
      setError("Phone number must be exactly 7 digits.");
      return;
    }

    setIsBooking(true);
    setError('');

    try {
      // Recalculate distance with current pickup and dropoff locations
      const calculatedDistance = await calculateDistance(fullPickupLocation, dropoffLocation);

      // Recalculate fare with current inputs
      const tripTime = rideDateTime.toLocaleTimeString('en-US', { hour12: false });
      const params = new URLSearchParams({
        distance_km: calculatedDistance.toString(),
        passenger_count: passengerCount.toString(),
        luggage_count: luggageCount.toString(),
        trip_time: tripTime,
        is_holiday: 'false',
      });

      const apiUrl = import.meta.env.VITE_BACKEND_API_URL;
      const fareResponse = await fetch(`${apiUrl}/fareCalc?${params}`);

      if (!fareResponse.ok) {
        throw new Error(`Fare calculation failed: ${fareResponse.status} ${fareResponse.statusText}`);
      }

      const fareData = await fareResponse.json();

      // Ensure fare is a valid number
      if (!fareData || typeof fareData.fare === 'undefined') {
        throw new Error('No fare data received from server');
      }

      const fareValue = parseFloat(fareData.fare);
      if (isNaN(fareValue) || fareValue < 0) {
        throw new Error('Invalid fare calculation received from server');
      }

      setFare(fareValue); // Update displayed fare

      // Book the ride with the recalculated distance
      const rideDetails = {
        pickup_location: fullPickupLocation,
        dropoff_location: dropoffLocation,
        ride_datetime: rideDateTime.toISOString(),
        passenger_count: passengerCount,
        luggage_count: luggageCount,
        rider_name: riderName,
        rider_phone: riderPhone,
        distance_km: calculatedDistance, // Use the fresh distance
      };

      const bookResponse = await fetch(`${apiUrl}/book-ride/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(rideDetails),
      });

      if (!bookResponse.ok) {
        const errorData = await bookResponse.json();
        throw new Error(`Failed to book ride: ${errorData.error || 'Unknown error'}`);
      }

      const bookData = await bookResponse.json();
      console.log('Ride booked:', bookData);
      
      // Create booking details object for confirmation page
      const bookingDetails = {
        id: bookData.id,
        pickup_location: fullPickupLocation,
        dropoff_location: dropoffLocation,
        ride_datetime: rideDateTime.toISOString(),
        passenger_count: passengerCount,
        luggage_count: luggageCount,
        rider_name: riderName,
        rider_phone: riderPhone,
        distance_km: calculatedDistance,
        calculated_fare: fareValue,
        status: bookData.status || 'pending'
      };

      // Clear form data before navigation
      clearFormData();

      // Navigate to confirmation page with booking details
      navigate('/booking-confirmation', { 
        state: { bookingData: bookingDetails },
        replace: true 
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to book ride. Please try again.';
      setError(errorMessage);
      console.error(err);
    } finally {
      setIsBooking(false);
    }
  };

  return (
    <div className="ride-form">
      <div className="input-group">
        <label htmlFor="pickup">Pickup Location</label>
        <div className="location-input-wrapper">
          <input
            id="pickup"
            type="text"
            placeholder="Enter pickup location"
            value={fullPickupLocation}
            onChange={(e) => setFullPickupLocation(e.target.value)}
            ref={pickupInputRef}
          />
          <input type="hidden" name="pickupHidden" value={fullPickupLocation} />
          <button
            type="button"
            onClick={handleGetCurrentLocation}
            disabled={isFetchingLocation}
            className="location-btn"
            aria-label="Use my location"
          >
            {isFetchingLocation ? <span className="spinner" /> : <MdMyLocation size={20} />}
          </button>
        </div>
      </div>
      <div className="input-group">
        <label htmlFor="dropoff">Drop-off Location</label>
        <input
          id="dropoff"
          type="text"
          placeholder="Enter drop-off location"
          ref={dropoffInputRef}
          value={dropoffLocation}
          onChange={(e) => setDropoffLocation(e.target.value)}
        />
      </div>
      <div className="input-group">
        <label htmlFor="datetime">Date & Time</label>
        <div className="datetime-wrapper">
          <DatePicker
            selected={rideDateTime}
            onChange={(date: Date | null) => date && setRideDateTime(date)}
            showTimeSelect
            timeIntervals={15}
            dateFormat="MMMM d, yyyy h:mm aa"
            placeholderText="Select date and time"
            minDate={new Date()}
            className="datetime-input"
          />
          <button
            type="button"
            onClick={() => setRideDateTime(new Date())}
            className="now-btn"
          >
            Now
          </button>
        </div>
      </div>
      <div className="options-group">
        <div className="input-group">
          <label htmlFor="passengers">Passengers</label>
          <input
            id="passengers"
            type="number"
            min="1"
            max="8"
            value={passengerCount}
            onChange={(e) => setPassengerCount(Math.max(1, Number(e.target.value)))}
          />
        </div>
        <div className="input-group">
          <label htmlFor="luggage">Luggage</label>
          <input
            id="luggage"
            type="number"
            min="0"
            value={luggageCount}
            onChange={(e) => setLuggageCount(Math.max(0, Number(e.target.value)))}
          />
        </div>
      </div>
      <button className="book-button" onClick={handleCalculateFare} disabled={isCalculating}>
        {isCalculating ? 'Calculating...' : 'Calculate Fare'}
      </button>
      {error && <p className="error">{error}</p>}
      {isFareCalculated && (
        <div className="fare-and-booking">
          <p className="fare-display">
            Estimated Fare: ${typeof fare === 'number' && !isNaN(fare) ? fare.toFixed(2) : 'N/A'}
          </p>
          <div className="input-group">
            <label htmlFor="riderName">Rider's Full Name</label>
            <input
              id="riderName"
              type="text"
              placeholder="Enter full name"
              value={riderName}
              onChange={(e) => setRiderName(e.target.value)}
            />
          </div>
          <div className="input-group">
            <label htmlFor="riderPhone">Rider's Phone Number</label>
            <input
              id="riderPhone"
              type="tel"
              placeholder="Enter phone number"
              value={riderPhone}
              onChange={(e) => setRiderPhone(e.target.value)}
              minLength={7}
              maxLength={7}
              pattern="[0-9]{7}"
              title="Phone number must be exactly 7 digits"
            />
          </div>
          <button className="book-button" onClick={handleBookRide} disabled={isBooking}>
            {isBooking ? 'Booking...' : 'Book Now'}
          </button>
        </div>
      )}
    </div>
  );
};

export default InstantRideForm;
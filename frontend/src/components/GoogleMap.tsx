// frontend/src/components/GoogleMap.tsx
import React, { useEffect, useRef, useState, useCallback } from 'react';

const DEBUG = true;

interface GoogleMapProps {
  onLocationUpdate: (location: { lat: number; lng: number }) => void;
}

const GoogleMap: React.FC<GoogleMapProps> = ({ onLocationUpdate }) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const [userLocation, setUserLocation] = useState<{ lat: number; lng: number; accuracy?: number; timestamp?: number } | null>(null);
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const [isApiLoaded, setIsApiLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [locationStatus, setLocationStatus] = useState<'requesting' | 'success' | 'failed' | null>(null);
  const scriptLoadedRef = useRef(false);

  const log = (...args: [string, ...unknown[]]) => {
    if (DEBUG) console.log(...args);
  };

  // Load Google Maps API once
  useEffect(() => {
    if (window.google && window.google.maps) {
      log('Google Maps API already loaded');
      setIsApiLoaded(true);
      return;
    }

    if (scriptLoadedRef.current) {
      log('Script already loading, skipping...');
      return;
    }

    const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;
    if (!apiKey) {
      log('Google Maps API key not found in environment variables');
      setError('Google Maps API key is missing. Please check your .env file.');
      return;
    }

    window.initMap = () => {
      log('initMap called at:', new Date().toISOString());
      setIsApiLoaded(true);
    };

    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places&callback=initMap`;
    script.async = true;
    script.onerror = () => {
      log('Google Maps script failed to load');
      setError('Failed to load Google Maps API');
    };
    document.head.appendChild(script);
    scriptLoadedRef.current = true;

    // Minimal cleanup: only remove callback, not script
    return () => {
      log('Cleaning up initMap');
      if (window.initMap) delete window.initMap;
    };
  }, []);

  // Request user location
  const requestLocation = useCallback(() => {
    log('Requesting user location at:', new Date().toISOString());
    setLocationStatus('requesting');
    setError(null);

    if (!navigator.geolocation) {
      log('Geolocation not supported');
      setError('Geolocation not supported by your browser.');
      setUserLocation({ lat: 12.1696, lng: -68.9900 });
      setLocationStatus('failed');
      onLocationUpdate({ lat: 12.1696, lng: -68.9900 });
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const location = {
          lat: position.coords.latitude,
          lng: position.coords.longitude,
          accuracy: position.coords.accuracy,
          timestamp: position.timestamp,
        };
        log('Location acquired:', location);
        setUserLocation(location);
        setLocationStatus('success');
        onLocationUpdate({ lat: location.lat, lng: location.lng });
      },
      (error) => {
        log('Location error:', error.message);
        setError(error.message);
        setUserLocation({ lat: 12.1696, lng: -68.9900 });
        setLocationStatus('failed');
        onLocationUpdate({ lat: 12.1696, lng: -68.9900 });
      },
      { timeout: 10000, maximumAge: 0, enableHighAccuracy: false }
    );
  }, [onLocationUpdate]);

  // Initial location request
  useEffect(() => {
    requestLocation();
  }, [requestLocation]);

  // Initialize map when API and location are ready
  useEffect(() => {
    if (!isApiLoaded || !userLocation || !mapRef.current || map || !window.google?.maps) {
      log('Map not ready:', { isApiLoaded, userLocation, mapRef: !!mapRef.current, map: !!map });
      return;
    }

    log('Initializing map with:', userLocation);
    try {
      const mapInstance = new window.google.maps.Map(mapRef.current, {
        center: userLocation,
        zoom: 12,
        mapTypeId: 'roadmap',
      });
      new window.google.maps.Marker({
        position: userLocation,
        map: mapInstance,
        title: 'Your Location',
      });
      setMap(mapInstance);
    } catch (err) {
      log('Map init error:', err);
      setError(`Map initialization failed: ${err instanceof Error ? err.message : String(err)}`);
    }
  }, [isApiLoaded, userLocation, map]);

  return (
    <div>
      <div ref={mapRef} style={{ width: '100%', height: '400px', margin: '20px 0', border: '1px solid red' }} />
      {!isApiLoaded && <div>Loading map...</div>}
      {isApiLoaded && locationStatus === 'requesting' && <div>Getting your location...</div>}
      {isApiLoaded && locationStatus === 'failed' && (
        <div>
          {error || 'Using default location (Curaçao)'}
          <button
            onClick={() => requestLocation()}
            style={{ marginLeft: '10px', padding: '5px 10px', background: '#007bff', color: 'white', border: 'none', borderRadius: '5px' }}
          >
            Retry
          </button>
        </div>
      )}
      {isApiLoaded && locationStatus === 'success' && userLocation && (
        <div>Location set: {userLocation.lat.toFixed(6)}, {userLocation.lng.toFixed(6)}</div>
      )}
    </div>
  );
};

export default GoogleMap;
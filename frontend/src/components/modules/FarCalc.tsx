import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import axios from "axios";
import "../../styles/components/modules/farCalc/farCalc.scss";
import "../../styles/components/modules/farCalc/animation.scss";
import "../../styles/components/modules/farCalc/responsiveness.scss";

interface CheatSheetRow {
  distance: number;
  passengers: number;
  luggage: number;
  isHoliday: boolean;
  isNight: boolean;
  fare: number;
}

interface RawCheatSheetRow {
  distance: number | string;
  passengers: number;
  luggage: number;
  isHoliday: boolean;
  isNight: boolean;
  fare: number | string;
}

type SortColumn = "distance" | "fare" | null;
type SortOrder = "asc" | "desc" | null;

const FarCalc: React.FC = () => {
  const [distanceKm, setDistanceKm] = useState<number>(2.0);
  const [passengerCount, setPassengerCount] = useState<number>(2);
  const [luggageCount, setLuggageCount] = useState<number>(2);
  const [fare, setFare] = useState<number | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [showCheatSheet, setShowCheatSheet] = useState(false);
  const [cheatSheetLoading, setCheatSheetLoading] = useState(false);
  const [cheatSheetData, setCheatSheetData] = useState<CheatSheetRow[]>([]);
  const [sortColumn, setSortColumn] = useState<SortColumn>(null);
  const [sortOrder, setSortOrder] = useState<SortOrder>(null);
  const [isMounted, setIsMounted] = useState(false);
  const [showImage, setShowImage] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);

  const apiUrl = import.meta.env.VITE_BACKEND_API_URL as string;

  // Helper function to safely format fare
  const formatFare = (fareValue: number | null): string => {
    if (fareValue === null) return 'N/A';
    if (typeof fareValue === 'number' && !Number.isNaN(fareValue)) {
      return fareValue.toFixed(2);
    }
    return 'N/A';
  };

  // Helper function to safely format distance
  const formatDistance = (distanceValue: any): string => {
    const numericDistance = typeof distanceValue === 'string' ? parseFloat(distanceValue) : distanceValue;
    if (typeof numericDistance === 'number' && !Number.isNaN(numericDistance)) {
      return numericDistance.toFixed(1);
    }
    return 'N/A';
  };

  // Helper function to safely format cheat sheet fare
  const formatCheatSheetFare = (fareValue: any): string => {
    const numericFare = typeof fareValue === 'string' ? parseFloat(fareValue) : fareValue;
    if (typeof numericFare === 'number' && !Number.isNaN(numericFare)) {
      return numericFare.toFixed(2);
    }
    return 'N/A';
  };

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const calculateFare = async () => {
    setLoading(true);
    setError(null);
    const trip_time = new Date().toLocaleTimeString("en-US", {
      hour12: false,
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    });

    try {
      const response = await axios.get(`${apiUrl}/fareCalc/`, {
        params: { distance_km: distanceKm, passenger_count: passengerCount, luggage_count: luggageCount, trip_time, is_holiday: false },
      });

      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Fare API response:', response.data);
        console.log('Fare value:', response.data.fare, 'Type:', typeof response.data.fare);
      }

      // Ensure fare is a valid number
      const fareValue = response.data.fare;
      const numericFare = typeof fareValue === 'string' ? parseFloat(fareValue) : fareValue;

      if (typeof numericFare === 'number' && !Number.isNaN(numericFare)) {
        setFare(numericFare);
      } else {
        throw new Error('Invalid fare value received from server');
      }
    } catch (err) {
      setError("Failed to calculate fare. Please try again.");
      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.error(err);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateCheatSheet = async () => {
    setCheatSheetLoading(true);
    setError(null);
    setShowCheatSheet(false);
    setSortColumn(null);
    setSortOrder(null);

    try {
      const res = await axios.get(`${apiUrl}/fareCalcCheatSheet/`);

      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Cheat sheet API response:', res.data);
        if (res.data.results && res.data.results.length > 0) {
          console.log('Sample cheat sheet row:', res.data.results[0]);
        }
      }

      // Transform the cheat sheet data to ensure numeric values
      const transformedData = res.data.results.map((row: RawCheatSheetRow) => {
        const distance = typeof row.distance === 'string' ? parseFloat(row.distance) : row.distance;
        const fare = typeof row.fare === 'string' ? parseFloat(row.fare) : row.fare;

        return {
          distance: typeof distance === 'number' && !Number.isNaN(distance) ? distance : 0,
          passengers: row.passengers,
          luggage: row.luggage,
          isHoliday: row.isHoliday,
          isNight: row.isNight,
          fare: typeof fare === 'number' && !Number.isNaN(fare) ? fare : 0
        };
      });

      setCheatSheetData(transformedData);
      setShowCheatSheet(true);
    } catch (err) {
      setError("Error generating cheat sheet.");
      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.error(err);
      }
    } finally {
      setCheatSheetLoading(false);
    }
  };

  const handleToggleImage = () => setShowImage((prev) => !prev);
  const openModal = () => setModalOpen(true);
  const closeModal = () => setModalOpen(false);

  const handleSort = (column: SortColumn) => {
    if (!showCheatSheet || cheatSheetData.length === 0) return;
    let newOrder: SortOrder = sortColumn === column && sortOrder === "asc" ? "desc" : "asc";
    setSortColumn(column);
    setSortOrder(newOrder);

    const sorted = [...cheatSheetData].sort((a, b) => {
      if (column === "distance") {
        const aDistance = typeof a.distance === 'string' ? parseFloat(a.distance) : a.distance;
        const bDistance = typeof b.distance === 'string' ? parseFloat(b.distance) : b.distance;
        return newOrder === "asc" ? aDistance - bDistance : bDistance - aDistance;
      }
      if (column === "fare") {
        const aFare = typeof a.fare === 'string' ? parseFloat(a.fare) : a.fare;
        const bFare = typeof b.fare === 'string' ? parseFloat(b.fare) : b.fare;
        return newOrder === "asc" ? aFare - bFare : bFare - aFare;
      }
      return 0;
    });
    setCheatSheetData(sorted);
  };

  const distanceHeaderLabel = () =>
    sortColumn === "distance" ? `Distance (km) ${sortOrder === "asc" ? "↑" : "↓"}` : "Distance (km)";
  const fareHeaderLabel = () => (sortColumn === "fare" ? `Fare ${sortOrder === "asc" ? "↑" : "↓"}` : "Fare");

  return (
    <div className={`fare-calculator ${isMounted ? "fade-in" : ""}`}>
      <Link to="/" className="logo-link">
        <img
          src="/eddybossDesign/Sinusta/png/sinusta-high-resolution-logo-transparent.png"
          alt="Sinusta Taxi Logo"
          className="logo"
        />
      </Link>
      <h2>Sinusta Taxi Fare Calculator</h2>

      <div className="input-group">
        <label htmlFor="distanceKm">Distance (km):</label>
        <input
          id="distanceKm"
          type="number"
          step="0.01"
          min="0"
          value={distanceKm}
          onChange={(e) => setDistanceKm(parseFloat(e.target.value))}
        />
      </div>

      <div className="input-group">
        <label htmlFor="passengerCount">Passengers:</label>
        <input
          id="passengerCount"
          type="number"
          step="1"
          min="0"
          value={passengerCount}
          onChange={(e) => setPassengerCount(parseInt(e.target.value, 10))}
        />
      </div>

      <div className="input-group">
        <label htmlFor="luggageCount">Luggage:</label>
        <input
          id="luggageCount"
          type="number"
          step="1"
          min="0"
          value={luggageCount}
          onChange={(e) => setLuggageCount(parseInt(e.target.value, 10))}
        />
      </div>

      <div className="button-group">
        <button onClick={calculateFare} disabled={loading}>
          {loading ? "Calculating..." : "Calculate Fare"}
        </button>
      </div>

      {fare !== null && (
        <p className="result fade-in">
          Estimated Fare: <strong>${formatFare(fare)}</strong>
        </p>
      )}
      {error && <p className="error fade-in">{error}</p>}

      <span className="section-spacer" />

      <div className="button-group">
        <button onClick={handleToggleImage}>
          {showImage ? "Hide Formula" : "Preview Formula"}
        </button>
      </div>

      {showImage && (
        <div className="formula-image slide-in">
          <p className="formula-note">Click the image for a zoomed-in version.</p>
          <img
            src="/farEstimate2025.jpeg"
            alt="Original Fare Formula"
            className="formula-thumbnail"
            onClick={openModal}
          />
          {modalOpen && (
            <div className="image-modal-overlay" onClick={closeModal}>
              <div className="image-modal-content" onClick={(e) => e.stopPropagation()}>
                <img src="/farEstimate2025.jpeg" alt="Full-Size Fare Formula" className="image-modal-img" />
              </div>
            </div>
          )}
        </div>
      )}

      <hr className="section-divider" />

      <div className="button-group">
        <button onClick={handleGenerateCheatSheet} disabled={cheatSheetLoading} className={isMounted ? "slide-in" : ""}>
          {cheatSheetLoading ? "Generating Cheat Sheet..." : "Generate Cheat Sheet"}
        </button>
      </div>

      {showCheatSheet && !cheatSheetLoading && cheatSheetData.length > 0 && (
        <div className="cheat-sheet-wrapper slide-in">
          <p>
            Found <strong>{cheatSheetData.length}</strong> possibilities.
          </p>
          <table className="cheat-sheet-table">
            <thead>
              <tr>
                <th onClick={() => handleSort("distance")}>{distanceHeaderLabel()}</th>
                <th>Passengers</th>
                <th>Luggage</th>
                <th>Holiday?</th>
                <th>Night?</th>
                <th onClick={() => handleSort("fare")}>{fareHeaderLabel()}</th>
              </tr>
            </thead>
            <tbody>
              {cheatSheetData.map((row, idx) => (
                <tr
                  key={idx}
                  className="table-row-hover"
                  style={{ backgroundColor: idx % 2 === 0 ? "#f9f9f9" : "#ffffff" }}
                >
                  <td>{formatDistance(row.distance)}</td>
                  <td>{row.passengers}</td>
                  <td>{row.luggage}</td>
                  <td>{row.isHoliday ? "Yes" : "No"}</td>
                  <td>{row.isNight ? "Yes" : "No"}</td>
                  <td>${formatCheatSheetFare(row.fare)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default FarCalc;
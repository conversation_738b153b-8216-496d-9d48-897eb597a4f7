import React, { useState, useRef, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import "../../styles/components/blades/UserProfileDropdown.scss";
import { useAuth } from "../auth/AuthContext";

const UserProfileDropdown: React.FC = () => {
  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);
  const dropdownMenuRef = useRef<HTMLUListElement>(null); // Add ref for dropdown menu
  const navigate = useNavigate();
  const { username, role, logout } = useAuth();

  // Check dropdown position when it opens
  useEffect(() => {
    if (isUserDropdownOpen && dropdownMenuRef.current) {
      const rect = dropdownMenuRef.current.getBoundingClientRect();
      if (rect.right > window.innerWidth) {
        dropdownMenuRef.current.classList.add("align-left");
      } else {
        dropdownMenuRef.current.classList.remove("align-left");
      }
    }
  }, [isUserDropdownOpen]);

  const handleLogout = () => {
    logout();
    setIsUserDropdownOpen(false);
    navigate("/login");
  };

  type MenuItem = {
    label: string;
    link: string;
    isExternal?: boolean;
  };

  const menuItemsByRole: Record<string, MenuItem[]> = {
    rider: [{ label: "My Rides", link: "/myridehistory" }],
    driver: [
      { label: "My Trips", link: "/driverhistory" },
      { label: "Riders List", link: "/driverpanel" },
    ],
    staff: [
      { label: "Admin View", link: "/wip" },
      { label: "Drivers View", link: "/driverpanel" },
      { label: "Operators View", link: "/operator" },
      { label: "Riders View", link: "/wip" },
      { label: "Backend Panel", link: "/admin", isExternal: true },
    ],
    operator: [
      { label: "Operator Panel", link: "/operator" },
    ],
  };

  const roleMenuItems =
    role ? menuItemsByRole[role as keyof typeof menuItemsByRole] || [] : [];

  return username ? (
    <li
      className={`user-dropdown ${isUserDropdownOpen ? "open" : ""}`}
      onMouseEnter={() => setIsUserDropdownOpen(true)}
      onMouseLeave={() => setIsUserDropdownOpen(false)}
      onClick={() => setIsUserDropdownOpen(!isUserDropdownOpen)}
    >
      <span className="username">
        {username}
        <svg
          width="18"
          height="18"
          viewBox="0 0 24 24"
          fill="currentColor"
          style={{ marginLeft: "5px", verticalAlign: "middle" }}
        >
          <path d="M18 8v3.8l-6 4.6-6-4.6V8l6 4.6 6-4.6Z" />
        </svg>
      </span>
      {isUserDropdownOpen && (
        <ul className="dropdown-menu" ref={dropdownMenuRef}>
          <li className="profile-item">
            <img
              src="https://avatars.githubusercontent.com/u/15048157?v=4"
              alt="Profile Picture"
              className="profile-picture"
            />
            <div className="profile-info">
              <span className="role">Role: {role || "Unknown"}</span>
              <div className="vip-level-container">
                <span className="vip-level">VIP Level: 0</span>
              </div>
            </div>
          </li>
          {roleMenuItems.map((item) => (
            <li key={item.link}>
              {item.isExternal ? (
                <a href={item.link} className="menu-link">
                  {item.label}
                </a>
              ) : (
                <Link to={item.link} className="menu-link">
                  {item.label}
                </Link>
              )}
            </li>
          ))}
          <li><Link to="/wip">Edit Profile</Link></li>
          <li>
            <button onClick={handleLogout} className="logout-btn">
              <span className="logout-span">Log out</span>
            </button>
          </li>
        </ul>
      )}
    </li>
  ) : (
    <>
      <li><Link to="/login">Login</Link></li>
      <li><Link to="/register">Sign up</Link></li>
    </>
  );
};

export default UserProfileDropdown;
// frontend/src/components/blades/navbar.tsx
// frontend/src/components/blades/Navbar.tsx
import React, { useState, useEffect, useRef } from "react";
import { Link } from "react-router-dom";
import UserProfileDropdown from "./UserProfileDropdown";
import "../../styles/components/blades/navbar.scss";
import { useAuth } from "../auth/AuthContext";

const Navbar: React.FC = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isAboutDropdownOpen, setIsAboutDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLLIElement>(null);
  const { username } = useAuth();

  // Debug username updates
  useEffect(() => {
    if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
      console.log("Navbar username updated:", username);
    }
  }, [username]);

  // <PERSON><PERSON> clicks outside the dropdown to close it on mobile
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isMobileMenuOpen &&
        isAboutDropdownOpen &&
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsAboutDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isMobileMenuOpen, isAboutDropdownOpen]);

  const toggleMobileMenu = () => {
    const newState = !isMobileMenuOpen;
    console.log("Hamburger clicked, isMobileMenuOpen:", newState);
    setIsMobileMenuOpen(newState);
    if (!newState) {
      setIsAboutDropdownOpen(false);
    }
  };

  return (
    <nav className="navbar">
      <div className="navbar-container">
        <div className="logo">
          <Link to="/">
            <img src="/logo-no-background.png" alt="Sinusta Taxi Logo" />
          </Link>
        </div>

        <div className="mobile-auth-links">
          {username ? (
            <Link to="/wip">{username}</Link>
          ) : (
            <Link to="/login">Log In/Register</Link>
          )}
        </div>

        <button
          className="hamburger"
          onClick={toggleMobileMenu}
          aria-label="Toggle menu"
        >
          ☰
        </button>

        <div className={`nav-links ${isMobileMenuOpen ? "active" : ""}`}>
          <div className="nav-left">
            <ul className="main-links">
              <li><Link to="/">Home</Link></li>
              <li><Link to="/airport">Airport Transfers</Link></li>
              <li><Link to="/find-my-ride">Track Ride</Link></li>
              <li><Link to="/privatetours">Tours</Link></li>
              <li
                ref={dropdownRef}
                className={`dropdown ${isAboutDropdownOpen ? "open" : ""}`}
                onMouseEnter={() => setIsAboutDropdownOpen(true)}
                onMouseLeave={() => setIsAboutDropdownOpen(false)}
                onClick={() => setIsAboutDropdownOpen(!isAboutDropdownOpen)}
              >
                <Link to="#">
                  About
                  <svg
                    width="18"
                    height="18"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                    style={{ marginLeft: "5px", verticalAlign: "middle" }}
                  >
                    <path d="M18 8v3.8l-6 4.6-6-4.6V8l6 4.6 6-4.6Z" />
                  </svg>
                </Link>
                {isAboutDropdownOpen && (
                  <ul className="dropdown-menu">
                    <li><Link to="/ourfleet">Our Fleet</Link></li>
                    <li><Link to="/aboutus">About Sinusta</Link></li>
                    <li><Link to="/contactus">Contact Us</Link></li>
                    <li><Link to="/faq">FAQ</Link></li>
                    <li><Link to="/wip">Careers</Link></li>
                    <li><Link to="/fare">Calculator</Link></li>
                  </ul>
                )}
              </li>
            </ul>
          </div>

          <div className="nav-right">
            <ul className="utility-links">
              <li>
                <button className="lang-switch">🌍 EN | NL | ES</button>
              </li>
              <UserProfileDropdown />
              <li><Link to="/contactus">Help</Link></li>

            </ul>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
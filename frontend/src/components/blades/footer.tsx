// frontend/src/components/blades/footer.tsx
import React from "react";
import "../../styles/components/blades/footer.scss";

const Footer: React.FC = () => {
  return (
    <footer className="footer">
      <div className="footer-container">
        <div className="footer-section">
          <h3>Contact Us</h3>
          <p>📍 Buenavista, Willemstad, Curaçao</p>
          <p>
            📧 <a href="mailto:<EMAIL>"><EMAIL></a>
          </p>
          <p>
            📞 <a href="tel:+59996733025">+5999 673 3025</a>
          </p>
        </div>

        <div className="footer-section">
          <h3>Follow Us</h3>
          <div className="social-links">
            <a href="https://www.instagram.com/curacaovacationdeal" aria-label="Instagram">
              <i className="fab fa-instagram"></i>
            </a>
            <a href="https://www.facebook.com/Sinusta/" aria-label="Facebook">
              <i className="fab fa-facebook-f"></i>
            </a>
          </div>
        </div>

        <div className="footer-section">
          <h3>Quick Links</h3>
          <p><a href="/ourfleet">Our Fleet</a></p>
          <p><a href="/aboutus">About Sinusta</a></p>
          <p><a href="/contactus">Contact Us</a></p>
          <p><a href="/faq">FAQ</a></p>
        </div>
      </div>
      <div className="footer-bottom">
        <p>© 2025 Sinusta Taxi Curaçao | All rights reserved.</p>
      </div>
    </footer>
  );
};

export default Footer;
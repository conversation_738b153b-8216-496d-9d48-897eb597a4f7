// frontend/src/components/FindMyRide.tsx
import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { <PERSON>a<PERSON><PERSON>ch, <PERSON>a<PERSON><PERSON>ner, FaMapMarkerAlt, FaUser, FaPhone, FaCar, FaClock, FaCalendarAlt, FaIdCard } from 'react-icons/fa';
import axios from 'axios';
import '../styles/components/FindMyRide.scss';

interface RideDetails {
  id: number;
  tracking_id: string;  // Add tracking_id field
  rider_name: string;
  rider_phone: string;  // Add rider_phone field
  pickup_location: string;
  dropoff_location: string;
  ride_datetime: string;
  passenger_count: number;
  luggage_count: number;
  distance_km?: number;
  calculated_fare?: number | null;
  created_at: string;
  status: string;
  driver?: number | null;
  driver_name?: string | null;
  driver_phone?: string | null;
  driver_vehicle?: string | null;
}

const FindMyRide: React.FC = () => {
  const location = useLocation();
  const [searchForm, setSearchForm] = useState({
    trackingId: '',     // Add tracking ID field
    riderName: '',
    riderPhone: '',
    bookingDate: ''
  });
  
  const [rideDetails, setRideDetails] = useState<RideDetails | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasSearched, setHasSearched] = useState(false);

  // Pre-fill form if URL parameters are provided (from booking confirmation)
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const name = urlParams.get('name');
    const phone = urlParams.get('phone');
    const date = urlParams.get('date');

    // Check for tracking ID first (preferred method)
    const trackingId = urlParams.get('tracking_id');

    if (trackingId) {
      setSearchForm({
        trackingId: decodeURIComponent(trackingId),
        riderName: '',
        riderPhone: '',
        bookingDate: ''
      });
    } else if (name && phone && date) {
      setSearchForm({
        trackingId: '',
        riderName: decodeURIComponent(name),
        riderPhone: decodeURIComponent(phone),
        bookingDate: date
      });
      
      // Auto-submit the search if all parameters are present
      setTimeout(() => {
        const submitEvent = new Event('submit') as any;
        submitEvent.preventDefault = () => {};
        handleSubmit(submitEvent);
      }, 500);
    }
  }, [location.search]);

  // Generate guest ID based on the pattern: fullname-date-month-year
  const generateGuestId = (name: string, date: string): string => {
    if (!date) return '';
    
    const dateObj = new Date(date);
    const day = dateObj.getDate().toString().padStart(2, '0');
    const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
    const year = dateObj.getFullYear();
    
    return `${name.replace(/\s+/g, '').toLowerCase()}-${day}-${month}-${year}`;
  };

  // Format date for display
  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Format currency
  const formatCurrency = (amount: number | null | undefined): string => {
    if (amount === null || amount === undefined) return 'N/A';
    return `$${amount.toFixed(2)}`;
  };

  // Get status badge styling
  const getStatusBadge = (status: string) => {
    const statusMap: Record<string, { label: string; className: string }> = {
      pending: { label: 'Pending Assignment', className: 'pending' },
      assigned: { label: 'Driver Assigned', className: 'assigned' },
      pickedup: { label: 'Picked Up', className: 'active' },
      inprogress: { label: 'In Progress', className: 'active' },
      completed: { label: 'Completed', className: 'completed' },
      cancelled: { label: 'Cancelled', className: 'cancelled' },
      rejected: { label: 'Rejected', className: 'rejected' }
    };
    
    const statusInfo = statusMap[status.toLowerCase()] || { label: status, className: 'unknown' };
    
    return (
      <span className={`status-badge ${statusInfo.className}`}>
        {statusInfo.label}
      </span>
    );
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setSearchForm(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear previous results when form changes
    if (rideDetails || error) {
      setRideDetails(null);
      setError(null);
      setHasSearched(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Check if tracking ID is provided (preferred method)
    if (searchForm.trackingId.trim()) {
      // Search by tracking ID - no other fields needed
    } else {
      // Fallback to old search method - require all fields
      if (!searchForm.riderName.trim() || !searchForm.riderPhone.trim() || !searchForm.bookingDate) {
        setError('Please fill in all fields or provide your tracking ID to search for your ride.');
        return;
      }
    }

    setIsLoading(true);
    setError(null);
    setRideDetails(null);
    setHasSearched(true);

    try {
      const apiUrl = import.meta.env.VITE_BACKEND_API_URL;
      if (!apiUrl) {
        throw new Error('Backend API URL not configured');
      }

      // Generate guest ID for the search (for potential future use)
      // const guestId = generateGuestId(searchForm.riderName, searchForm.bookingDate);
      
      // Create search parameters based on available data
      const searchParams = new URLSearchParams();

      if (searchForm.trackingId.trim()) {
        // Search by tracking ID (preferred)
        searchParams.append('tracking_id', searchForm.trackingId.trim());
      } else {
        // Search by rider details (fallback)
        searchParams.append('rider_name', searchForm.riderName.trim());
        searchParams.append('rider_phone', searchForm.riderPhone.trim());
        searchParams.append('booking_date', searchForm.bookingDate);
      }

      const response = await axios.get(`${apiUrl}/api/rides/search/?${searchParams}`);

      // Axios automatically handles status codes and throws for 4xx/5xx
      const data = response.data;
      
      if (data && data.length > 0) {
        // Take the most recent ride if multiple matches
        const ride = data[0];
        setRideDetails(ride);
      } else {
        throw new Error('No ride found with the provided information.');
      }

    } catch (err) {
      console.error('Ride search error:', err);

      if (axios.isAxiosError(err)) {
        if (err.response?.status === 404) {
          setError('No ride found with the provided information. Please check your details and try again.');
        } else if (err.response?.status === 400) {
          setError(err.response.data?.error || 'Invalid search parameters provided.');
        } else {
          setError('Failed to search for your ride. Please try again.');
        }
      } else {
        const errorMessage = err instanceof Error ? err.message : 'Failed to search for ride. Please try again.';
        setError(errorMessage);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="find-my-ride">
      <div className="find-ride-container">
        <div className="find-ride-header">
          <h2>Track Your Ride</h2>
          <p>Enter your booking details to check your ride status</p>
        </div>

        <form onSubmit={handleSubmit} className="ride-search-form">
          {/* Tracking ID Field (Preferred Method) */}
          <div className="form-group tracking-id-group">
            <label htmlFor="trackingId">
              <FaIdCard className="label-icon" />
              Tracking ID (Recommended)
            </label>
            <input
              type="text"
              id="trackingId"
              name="trackingId"
              value={searchForm.trackingId}
              onChange={handleInputChange}
              placeholder="Enter your tracking ID (e.g., 20241216-1234)"
              className="tracking-id-input"
            />
            <small className="field-help">
              Use your tracking ID for faster and more accurate results
            </small>
          </div>

          <div className="form-divider">
            <span>OR</span>
          </div>

          {/* Traditional Search Fields */}
          <div className="form-group">
            <label htmlFor="riderName">Full Name</label>
            <input
              type="text"
              id="riderName"
              name="riderName"
              value={searchForm.riderName}
              onChange={handleInputChange}
              placeholder="Enter your full name as provided during booking"
              required={!searchForm.trackingId.trim()}
            />
          </div>

          <div className="form-group">
            <label htmlFor="riderPhone">Phone Number</label>
            <input
              type="tel"
              id="riderPhone"
              name="riderPhone"
              value={searchForm.riderPhone}
              onChange={handleInputChange}
              placeholder="Enter your phone number"
              required={!searchForm.trackingId.trim()}
            />
          </div>

          <div className="form-group">
            <label htmlFor="bookingDate">Booking Date</label>
            <input
              type="date"
              id="bookingDate"
              name="bookingDate"
              value={searchForm.bookingDate}
              onChange={handleInputChange}
              required={!searchForm.trackingId.trim()}
            />
          </div>

          <button 
            type="submit" 
            className="search-btn"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <FaSpinner className="spinner" />
                Searching...
              </>
            ) : (
              <>
                <FaSearch />
                {searchForm.trackingId.trim() ? 'Track by ID' : 'Find My Ride'}
              </>
            )}
          </button>
        </form>

        {/* Error Message */}
        {error && hasSearched && (
          <div className="error-message">
            <p>{error}</p>
          </div>
        )}

        {/* Ride Details Display */}
        {rideDetails && (
          <div className="ride-details-card">
            <div className="ride-header">
              <div className="ride-id">
                <h3>Ride #{rideDetails.id}</h3>
                {getStatusBadge(rideDetails.status)}
              </div>
              <div className="tracking-info">
                <span className="tracking-id">
                  <FaIdCard />
                  Tracking ID: <code>{rideDetails.tracking_id}</code>
                </span>
              </div>
              <div className="booking-info">
                <span className="booking-date">
                  <FaCalendarAlt />
                  Booked: {formatDate(rideDetails.created_at)}
                </span>
              </div>
            </div>

            <div className="ride-details-content">
              <div className="ride-route">
                <div className="route-point pickup">
                  <FaMapMarkerAlt className="icon pickup-icon" />
                  <div className="location-info">
                    <span className="label">Pickup</span>
                    <span className="address">{rideDetails.pickup_location}</span>
                  </div>
                </div>
                
                <div className="route-line"></div>
                
                <div className="route-point dropoff">
                  <FaMapMarkerAlt className="icon dropoff-icon" />
                  <div className="location-info">
                    <span className="label">Dropoff</span>
                    <span className="address">{rideDetails.dropoff_location}</span>
                  </div>
                </div>
              </div>

              <div className="ride-info-grid">
                <div className="info-item">
                  <FaClock className="icon" />
                  <div className="info-content">
                    <span className="label">Scheduled Time</span>
                    <span className="value">{formatDate(rideDetails.ride_datetime)}</span>
                  </div>
                </div>

                <div className="info-item">
                  <FaUser className="icon" />
                  <div className="info-content">
                    <span className="label">Passengers</span>
                    <span className="value">{rideDetails.passenger_count}</span>
                  </div>
                </div>

                {rideDetails.distance_km && (
                  <div className="info-item">
                    <span className="label">Distance</span>
                    <span className="value">{rideDetails.distance_km.toFixed(1)} km</span>
                  </div>
                )}

                <div className="info-item">
                  <span className="label">Fare</span>
                  <span className="value fare-amount">{formatCurrency(rideDetails.calculated_fare)}</span>
                </div>
              </div>

              {/* Driver Information */}
              {rideDetails.driver_name && (
                <div className="driver-info">
                  <h4>Driver Information</h4>
                  <div className="driver-details">
                    <div className="driver-item">
                      <FaUser className="icon" />
                      <span className="driver-name">{rideDetails.driver_name}</span>
                    </div>
                    {rideDetails.driver_phone && (
                      <div className="driver-item">
                        <FaPhone className="icon" />
                        <a href={`tel:${rideDetails.driver_phone}`} className="driver-phone">
                          {rideDetails.driver_phone}
                        </a>
                      </div>
                    )}
                    {rideDetails.driver_vehicle && (
                      <div className="driver-item">
                        <FaCar className="icon" />
                        <span className="driver-vehicle">{rideDetails.driver_vehicle}</span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Status-specific messages */}
              <div className="status-message">
                {rideDetails.status.toLowerCase() === 'pending' && (
                  <p>Your ride request is being processed. We'll assign a driver shortly.</p>
                )}
                {rideDetails.status.toLowerCase() === 'assigned' && rideDetails.driver_name && (
                  <p>Great! {rideDetails.driver_name} has been assigned to your ride and will contact you soon.</p>
                )}
                {rideDetails.status.toLowerCase() === 'pickedup' && (
                  <p>You've been picked up! Enjoy your ride.</p>
                )}
                {rideDetails.status.toLowerCase() === 'inprogress' && (
                  <p>Your ride is in progress. You'll arrive at your destination soon.</p>
                )}
                {rideDetails.status.toLowerCase() === 'completed' && (
                  <p>Your ride has been completed. Thank you for choosing our service!</p>
                )}
                {rideDetails.status.toLowerCase() === 'cancelled' && (
                  <p>This ride has been cancelled. Please contact us if you need assistance.</p>
                )}
              </div>
            </div>
          </div>
        )}

        {/* No results message */}
        {hasSearched && !rideDetails && !error && !isLoading && (
          <div className="no-results">
            <p>No ride found with the provided information. Please check your details and try again.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default FindMyRide;
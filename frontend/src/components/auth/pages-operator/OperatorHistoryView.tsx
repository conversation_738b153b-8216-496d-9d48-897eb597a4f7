import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../AuthContext';
import axios from 'axios';
import {
  FaSearch, FaFilter, FaEye,
  FaMapMarkerAlt, FaDollarSign, FaUser, FaCar,
  FaChartLine, FaSync, FaExclamationTriangle
} from 'react-icons/fa';
import '../../../styles/components/auth/pages-operator/OperatorHistoryView.scss';

// Types for completed rides
interface CompletedRide {
  id: number;
  rider_name: string;
  rider_phone: string;
  pickup_location: string;
  dropoff_location: string;
  ride_datetime: string;
  completed_at: string;
  calculated_fare: number;
  distance_km: number;
  driver: {
    id: number;
    username: string;
    first_name: string;
    last_name: string;
  } | null; // Driver can be null
  status: string;
}

interface DateFilter {
  startDate: string;
  endDate: string;
}

interface HistoryStats {
  totalRides: number;
  totalRevenue: number;
  totalDistance: number;
  averageFare: number;
  averageDistance: number;
  topDriver: string;
}

const OperatorHistoryView: React.FC = () => {
  const { logout } = useAuth();
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();

  // State management
  const [completedRides, setCompletedRides] = useState<CompletedRide[]>([]);
  const [filteredRides, setFilteredRides] = useState<CompletedRide[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Filter states
  const [searchQuery, setSearchQuery] = useState('');
  const [dateFilter, setDateFilter] = useState<DateFilter>({
    startDate: '',
    endDate: ''
  });
  const [selectedDriver, setSelectedDriver] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  // Stats state
  const [stats, setStats] = useState<HistoryStats>({
    totalRides: 0,
    totalRevenue: 0,
    totalDistance: 0,
    averageFare: 0,
    averageDistance: 0,
    topDriver: ''
  });

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const ridesPerPage = 20;

  // Detail view state
  const [detailRide, setDetailRide] = useState<CompletedRide | null>(null);
  const [showDetails, setShowDetails] = useState<boolean>(false);

  // Fetch completed rides from backend
  const fetchCompletedRides = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        setError('Authentication required');
        logout();
        return;
      }

      const baseUrl = import.meta.env.VITE_BACKEND_API_URL || '';
      const response = await axios.get(`${baseUrl}/ride-requests/?type=completed`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      const rides: CompletedRide[] = response.data;

      // Debug logging (only in development)
      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Fetched completed rides:', rides.length);
        if (rides.length > 0) {
          console.log('Sample ride data:', rides[0]);
          console.log('Sample ride fare:', rides[0].calculated_fare, typeof rides[0].calculated_fare);
          console.log('Sample ride distance:', rides[0].distance_km, typeof rides[0].distance_km);
          console.log('safeNumber(fare):', safeNumber(rides[0].calculated_fare));
          console.log('safeNumber(distance):', safeNumber(rides[0].distance_km));
          console.log('formatCurrency result:', formatCurrency(safeNumber(rides[0].calculated_fare)));
        }
      }

      // Create a shallow copy before sorting to avoid mutating the original Axios response array
      const sortedRides = [...rides].sort((a, b) => new Date(b.completed_at).getTime() - new Date(a.completed_at).getTime());

      setCompletedRides(sortedRides);
      setFilteredRides(sortedRides);
      calculateStats(sortedRides);

    } catch (err) {
      if (axios.isAxiosError(err) && err.response?.status === 401) {
        logout();
      } else {
        setError('Failed to fetch completed rides. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  }, [logout]);

  // Calculate statistics from rides data
  const calculateStats = useCallback((rides: CompletedRide[]) => {
    if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
      console.log('Calculating stats for rides:', rides.length);
    }

    if (rides.length === 0) {
      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('No rides, setting empty stats');
      }
      setStats({
        totalRides: 0,
        totalRevenue: 0,
        totalDistance: 0,
        averageFare: 0,
        averageDistance: 0,
        topDriver: ''
      });
      return;
    }

    const totalRevenue = rides.reduce(
      (sum, ride) => sum + safeNumber(ride.calculated_fare),
      0
    );

    const totalDistance = rides.reduce(
      (sum, ride) => sum + safeNumber(ride.distance_km),
      0
    );

    // Find top driver by number of rides
    const driverCounts: { [key: string]: number } = {};
    rides.forEach(ride => {
      if (ride.driver && ride.driver.id) {
        const driverName = `${ride.driver.first_name || ''} ${ride.driver.last_name || ''}`.trim() || ride.driver.username || 'Unknown Driver';
        driverCounts[driverName] = (driverCounts[driverName] || 0) + 1;
      }
    });

    const topDriver = Object.keys(driverCounts).length > 0
      ? Object.entries(driverCounts).reduce((a, b) =>
          driverCounts[a[0]] > driverCounts[b[0]] ? a : b
        )[0]
      : '';

    // Ensure all values are valid numbers
    const validTotalRevenue = typeof totalRevenue === 'number' && !isNaN(totalRevenue) ? totalRevenue : 0;
    const validTotalDistance = typeof totalDistance === 'number' && !isNaN(totalDistance) ? totalDistance : 0;
    const validAverageFare = rides.length > 0 ? (validTotalRevenue / rides.length) : 0;
    const validAverageDistance = rides.length > 0 ? (validTotalDistance / rides.length) : 0;

    if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
      console.log('Calculated stats:', {
        totalRevenue,
        totalDistance,
        validTotalRevenue,
        validTotalDistance,
        validAverageFare,
        validAverageDistance,
        topDriver
      });
    }

    setStats({
      totalRides: rides.length,
      totalRevenue: validTotalRevenue,
      totalDistance: validTotalDistance,
      averageFare: typeof validAverageFare === 'number' && !isNaN(validAverageFare) ? validAverageFare : 0,
      averageDistance: typeof validAverageDistance === 'number' && !isNaN(validAverageDistance) ? validAverageDistance : 0,
      topDriver
    });
  }, []);

  // Filter rides based on search query, date range, and driver
  const filterRides = useCallback(() => {
    let filtered = [...completedRides];

    // Search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(ride =>
        (ride.rider_name || '').toLowerCase().includes(query) ||
        (ride.rider_phone || '').includes(query) ||
        (ride.pickup_location || '').toLowerCase().includes(query) ||
        (ride.dropoff_location || '').toLowerCase().includes(query) ||
        (ride.driver && (
          `${ride.driver.first_name || ''} ${ride.driver.last_name || ''}`.toLowerCase().includes(query) ||
          (ride.driver.username && ride.driver.username.toLowerCase().includes(query))
        ))
      );
    }

    // Date filter
    if (dateFilter.startDate) {
      filtered = filtered.filter(ride =>
        new Date(ride.completed_at) >= new Date(dateFilter.startDate)
      );
    }
    if (dateFilter.endDate) {
      filtered = filtered.filter(ride =>
        new Date(ride.completed_at) <= new Date(dateFilter.endDate + 'T23:59:59')
      );
    }

    // Driver filter
    if (selectedDriver) {
      filtered = filtered.filter(ride => ride.driver && ride.driver.id.toString() === selectedDriver);
    }

    setFilteredRides(filtered);
    calculateStats(filtered);
    setCurrentPage(1); // Reset to first page when filtering
  }, [completedRides, searchQuery, dateFilter, selectedDriver, calculateStats]);

  // Load data on component mount and handle URL parameters
  useEffect(() => {
    fetchCompletedRides();

    // Check for driver parameter in URL
    const driverParam = searchParams.get('driver');
    if (driverParam) {
      setSelectedDriver(driverParam);
      setShowFilters(true); // Show filters when driver is pre-selected

      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Driver filter set from URL parameter:', driverParam);
      }
    }
  }, [fetchCompletedRides, searchParams]);

  // Apply filters when dependencies change
  useEffect(() => {
    filterRides();
  }, [filterRides]);

  // Format currency
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Safely format numbers
  const safeNumber = (value: any): number => {
    if (value === null || value === undefined) {
      return 0;
    }
    const num = typeof value === 'string' ? parseFloat(value) : value;
    return isNaN(num) ? 0 : num;
  };

  // Handle view details function
  const handleViewDetails = (ride: CompletedRide) => {
    setDetailRide(ride);
    setShowDetails(true);
  };

  // Handle close details modal
  const handleCloseDetails = () => {
    setShowDetails(false);
    // Keep the ride data for a moment to allow for smooth animation
    setTimeout(() => setDetailRide(null), 300);
  };

  // Handle driver filter change and update URL
  const handleDriverFilterChange = (driverId: string) => {
    setSelectedDriver(driverId);

    // Update URL parameters
    const newSearchParams = new URLSearchParams(searchParams);
    if (driverId) {
      newSearchParams.set('driver', driverId);
    } else {
      newSearchParams.delete('driver');
    }
    setSearchParams(newSearchParams);

    if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
      console.log('Driver filter changed:', driverId);
    }
  };

  // Modal ref for click outside handling
  const modalRef = useRef<HTMLDivElement>(null);

  // Handle click outside modal
  const handleClickOutside = useCallback((event: MouseEvent) => {
    if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
      handleCloseDetails();
    }
  }, []);

  // Handle escape key press
  const handleEscapeKey = useCallback((event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      handleCloseDetails();
    }
  }, []);

  // Set up event listeners for modal interactions
  useEffect(() => {
    if (showDetails) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscapeKey);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscapeKey);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [showDetails, handleClickOutside, handleEscapeKey]);

  // Get unique drivers for filter dropdown - optimized O(n) version
  const uniqueDrivers = useMemo(() => {
    // Build a Map of driver id -> driver info in O(n) time
    const driverMap = new Map<number, { id: number; name: string }>();

    completedRides.forEach(ride => {
      if (ride.driver && ride.driver.id && !driverMap.has(ride.driver.id)) {
        const name = `${ride.driver.first_name || ''} ${ride.driver.last_name || ''}`.trim()
          || ride.driver.username
          || 'Unknown Driver';

        driverMap.set(ride.driver.id, {
          id: ride.driver.id,
          name
        });
      }
    });

    // Convert Map values to array - O(k) where k is number of unique drivers
    return Array.from(driverMap.values());
  }, [completedRides]);

  // Pagination logic
  const totalPages = Math.ceil(filteredRides.length / ridesPerPage);
  const startIndex = (currentPage - 1) * ridesPerPage;
  const endIndex = startIndex + ridesPerPage;
  const currentRides = filteredRides.slice(startIndex, endIndex);

  return (
    <div className="operator-history-view">
      {/* Header Section */}
      <div className="history-header">
        <div className="header-content">
          <h1>
            <FaChartLine className="header-icon" />
            Ride History & Analytics
          </h1>
          <p className="header-subtitle">
            View completed rides, analyze performance, and track revenue
          </p>
        </div>
        <div className="header-actions">
          <button
            className="btn btn-secondary"
            onClick={() => setShowFilters(!showFilters)}
          >
            <FaFilter /> Filters
          </button>
          <button
            className="btn btn-primary"
            onClick={fetchCompletedRides}
            disabled={isLoading}
          >
            <FaSync className={isLoading ? 'spinning' : ''} /> Refresh
          </button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon">
            <FaCar />
          </div>
          <div className="stat-content">
            <div className="stat-value">{stats.totalRides}</div>
            <div className="stat-label">Total Rides</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon revenue">
            <FaDollarSign />
          </div>
          <div className="stat-content">
            <div className="stat-value">{formatCurrency(safeNumber(stats.totalRevenue))}</div>
            <div className="stat-label">Total Revenue</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">
            <FaMapMarkerAlt />
          </div>
          <div className="stat-content">
            <div className="stat-value">{safeNumber(stats.totalDistance).toFixed(1)} km</div>
            <div className="stat-label">Total Distance</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">
            <FaDollarSign />
          </div>
          <div className="stat-content">
            <div className="stat-value">{formatCurrency(safeNumber(stats.averageFare))}</div>
            <div className="stat-label">Average Fare</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">
            <FaUser />
          </div>
          <div className="stat-content">
            <div className="stat-value">{stats.topDriver || 'N/A'}</div>
            <div className="stat-label">Top Driver</div>
          </div>
        </div>
      </div>

      {/* Filters Section */}
      {showFilters && (
        <div className="filters-section">
          <div className="filters-grid">
            <div className="filter-group">
              <label htmlFor="search">Search Rides</label>
              <div className="search-input">
                <FaSearch className="search-icon" />
                <input
                  id="search"
                  type="text"
                  placeholder="Search by rider, driver, location..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>

            <div className="filter-group">
              <label htmlFor="startDate">Start Date</label>
              <input
                id="startDate"
                type="date"
                value={dateFilter.startDate}
                onChange={(e) => setDateFilter(prev => ({ ...prev, startDate: e.target.value }))}
              />
            </div>

            <div className="filter-group">
              <label htmlFor="endDate">End Date</label>
              <input
                id="endDate"
                type="date"
                value={dateFilter.endDate}
                onChange={(e) => setDateFilter(prev => ({ ...prev, endDate: e.target.value }))}
              />
            </div>

            <div className="filter-group">
              <label htmlFor="driver">Driver</label>
              <select
                id="driver"
                value={selectedDriver}
                onChange={(e) => handleDriverFilterChange(e.target.value)}
              >
                <option value="">All Drivers</option>
                {uniqueDrivers.map((driver) => (
                  <option key={driver?.id} value={driver?.id}>
                    {driver?.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className="filter-actions">
            <button
              className="btn btn-secondary"
              onClick={() => {
                setSearchQuery('');
                setDateFilter({ startDate: '', endDate: '' });
                setSelectedDriver('');
                // Clear URL parameters
                setSearchParams(new URLSearchParams());
              }}
            >
              Clear Filters
            </button>
          </div>
        </div>
      )}

      {/* Content Section */}
      <div className="content-section">
        {isLoading ? (
          <div className="loading-container">
            <div className="spinner"></div>
            <p>Loading ride history...</p>
          </div>
        ) : error ? (
          <div className="error-container">
            <FaExclamationTriangle className="error-icon" />
            <p>{error}</p>
            <button className="btn btn-primary" onClick={fetchCompletedRides}>
              Try Again
            </button>
          </div>
        ) : filteredRides.length === 0 ? (
          <div className="empty-state">
            <FaCar className="empty-icon" />
            <h3>No completed rides found</h3>
            <p>There are no completed rides matching your current filters.</p>
          </div>
        ) : (
          <>
            {/* Results Summary */}
            <div className="results-summary">
              <p>
                Showing {startIndex + 1}-{Math.min(endIndex, filteredRides.length)} of {filteredRides.length} rides
              </p>
            </div>

            {/* Rides Table */}
            <div className="rides-table-container">
              <table className="rides-table">
                <thead>
                  <tr>
                    <th>Ride ID</th>
                    <th>Rider</th>
                    <th>Driver</th>
                    <th>Route</th>
                    <th>Date & Time</th>
                    <th>Distance</th>
                    <th>Fare</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {currentRides.map((ride) => (
                    <tr key={ride.id}>
                      <td>#{ride.id}</td>
                      <td>
                        <div className="rider-info">
                          <div className="rider-name">{ride.rider_name || 'Unknown Rider'}</div>
                          <div className="rider-phone">{ride.rider_phone || 'No Phone'}</div>
                        </div>
                      </td>
                      <td>
                        <div className="driver-info">
                          <div className="driver-name">
                            {ride.driver
                              ? (`${ride.driver.first_name || ''} ${ride.driver.last_name || ''}`.trim() || ride.driver.username || 'Unknown Driver')
                              : 'No Driver Assigned'
                            }
                          </div>
                        </div>
                      </td>
                      <td>
                        <div className="route-info">
                          <div className="pickup">
                            <FaMapMarkerAlt className="location-icon pickup" />
                            {ride.pickup_location || 'Unknown Pickup'}
                          </div>
                          <div className="dropoff">
                            <FaMapMarkerAlt className="location-icon dropoff" />
                            {ride.dropoff_location || 'Unknown Dropoff'}
                          </div>
                        </div>
                      </td>
                      <td>
                        <div className="datetime-info">
                          <div className="date">{formatDate(ride.completed_at)}</div>
                        </div>
                      </td>
                      <td>
                        <span className="distance">{safeNumber(ride.distance_km).toFixed(1)} km</span>
                      </td>
                      <td>
                        <span className="fare">{formatCurrency(safeNumber(ride.calculated_fare))}</span>
                      </td>
                      <td>
                        <div className="actions">
                          <button
                            className="details-btn"
                            title="View Ride Details"
                            onClick={() => handleViewDetails(ride)}
                            aria-label={`View details for ride #${ride.id}`}
                          >
                            <FaEye className="eye-icon" />
                            <span className="btn-text">Details</span>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="pagination">
                <button
                  className="btn btn-secondary"
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </button>

                <div className="page-numbers">
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                    <button
                      key={page}
                      className={`btn ${page === currentPage ? 'btn-primary' : 'btn-secondary'}`}
                      onClick={() => setCurrentPage(page)}
                    >
                      {page}
                    </button>
                  ))}
                </div>

                <button
                  className="btn btn-secondary"
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                >
                  Next
                </button>
              </div>
            )}
          </>
        )}
      </div>

      {/* Ride Details Modal */}
      {showDetails && detailRide && (
        <div className="modal-overlay" aria-modal="true" role="dialog">
          <div 
            className="modal ride-details-modal" 
            ref={modalRef}
          >
            <div className="modal-header">
              <div className="header-content">
                <div className="ride-id-badge">#{detailRide.id}</div>
                <h3>Ride Details</h3>
                <div className="status-indicator completed">
                  Completed
                </div>
              </div>
              <button
                className="close-btn"
                onClick={handleCloseDetails}
                aria-label="Close details"
              >
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            </div>
            
            <div className="modal-content">
              <div className="modal-sections-container">
                <div className="detail-section">
                  <h4>Ride Information</h4>
                  <div className="detail-item">
                    <span className="label">Status:</span>
                    <span className="value status-badge completed">
                      {detailRide.status || 'Completed'}
                    </span>
                  </div>
                  <div className="detail-item">
                    <span className="label">Completed At:</span>
                    <span className="value">{formatDate(detailRide.completed_at)}</span>
                  </div>
                  <div className="detail-item">
                    <span className="label">Distance:</span>
                    <span className="value">{safeNumber(detailRide.distance_km).toFixed(1)} km</span>
                  </div>
                  <div className="detail-item">
                    <span className="label">Fare:</span>
                    <span className="value fare-value">{formatCurrency(safeNumber(detailRide.calculated_fare))}</span>
                  </div>
                </div>

                <div className="detail-section">
                  <h4>Rider Information</h4>
                  <div className="detail-item">
                    <span className="label">Name:</span>
                    <span className="value">{detailRide.rider_name || 'Unknown'}</span>
                  </div>
                  <div className="detail-item">
                    <span className="label">Phone:</span>
                    <span className="value">
                      {detailRide.rider_phone ? (
                        <a href={`tel:${detailRide.rider_phone}`} className="phone-link">
                          {detailRide.rider_phone}
                        </a>
                      ) : (
                        'No phone number'
                      )}
                    </span>
                  </div>
                </div>

                <div className="detail-section">
                  <h4>Driver Information</h4>
                  <div className="detail-item">
                    <span className="label">Name:</span>
                    <span className="value">
                      {detailRide.driver
                        ? (`${detailRide.driver.first_name || ''} ${detailRide.driver.last_name || ''}`.trim() || detailRide.driver.username || 'Unknown Driver')
                        : 'No Driver Assigned'}
                    </span>
                  </div>
                </div>

                <div className="detail-section">
                  <h4>Location Information</h4>
                  <div className="detail-item">
                    <span className="label">Pickup:</span>
                    <span className="value location-value">{detailRide.pickup_location || 'Unknown'}</span>
                  </div>
                  <div className="detail-item">
                    <span className="label">Dropoff:</span>
                    <span className="value location-value">{detailRide.dropoff_location || 'Unknown'}</span>
                  </div>
                </div>
              </div>
            </div>


          </div>
        </div>
      )}
    </div>
  );
};

export default OperatorHistoryView;
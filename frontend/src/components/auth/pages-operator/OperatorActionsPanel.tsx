// frontend/src/components/auth/pages-operator/OperatorActionsPanel.tsx
import React, { useState } from 'react';
import { 
  FiMapPin, FiNavigation, FiPhone, FiUser, 
  FiClock, FiDollarSign, FiEye, FiRefreshCw, FiX 
} from 'react-icons/fi';
import { <PERSON>aRoute, FaCar } from 'react-icons/fa';
import '../../../styles/components/auth/pages-operator/OperatorActionsPanel.scss';

interface AssignedRide {
  id: string;
  pickup: string;
  dropoff: string;
  distance_km: number;
  calculated_fare: number | null;
  passenger_count: number;
  luggage_count: number;
  rider_name: string;
  rider_phone: string;
  created_at: string;
  ride_datetime: string;
  driver?: {
    id: string;
    name: string;
    phone: string;
    vehicle: string;
  };
  status: 'assigned' | 'picked_up' | 'in_progress' | 'completed' | 'cancelled';
  assignedTime: string;
  estimatedArrival: string;
}

interface OperatorActionsPanelProps {
  ride: AssignedRide;
  onClose: () => void;
  onRefresh: () => void;
}

const OperatorActionsPanel: React.FC<OperatorActionsPanelProps> = ({
  ride,
  onClose,
  onRefresh
}) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'maps' | 'contact'>('overview');

  // Generate Google Maps URLs
  const getPickupMapUrl = () => {
    const encodedAddress = encodeURIComponent(ride.pickup);
    return `https://www.google.com/maps/search/?api=1&query=${encodedAddress}`;
  };

  const getDropoffMapUrl = () => {
    const encodedAddress = encodeURIComponent(ride.dropoff);
    return `https://www.google.com/maps/search/?api=1&query=${encodedAddress}`;
  };

  const getDirectionsUrl = () => {
    const origin = encodeURIComponent(ride.pickup);
    const destination = encodeURIComponent(ride.dropoff);
    return `https://www.google.com/maps/dir/?api=1&origin=${origin}&destination=${destination}`;
  };

const formatTime = (dateString: string) => {
   try {
    if (!dateString) return 'N/A';
     return new Date(dateString).toLocaleString();
  } catch (error) {
    if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
      console.warn('Invalid date format:', dateString, error);
    }
     return dateString;
   }
 };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'assigned': return '#3b82f6';
      case 'in_progress': return '#f59e0b';
      case 'completed': return '#10b981';
      case 'cancelled': return '#ef4444';
      default: return '#6b7280';
    }
  };

  return (
    <div className="operator-actions-panel">
      <div className="panel-header">
        <div className="panel-title">
          <h3>Ride #{ride.id}</h3>
          <span 
            className="status-indicator"
            style={{ backgroundColor: getStatusColor(ride.status) }}
          >
            {ride.status}
          </span>
        </div>
        <div className="panel-actions">
          <button 
            className="btn btn-ghost btn-sm" 
            onClick={onRefresh}
            title="Refresh ride data"
          >
            <FiRefreshCw />
          </button>
          <button 
            className="btn btn-ghost btn-sm" 
            onClick={onClose}
            title="Close panel"
          >
            <FiX />
          </button>
        </div>
      </div>

      <div className="panel-tabs">
        <button 
          className={`tab ${activeTab === 'overview' ? 'active' : ''}`}
          onClick={() => setActiveTab('overview')}
        >
          <FiEye /> Overview
        </button>
        <button 
          className={`tab ${activeTab === 'maps' ? 'active' : ''}`}
          onClick={() => setActiveTab('maps')}
        >
          <FiMapPin /> Maps
        </button>
        <button 
          className={`tab ${activeTab === 'contact' ? 'active' : ''}`}
          onClick={() => setActiveTab('contact')}
        >
          <FiPhone /> Contact
        </button>
      </div>

      <div className="panel-content">
        {activeTab === 'overview' && (
          <div className="overview-tab">
            <div className="info-section">
              <h4>Trip Details</h4>
              <div className="info-item">
                <FiMapPin className="icon pickup" />
                <div className="info-text">
                  <span className="label">Pickup</span>
                  <span className="value">{ride.pickup}</span>
                </div>
              </div>
              <div className="info-item">
                <FiNavigation className="icon dropoff" />
                <div className="info-text">
                  <span className="label">Dropoff</span>
                  <span className="value">{ride.dropoff}</span>
                </div>
              </div>
              <div className="info-item">
                <FaRoute className="icon" />
                <div className="info-text">
                  <span className="label">Distance</span>
                  <span className="value">{ride.distance_km} km</span>
                </div>
              </div>
              <div className="info-item">
                <FiDollarSign className="icon" />
                <div className="info-text">
                  <span className="label">Fare</span>
                  <span className="value">${ride.calculated_fare?.toFixed(2) || 'N/A'}</span>
                </div>
              </div>
            </div>

            <div className="info-section">
              <h4>Timing</h4>
              <div className="info-item">
                <FiClock className="icon" />
                <div className="info-text">
                  <span className="label">Scheduled</span>
                  <span className="value">{formatTime(ride.ride_datetime)}</span>
                </div>
              </div>
              <div className="info-item">
                <FiClock className="icon" />
                <div className="info-text">
                  <span className="label">Assigned</span>
                  <span className="value">{formatTime(ride.assignedTime)}</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'maps' && (
          <div className="maps-tab">
            <div className="map-actions">
<a 
   href={getPickupMapUrl()} 
   target="_blank" 
   rel="noopener noreferrer"
   className="map-button pickup"
  aria-label="View pickup location in Google Maps"
 >
                <FiMapPin />
                <div className="button-text">
                  <span className="title">View Pickup</span>
                  <span className="subtitle">Open in Google Maps</span>
                </div>
              </a>

<a 
                 href={getDropoffMapUrl()} 
                 target="_blank" 
                 rel="noopener noreferrer"
                 className="map-button dropoff"
                aria-label="View dropoff location in Google Maps"
               >
                <FiNavigation />
                <div className="button-text">
                  <span className="title">View Dropoff</span>
                  <span className="subtitle">Open in Google Maps</span>
                </div>
              </a>

              <a 
                href={getDirectionsUrl()} 
                target="_blank" 
                rel="noopener noreferrer"
                className="map-button directions"
              >
                <FaRoute />
                <div className="button-text">
                  <span className="title">Get Directions</span>
                  <span className="subtitle">Full route in Google Maps</span>
                </div>
              </a>
            </div>

            <div className="map-info">
              <p>Click any button above to open the location in Google Maps. This will help you:</p>
              <ul>
                <li>Verify pickup and dropoff locations</li>
                <li>Check traffic conditions</li>
                <li>Estimate travel time</li>
                <li>Provide directions to the driver</li>
              </ul>
            </div>
          </div>
        )}

        {activeTab === 'contact' && (
          <div className="contact-tab">
            <div className="contact-section">
              <h4>Rider Contact</h4>
              <div className="contact-card">
                <div className="contact-avatar">
                  <FiUser />
                </div>
                <div className="contact-info">
                  <span className="name">{ride.rider_name}</span>
                  <a href={`tel:${ride.rider_phone}`} className="phone">
                    <FiPhone /> {ride.rider_phone}
                  </a>
                </div>
              </div>
            </div>

            {ride.driver && (
              <div className="contact-section">
                <h4>Driver Contact</h4>
                <div className="contact-card">
                  <div className="contact-avatar">
                    <FaCar />
                  </div>
                  <div className="contact-info">
                    <span className="name">{ride.driver ? ride.driver.name : 'Not Assigned'}</span>
                    <a href={`tel:${ride.driver ? ride.driver.phone : ''}`} className="phone">
                      <FiPhone /> {ride.driver ? ride.driver.phone : 'Not Assigned'}
                    </a>
                    <span className="vehicle">{ride.driver ? ride.driver.vehicle : 'Not Assigned'}</span>
                  </div>
                </div>
              </div>
            )}

            <div className="quick-actions">
              <h4>Quick Actions</h4>
<button 
                 className="action-button"
                onClick={() => window.open(`tel:${ride.rider_phone}`, '_self')}
              >
                <FiPhone /> Call Rider
              </button>
              {ride.driver && ride.driver.phone && (
                <button 
                  className="action-button"
                  onClick={() => {
                    if (ride.driver && ride.driver.phone) {
                      window.open(`tel:${ride.driver.phone}`, '_self');
                    }
                  }}
                >
                  <FaCar /> Call Driver
                </button>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default OperatorActionsPanel;

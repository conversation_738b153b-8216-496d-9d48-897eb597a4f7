import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from '../AuthContext';
import { Link } from 'react-router-dom';
import {
  FaUserCircle, FaBell, FaCar, FaMapMarkerAlt,
  FaUserClock, FaExclamationTriangle, FaCheck,
  FaArrowRight, FaCalendarCheck, FaClock
} from 'react-icons/fa';
import axios from 'axios';
import { useSilentPolling } from '../../../types/useSilentPolling';
import {
  RideRequest,
  Driver,
  ActiveRide,
  OperationalStats
} from '../../../types/dashboard';
import '../../../styles/components/auth/pages-operator/DashboardPage.scss';
import AssignDriverModal from './AssignDriverModal';
import { TransformedDriver, RawDriverData, getAvailableDrivers } from '../../../types/driver';

const DashboardPage: React.FC = () => {
  const { username, role, logout } = useAuth();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [pendingRides, setPendingRides] = useState<RideRequest[]>([]);
  const [availableDrivers, setAvailableDrivers] = useState<Driver[]>([]);
  const [activeRides, setActiveRides] = useState<ActiveRide[]>([]);
  const [operationalStats, setOperationalStats] = useState<OperationalStats>({
    pendingCount: 0,
    assignedCount: 0,
    availableDrivers: 0,
    busyDrivers: 0,
    todayCompletedRides: 0,
    avgResponseTime: '5.2 min'
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Modal state for assigning drivers
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedRideId, setSelectedRideId] = useState<number | null>(null);
  const [availableDriversList, setAvailableDriversList] = useState<TransformedDriver[]>([]);
  const [selectedDriverId, setSelectedDriverId] = useState<number | null>(null);
  const [isAssigning, setIsAssigning] = useState(false);

  // Ref to track if component is mounted to prevent state updates after unmount
  const isMountedRef = useRef(false);
  
  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 60000);
    return () => clearInterval(timer);
  }, []);

  // Improved error handling helper
  const handleApiError = useCallback((error: unknown, context: string): void => {
    if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
      console.error(`Error in ${context}:`, error);
    }

    if (axios.isAxiosError(error)) {
      if (error.response?.status === 401) {
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.log('Authentication failed, logging out...');
        }
        logout();
        return;
      }

      const errorMessage = error.response?.data?.message || error.message || 'Unknown error occurred';
      if (isMountedRef.current) {
        setError(`${context}: ${errorMessage}`);
      }
    } else {
      if (isMountedRef.current) {
        setError(`${context}: An unexpected error occurred`);
      }
    }
  }, [logout]);

  const fetchDashboardData = useCallback(async (abortSignal?: AbortSignal): Promise<void> => {
    if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
      console.log('fetchDashboardData called, isMountedRef.current:', isMountedRef.current);
    }
    if (!isMountedRef.current) {
      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Component not mounted, skipping fetch');
      }
      return;
    }

    try {
      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Starting dashboard data fetch...');
        console.log('Current loading state:', isLoading);
        console.log('Current error state:', error);
      }
      setIsLoading(true);
      setError(null);

      const token = localStorage.getItem('access_token');
      if (!token) {
        setError('Authentication required');
        logout();
        return;
      }

      const baseUrl = import.meta.env.VITE_BACKEND_API_URL || '';

      // Create AbortController if not provided
      const controller = !abortSignal ? new AbortController() : undefined;
      const signal = abortSignal || controller?.signal;

      // Force fresh data by adding cache-busting headers for initial load
      const headers = {
        Authorization: `Bearer ${token}`,
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      };

      // Add cache-busting timestamp to URLs
      const cacheBuster = `_t=${Date.now()}`;

      // Use Promise.all for parallel requests with cache-busting headers and URLs
      const [pendingResponse, driversResponse, assignedResponse] = await Promise.all([
        axios.get(`${baseUrl}/ride-requests/?type=pending&${cacheBuster}`, {
          headers,
          signal
        }),
        axios.get(`${baseUrl}/drivers/?${cacheBuster}`, {
          headers,
          signal
        }),
        axios.get(`${baseUrl}/assigned-rides/?${cacheBuster}`, {
          headers,
          signal
        })
      ]);

      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('All API responses received successfully');
        console.log('Pending response status:', pendingResponse.status);
        console.log('Drivers response status:', driversResponse.status);
        console.log('Assigned response status:', assignedResponse.status);
      }

      // Only update state if component is still mounted
      if (!isMountedRef.current) return;

      // Process responses with proper type checking
      const pendingRidesData: RideRequest[] = Array.isArray(pendingResponse.data)
        ? pendingResponse.data.slice(0, 5)
        : [];

      const allDrivers: Driver[] = Array.isArray(driversResponse.data)
        ? driversResponse.data
        : [];

      const availableDriversData = allDrivers
        .filter((driver: Driver) => driver.status === 'available')
        .slice(0, 5);

      const activeRidesData: ActiveRide[] = Array.isArray(assignedResponse.data)
        ? assignedResponse.data.slice(0, 5)
        : [];

      // Update state with fetched data
      setPendingRides(pendingRidesData);
      setAvailableDrivers(availableDriversData);
      setActiveRides(activeRidesData);

      // Calculate operational stats with null checks
      const newStats: OperationalStats = {
        pendingCount: pendingResponse.data?.length || 0,
        assignedCount: assignedResponse.data?.length || 0,
        availableDrivers: allDrivers.filter((d: Driver) => d.status === 'available').length,
        busyDrivers: allDrivers.filter((d: Driver) => d.status === 'on_ride').length,
        todayCompletedRides: 0, // TODO: Implement proper API endpoint for completed rides
        avgResponseTime: '5.2 min' // TODO: Calculate from actual data
      };

      setOperationalStats(newStats);

      // Update timestamp for future silent polling
      const currentTime = new Date().toISOString();
      localStorage.setItem('lastDashboardFetchTime', currentTime);

      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Dashboard data fetch completed successfully');
      }

    } catch (error) {
      // Don't handle AbortError as it's expected when component unmounts
      if (error instanceof Error && error.name === 'AbortError') {
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.log('Dashboard data fetch was aborted');
        }
        return;
      }
      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.error('Dashboard data fetch failed:', error);
      }
      handleApiError(error, 'Failed to load dashboard data');
    } finally {
      if (isMountedRef.current) {
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.log('Setting loading to false');
        }
        setIsLoading(false);
      } else {
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.log('Component unmounted, not setting loading to false');
        }
      }
    }
  }, [logout, handleApiError]);

  // Enhanced silent polling function with smart change detection
  const fetchDashboardDataSilently = useCallback(async (abortSignal?: AbortSignal): Promise<void> => {
    if (!isMountedRef.current) {
      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Component not mounted, skipping silent fetch');
      }
      return;
    }

    try {
      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Silently refreshing dashboard data...');
      }
      const token = localStorage.getItem('access_token');
      if (!token) {
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.error('No auth token found, skipping silent refresh');
        }
        return;
      }

      const baseUrl = import.meta.env.VITE_BACKEND_API_URL || '';

      // Check if we need to force a refresh based on time elapsed
      const lastFetchTime = localStorage.getItem('lastDashboardFetchTime');
      const forceRefresh = shouldForceRefresh(lastFetchTime);

      // Add If-Modified-Since header if we have a last fetch timestamp and don't need to force refresh
      const headers: Record<string, string> = {
        Authorization: `Bearer ${token}`
      };

      if (lastFetchTime && !forceRefresh) {
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.log(`Using If-Modified-Since header: ${lastFetchTime}`);
        }
        headers['If-Modified-Since'] = lastFetchTime;
      } else if (forceRefresh) {
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.log('Forcing dashboard refresh due to time elapsed since last update');
        }
      }

      // Fetch each endpoint individually to handle 304 responses properly
      let pendingResponse: any = null;
      let driversResponse: any = null;
      let assignedResponse: any = null;
      let hasNewData = false;

      // Fetch pending rides
      try {
        pendingResponse = await axios.get(`${baseUrl}/ride-requests/?type=pending`, {
          headers,
          signal: abortSignal
        });
        hasNewData = true;
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.log('Pending rides: Got new data (200)');
        }
      } catch (error: any) {
        if (error?.response?.status === 304) {
          if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
            console.log('Pending rides: No changes (304)');
          }
          // Keep existing data, no update needed
        } else {
          throw error; // Re-throw non-304 errors
        }
      }

      // Fetch drivers
      try {
        driversResponse = await axios.get(`${baseUrl}/drivers/`, {
          headers,
          signal: abortSignal
        });
        hasNewData = true;
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.log('Drivers: Got new data (200)');
        }
      } catch (error: any) {
        if (error?.response?.status === 304) {
          if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
            console.log('Drivers: No changes (304)');
          }
          // Keep existing data, no update needed
        } else {
          throw error; // Re-throw non-304 errors
        }
      }

      // Fetch assigned rides
      try {
        assignedResponse = await axios.get(`${baseUrl}/assigned-rides/`, {
          headers,
          signal: abortSignal
        });
        hasNewData = true;
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.log('Assigned rides: Got new data (200)');
        }
      } catch (error: any) {
        if (error?.response?.status === 304) {
          if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
            console.log('Assigned rides: No changes (304)');
          }
          // Keep existing data, no update needed
        } else {
          throw error; // Re-throw non-304 errors
        }
      }

      // Only update timestamp if we got some new data
      if (hasNewData) {
        const currentTime = new Date().toISOString();
        localStorage.setItem('lastDashboardFetchTime', currentTime);
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.log('Updated lastDashboardFetchTime due to new data');
        }
      }

      // Only update state if component is still mounted
      if (!isMountedRef.current) return;

      // Process pending rides if we got new data
      if (pendingResponse) {
        const pendingRidesData: RideRequest[] = Array.isArray(pendingResponse.data)
          ? pendingResponse.data.slice(0, 5)
          : [];

        // Always update when we get new data from server
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.log('Updating pending rides with new data:', pendingRidesData.length, 'rides');
        }
        setPendingRides(pendingRidesData);

        // Update pending count in stats
        setOperationalStats(prev => ({
          ...prev,
          pendingCount: pendingResponse.data?.length || 0
        }));
      }

      // Process drivers if we got new data
      if (driversResponse) {
        const allDrivers: Driver[] = Array.isArray(driversResponse.data)
          ? driversResponse.data
          : [];

        const availableDriversData = allDrivers
          .filter((driver: Driver) => driver.status === 'available')
          .slice(0, 5);

        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.log('Updating drivers with new data:', availableDriversData.length, 'available drivers');
        }
        setAvailableDrivers(availableDriversData);

        // Update driver counts in stats
        setOperationalStats(prev => ({
          ...prev,
          availableDrivers: allDrivers.filter((d: Driver) => d.status === 'available').length,
          busyDrivers: allDrivers.filter((d: Driver) => d.status === 'on_ride').length
        }));
      }

      // Process assigned rides if we got new data
      if (assignedResponse) {
        const activeRidesData: ActiveRide[] = Array.isArray(assignedResponse.data)
          ? assignedResponse.data.slice(0, 5)
          : [];

        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.log('Updating active rides with new data:', activeRidesData.length, 'rides');
        }
        setActiveRides(activeRidesData);

        // Update assigned count in stats
        setOperationalStats(prev => ({
          ...prev,
          assignedCount: assignedResponse.data?.length || 0
        }));
      }

      // Handle case where all endpoints returned 304
      if (!hasNewData) {
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.log('All endpoints returned 304 - no updates needed');
        }

        // Check if we should do a full refresh anyway based on time
        if (shouldForceRefresh(lastFetchTime)) {
          if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
            console.log('All 304s received but forcing a full dashboard refresh due to time elapsed');
          }
          // Remove the timestamp to force a full refresh next time
          localStorage.removeItem('lastDashboardFetchTime');
          // Schedule an immediate retry without If-Modified-Since
          setTimeout(() => {
            if (isMountedRef.current) {
              fetchDashboardData();
            }
          }, 500);
        }
      }

    } catch (error) {
      // Don't handle AbortError as it's expected when component unmounts
      if (error instanceof Error && error.name === 'AbortError') {
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.log('Silent dashboard data fetch was aborted');
        }
        return;
      }
      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.error('Error silently refreshing dashboard data:', error);
      }
      // Don't set error state on silent fetch to avoid UI disruption
      // But if it's an auth error, we should handle it
      if (axios.isAxiosError(error) && error.response?.status === 401) {
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.log('Authentication failed during silent refresh, logging out...');
        }
        logout();
      }
    }
  }, [fetchDashboardData, logout]);

  // Helper function to determine if we should force a refresh
  const shouldForceRefresh = useCallback((lastFetchTime: string | null): boolean => {
    if (!lastFetchTime) return true;

    const lastFetch = new Date(lastFetchTime);
    const now = new Date();
    const elapsedMinutes = (now.getTime() - lastFetch.getTime()) / (1000 * 60);

    // Force a refresh every 2 minutes regardless of 304 responses
    return elapsedMinutes > 2;
  }, []);

  // Component mount/unmount lifecycle management
  useEffect(() => {
    isMountedRef.current = true;
    if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
      console.log('Dashboard component mounted');
    }

    // Clear timestamp to force fresh data on initial load
    localStorage.removeItem('lastDashboardFetchTime');
    if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
      console.log('Cleared lastDashboardFetchTime to force fresh data');
    }

    return () => {
      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Dashboard component unmounting, cleaning up...');
      }
      isMountedRef.current = false;
    };
  }, []);

  // Set up polling with initial fetch
  useSilentPolling(
    fetchDashboardDataSilently,
    fetchDashboardData, // Use main fetch function for initial load
    10000, // Poll every 10 seconds for dashboard data
    5000,  // Random offset between -2.5s and +2.5s
    [logout]
  );

  // Format time from ISO string with null safety
  const formatTime = useCallback((isoString: string | null | undefined): string => {
    if (!isoString) return 'N/A';

    try {
      const date = new Date(isoString);
      if (Number.isNaN(date.getTime())) return 'Invalid Date';
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } catch (error) {
      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.error('Error formatting time:', error);
      }
      return 'N/A';
    }
  }, []);

  // Format date from ISO string with null safety
  const formatDate = useCallback((isoString: string | null | undefined): string => {
    if (!isoString) return 'N/A';

    try {
      const date = new Date(isoString);
      if (Number.isNaN(date.getTime())) return 'Invalid Date';
      return date.toLocaleDateString();
    } catch (error) {
      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.error('Error formatting date:', error);
      }
      return 'N/A';
    }
  }, []);

  // Functions for AssignDriverModal
  const openAssignModal = useCallback((rideId: number) => {
    setSelectedRideId(rideId);
    setSelectedDriverId(null);
    setIsAssigning(false);
    fetchDriversForAssignment();
    setIsModalOpen(true);
  }, []);

  const closeAssignModal = useCallback(() => {
    setIsModalOpen(false);
    setSelectedRideId(null);
    setSelectedDriverId(null);
    setIsAssigning(false);
  }, []);

  const fetchDriversForAssignment = useCallback(async () => {
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        setError('Authentication required');
        logout();
        return;
      }

      const baseUrl = import.meta.env.VITE_BACKEND_API_URL || '';
      
      const response = await axios.get(`${baseUrl}/drivers/`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Cache-Control': 'no-cache',
        }
      });

      // Use shared helper to transform and filter available drivers
      const transformedDrivers = getAvailableDrivers(response.data as RawDriverData[]);
      setAvailableDriversList(transformedDrivers);
    } catch (error) {
      handleApiError(error, 'Failed to fetch available drivers');
    }
  }, [handleApiError, logout]);
  
  const handleSelectDriver = useCallback((driverId: number) => {
    setSelectedDriverId(driverId);
  }, []);

  const handleAssignDriver = useCallback(async () => {
    if (!selectedRideId || !selectedDriverId) return;

    setIsAssigning(true);
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        setError('Authentication required');
        logout();
        return;
      }

      const baseUrl = import.meta.env.VITE_BACKEND_API_URL || '';
      
      await axios.post(`${baseUrl}/assign-driver/${selectedRideId}/`,
        {
          driver_id: selectedDriverId
        },
        {
          headers: { Authorization: `Bearer ${token}` }
        }
      );

      // Update UI after successful assignment
      closeAssignModal();
      
      // Refresh dashboard data to reflect the changes
      await fetchDashboardData();
      
    } catch (error) {
      handleApiError(error, 'Failed to assign driver to ride');
    } finally {
      setIsAssigning(false);
    }
  }, [selectedRideId, selectedDriverId, fetchDashboardData, handleApiError, logout, closeAssignModal]);

  return (
    <div className="dashboard-page">
      <div className="dashboard-content">
        {/* Header with user info and time */}
        <div className="dashboard-header">
          <div className="user-profile">
            <FaUserCircle className="user-avatar" />
            <div className="user-info">
              <h2>Welcome, {username}</h2>
              <div className="user-role">
                <span className={`role-badge ${role?.toLowerCase()}`}>{role}</span>
                <span className="current-time">
                  {currentTime.toLocaleDateString()} • {currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </span>
              </div>
            </div>
          </div>
          <div className="notifications">
          <Link to="/operator/pending-rides" className="notification-link">
            <FaBell className="notification-bell" />
            <span className="notification-badge">{operationalStats.pendingCount}</span>
          </Link>
          </div>
        </div>
        
        {isLoading ? (
          <div className="loading-container">
            <div className="spinner"></div>
            <p>Loading dashboard data...</p>
          </div>
        ) : error ? (
          <div className="error-container">
            <FaExclamationTriangle className="error-icon" />
            <p>{error}</p>
            <button
              className="btn btn-primary"
              onClick={() => {
                setError(null);
                fetchDashboardData();
              }}
            >
              Retry
            </button>
          </div>
        ) : (
          <>
            {/* Operational Stats */}
            <div className="stats-grid">
              <div className="stat-card">
                <div className="stat-icon-container pending">
                  <FaUserClock />
                </div>
                <div className="stat-details">
                  <div className="stat-value">{operationalStats.pendingCount}</div>
                  <div className="stat-label">Pending Rides</div>
                </div>
                <Link to="/operator/pending-rides" className="stat-action">View All</Link>
              </div>
              
              <div className="stat-card">
                <div className="stat-icon-container assigned">
                  <FaCar />
                </div>
                <div className="stat-details">
                  <div className="stat-value">{operationalStats.assignedCount}</div>
                  <div className="stat-label">Active Rides</div>
                </div>
                <Link to="/operator/assigned-rides" className="stat-action">Monitor</Link>
              </div>
              
              <div className="stat-card">
                <div className="stat-icon-container available">
                  <FaCheck />
                </div>
                <div className="stat-details">
                  <div className="stat-value">{operationalStats.availableDrivers}</div>
                  <div className="stat-label">Available Drivers</div>
                </div>
                <Link to="/operator/drivers" className="stat-action">Manage</Link>
              </div>
              
              <div className="stat-card">
                <div className="stat-icon-container completed">
                  <FaCalendarCheck />
                </div>
                <div className="stat-details">
                  <div className="stat-value">{operationalStats.todayCompletedRides}</div>
                  <div className="stat-label">Completed Today</div>
                </div>
                <Link to="/operator/history" className="stat-action">Details</Link>
              </div>
            </div>
            
            {/* Main dashboard grid layout */}
            <div className="dashboard-grid">
              {/* Pending Rides Section */}
              <div className="dashboard-section pending-rides">
                <div className="section-header">
                  <h3>Pending Ride Requests</h3>
                  <Link to="/operator/pending-rides" className="view-all">View All</Link>
                </div>
                
                {!Array.isArray(pendingRides) || pendingRides.length === 0 ? (
                  <div className="empty-state">
                    <p>No pending ride requests at the moment.</p>
                  </div>
                ) : (
                  <div className="rides-list">
                    {pendingRides.map((ride, idx) => (
                      <div key={ride?.id || `pending-${idx}`} className="ride-card pending">
                        <div className="ride-header">
                          <div className="ride-time">
                            <FaClock /> {formatTime(ride?.ride_datetime)}
                          </div>
                          <div className="ride-date">
                            {formatDate(ride?.ride_datetime)}
                          </div>
                        </div>

                        <div className="ride-details">
                          <div className="locations">
                            <div className="pickup">
                              <FaMapMarkerAlt className="pickup-icon" />
                              <span className="location-text">{ride?.pickup_location || 'Unknown pickup'}</span>
                            </div>
                            <div className="dropoff">
                              <FaMapMarkerAlt className="dropoff-icon" />
                              <span className="location-text">{ride?.dropoff_location || 'Unknown destination'}</span>
                            </div>
                          </div>

                          <div className="passenger-info">
                            <div className="name">{ride?.rider_name || 'Unknown rider'}</div>
                            <div className="passengers">
<span className="count">{ride?.passenger_count ?? 0} passengers</span>
<span className="luggage">{ride?.luggage_count ?? 0} luggage</span>
                            </div>
                          </div>
                        </div>

                        <div className="ride-actions">
                          {ride?.id && (
                            <button 
                              onClick={() => openAssignModal(ride.id)}
                              className="btn btn-primary"
                            >
                              Assign Driver
                            </button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
              
              {/* Available Drivers Section */}
              <div className="dashboard-section drivers">
                <div className="section-header">
                  <h3>Available Drivers</h3>
                  <Link to="/operator/drivers" className="view-all">View All</Link>
                </div>
                
                {!Array.isArray(availableDrivers) || availableDrivers.length === 0 ? (
                  <div className="empty-state">
                    <p>No drivers available at the moment.</p>
                  </div>
                ) : (
                  <div className="drivers-list">
                    {availableDrivers.map((driver, idx) => (
                      <div key={driver?.id || `driver-${idx}`} className="driver-card">
                        <div className="driver-info">
                          <div className="driver-avatar">
                            <FaUserCircle />
                          </div>
                          <div className="driver-details">
                            <div className="driver-name">{driver?.name || 'Unknown driver'}</div>
                            <div className="driver-vehicle">{driver?.vehicle || 'No vehicle info'}</div>
                          </div>
                        </div>
                        <div className="driver-status">
                          <span className="status-badge available">Available</span>
                        </div>
                        <div className="driver-actions">
                          {driver?.id && (
                            <Link to={`/operator/drivers/${driver.id}`} className="btn btn-secondary btn-sm">
                              View
                            </Link>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
              
              {/* Active Rides Section */}
              <div className="dashboard-section active-rides">
                <div className="section-header">
                  <h3>Active Rides</h3>
                  <Link to="/operator/assigned-rides" className="view-all">View All</Link>
                </div>
                
                {!Array.isArray(activeRides) || activeRides.length === 0 ? (
                  <div className="empty-state">
                    <p>No active rides at the moment.</p>
                  </div>
                ) : (
                  <div className="rides-list">
                    {activeRides.map((ride, idx) => (
                      <div key={ride?.id || `active-${idx}`} className="ride-card active">
                        <div className="ride-header">
                          <div className="ride-status">
                            <span className={`status-badge ${(ride?.status || 'unknown').toLowerCase()}`}>
                              {ride?.status || 'Unknown'}
                            </span>
                          </div>
                          <div className="ride-id">#{ride?.id || 'N/A'}</div>
                        </div>

                        <div className="ride-route">
                          <div className="origin">
                            <div className="location">{ride?.pickup_location || 'Unknown pickup'}</div>
                          </div>
                          <div className="route-arrow">
                            <FaArrowRight />
                          </div>
                          <div className="destination">
                            <div className="location">{ride?.dropoff_location || 'Unknown destination'}</div>
                          </div>
                        </div>

                        <div className="driver-info">
                          <div className="driver-name">
                            <FaUserCircle /> {ride?.driver_name || 'Unknown driver'}
                          </div>
                          <div className="assigned-time">
                            <FaClock /> Assigned: {formatTime(ride?.assigned_time)}
                          </div>
                        </div>

                        <div className="ride-actions">
                          {ride?.id && (
                            <Link to={`/operator/assigned-rides/${ride.id}`} className="btn btn-accent btn-sm">
                              Details
                            </Link>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </>
        )}
      </div>

      {/* Modal for assigning drivers to rides */}
      <AssignDriverModal
        isOpen={isModalOpen}
        rideId={selectedRideId}
        drivers={availableDriversList}
        selectedDriver={selectedDriverId}
        isAssigning={isAssigning}
        onClose={closeAssignModal}
        onSelectDriver={handleSelectDriver}
        onConfirmAssign={handleAssignDriver}
        onRefreshDrivers={fetchDriversForAssignment}
      />
    </div>
  );
};

export default DashboardPage;
import React from 'react';
import { NavLink } from 'react-router-dom';
import { FaHome, FaTaxi, FaUserTie, FaHistory, FaCog, FaSignOutAlt } from 'react-icons/fa';
import '../../../styles/components/auth/pages-operator/OperatorSidebar.scss';

const OperatorSidebar: React.FC = () => {
  const navItems = [
    { to: '/operator', icon: <FaHome />, label: 'Dashboard' },
    { to: '/operator/pending-rides', icon: <FaTaxi />, label: 'Pending Rides' },
    { to: '/operator/assigned-rides', icon: <FaTaxi />, label: 'Assigned Rides' },
    { to: '/operator/drivers', icon: <FaUserTie />, label: 'Drivers' },
    { to: '/operator/history', icon: <FaHistory />, label: 'History' },
    { to: '/wip', icon: <FaCog />, label: 'Settings' },
    { to: '/', icon: <FaSignOutAlt />, label: 'Quit' },
  ];

  return (
    <aside className="operator-sidebar">
      <div className="sidebar-header">
        <h2>Operator Panel</h2>
      </div>
      <nav className="sidebar-nav">
        <ul>
          {navItems.map((item) => (
            <li key={item.to}>
              <NavLink 
                to={item.to} 
                className={({ isActive }) => 
                  `nav-link ${isActive ? 'active' : ''}`
                }
                end={item.to === '/operator'}
              >
                <span className="nav-icon">{item.icon}</span>
                <span className="nav-label">{item.label}</span>
              </NavLink>
            </li>
          ))}
        </ul>
      </nav>
    </aside>
  );
};

export default OperatorSidebar;

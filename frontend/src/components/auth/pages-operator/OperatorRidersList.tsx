// frontend/src/components/auth/pages-operator/OperatorRidersList.tsx
//~ File for operator to view and manage ride requests
import React, { useState, useEffect } from 'react';
import { FiRefreshCw, FiXCircle, FiUser } from 'react-icons/fi';
import { useLocation, useNavigate } from 'react-router-dom';
import { FaUser, FaSuitcase } from 'react-icons/fa';
import OperatorSidebar from './OperatorSidebar';
import AssignDriverModal from './AssignDriverModal';
import axios from 'axios';

import '../../../styles/components/auth/pages-operator/PendingRides.scss';
import { useAuth } from '../AuthContext';
import { useSilentPolling } from '../../../types/useSilentPolling';

interface RideRequest {
  id: number;
  rider_name: string;
  pickup_location: string;
  dropoff_location: string;
  ride_datetime: string;
  passenger_count: number;
  luggage_count: number;
  distance_km: string;
  rider_phone: string;
  calculated_fare: string | null;
  created_at: string;
  status: string;
  driver?: number | null;
  driver_name?: string | null;
}

interface Driver {
  id: number;
  username: string;
  profile: {
    phone_number: string;
    vehicle_type: string;
    current_location: string;
  };
}

const OperatorRidersList: React.FC = () => {
  const [rideRequests, setRideRequests] = useState<RideRequest[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [selectedRide, setSelectedRide] = useState<number | null>(null);
  const [drivers, setDrivers] = useState<Driver[]>([]);
  const [selectedDriver, setSelectedDriver] = useState<number | null>(null);
  const [assigning, setAssigning] = useState<boolean>(false);
  const [rejectedRideIds, setRejectedRideIds] = useState<Set<number>>(new Set());
  const [isLoading, setIsLoading] = useState(false);
  const [preselectedDriverName, setPreselectedDriverName] = useState<string | null>(null);
  const { logout, username, role } = useAuth();
  
  // Access URL query parameters
  const location = useLocation();
  const navigate = useNavigate();

  console.log('Current logged in user:', { username, role });

  const fetchRideRequests = async () => {
    setError(null);
    setIsLoading(true);
    try {
      const baseUrl = import.meta.env.VITE_BACKEND_API_URL;
      // Ensure we're using the type=pending query parameter to filter at the database level
      const apiUrl = baseUrl.endsWith('/api') 
        ? `${baseUrl}/ride-requests/?type=pending`
        : `${baseUrl}/api/ride-requests/?type=pending`;
      console.log('Sending request to:', apiUrl);
      if (import.meta.env.DEV) {
   // Log header names only – never the token value
   // eslint-disable-next-line no-console
   console.log('Sending Authorization header');
 }
      const response = await axios.get(apiUrl, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('access_token')}`,
        },
      });
if (import.meta.env.DEV) {
  // eslint-disable-next-line no-console
  console.log('Response received:', response.status, response.data);
}
      const data: RideRequest[] = response.data;
      // Since backend now filters by pending status, we just need to filter out rejected rides
      const filteredData = data.filter(ride => !rejectedRideIds.has(ride.id));
      setRideRequests(filteredData);
      console.log(`Loaded ${filteredData.length} pending rides after filtering rejected rides`);
      setError(null);
    } catch (err: unknown) {
      console.error('Error details:', err);
      if (err instanceof Error && err.message.includes('401')) {
        console.log('Unauthorized error - logging out');
        logout();
      } else {
        console.log('Other error occurred:', err instanceof Error ? err.message : 'Unknown error');
        setError('Failed to fetch ride requests. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const fetchDrivers = async () => {
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      // Fix the API URL to include /api/ prefix
      const baseUrl = import.meta.env.VITE_BACKEND_API_URL;
      const apiUrl = baseUrl.endsWith('/api')
        ? `${baseUrl}/drivers/`
        : `${baseUrl}/api/drivers/`;

      console.log('Fetching drivers from:', apiUrl);

      const response = await axios.get(apiUrl, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Drivers response:', response.data);
      }

      // Transform the driver data to match our interface and filter available drivers
      const transformedDrivers = response.data
        .filter((driver: any) => driver.status === 'available' || driver.status === 'offline')
        .map((driver: any) => ({
          id: driver.id,
          username: driver.name, // Map name to username for modal compatibility
          profile: {
            phone_number: driver.phone || 'No phone',
            vehicle_type: driver.vehicle || 'N/A',
            current_location: driver.location || 'Unknown'
          }
        }));

      setDrivers(transformedDrivers);
    } catch (err) {
      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.error('Error fetching drivers:', err);
      }
      if (axios.isAxiosError(err)) {
        if (err.response?.status === 404) {
          setError('Drivers endpoint not found. Please contact support.');
        } else if (err.response?.status === 401) {
          logout();
        } else {
          setError(`Failed to fetch drivers: ${err.response?.data?.error || err.message}`);
        }
      } else {
        setError(err instanceof Error ? err.message : 'Failed to fetch drivers');
      }
    }
  };

  const fetchDataSilently = async () => {
    try {
      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Silently fetching operator data (rides and drivers)...');
      }
      const token = localStorage.getItem('access_token');
      if (!token) {
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.error('No auth token found, skipping silent refresh');
        }
        return;
      }
      
      const baseUrl = import.meta.env.VITE_BACKEND_API_URL || '';
      const headers: Record<string, string> = {
        Authorization: `Bearer ${token}`
      };
      
      // CRITICAL FIX: First check if there's a new ride in the database by making a count-only request
      // This ensures we can detect new rides without relying on timestamps
      let currentRideCount = 0;
      try {
        const countUrl = baseUrl.endsWith('/api') 
          ? `${baseUrl}/ride-requests/?type=pending&count_only=true`
          : `${baseUrl}/api/ride-requests/?type=pending&count_only=true`;
        
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.log('Making count-only request to check for new rides...');
        }
        const countResponse = await axios.get(countUrl, {
          headers: { Authorization: `Bearer ${token}` }
        });
        currentRideCount = countResponse.data.count || 0;
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.log(`Current pending ride count from server: ${currentRideCount}`);
        }
      } catch (countError) {
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.error('Error checking ride count:', countError);
        }
        // Continue with normal flow if count check fails
      }
      
      // Force refresh if the ride count has changed or if time threshold exceeded
      const lastRideCount = localStorage.getItem('lastRideCount') ? 
        parseInt(localStorage.getItem('lastRideCount') || '0', 10) : 0;
      
      const lastFetchTime = localStorage.getItem('lastRidesFetchTime');
      const forceRefresh = shouldForceRefresh(lastFetchTime) || 
                         (currentRideCount > 0 && currentRideCount !== lastRideCount);
      
      if (forceRefresh) {
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.log(`Forcing refresh: ${shouldForceRefresh(lastFetchTime) ? 'time elapsed' : 'ride count changed'} ` +
                      `(last: ${lastRideCount}, current: ${currentRideCount})`);
        }
        // Don't add If-Modified-Since header to force a full refresh
        localStorage.removeItem('lastRidesFetchTime'); // Important: remove the timestamp to ensure we get fresh data
      } else if (lastFetchTime) {
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.log(`Using If-Modified-Since header: ${lastFetchTime}`);
        }
        headers['If-Modified-Since'] = lastFetchTime;
      }
      
      // Prepare API URLs
      const ridesUrl = baseUrl.endsWith('/api') 
        ? `${baseUrl}/ride-requests/?type=pending`
        : `${baseUrl}/api/ride-requests/?type=pending`;
      
      const driversUrl = baseUrl.endsWith('/api')
        ? `${baseUrl}/drivers/`
        : `${baseUrl}/api/drivers/`;
      
      try {
        // Fetch both ride requests and drivers in parallel
        const [ridesResponse, driversResponse] = await Promise.all([
          axios.get(ridesUrl, { headers }),
          axios.get(driversUrl, { headers })
        ]);
        
        // Store the current timestamp for future If-Modified-Since headers
        const currentTime = new Date().toISOString();
        localStorage.setItem('lastRidesFetchTime', currentTime);
        
        // CRITICAL FIX: Update the ride count in localStorage
        if (ridesResponse.status === 200 && ridesResponse.data) {
          const currentCount = ridesResponse.data.length;
          if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
            console.log(`Updating stored ride count to: ${currentCount}`);
          }
          localStorage.setItem('lastRideCount', currentCount.toString());
        }
        
        // Process ride requests if we got a 200 OK response
        if (ridesResponse.status === 200) {
          const ridesData = ridesResponse.data;
          if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
            console.log(`Received ${ridesData.length} ride requests from server`);
          }
          
          // Filter out rejected rides
          const filteredRidesData = ridesData.filter((ride: RideRequest) => !rejectedRideIds.has(ride.id));
          
          // Always update if we forced a refresh or if data has changed
          const ridesChanged = forceRefresh || 
            filteredRidesData.length !== rideRequests.length || 
            filteredRidesData.some((ride: RideRequest) => {
              const existingRide = rideRequests.find(r => r.id === ride.id);
              return !existingRide || 
                     existingRide.status !== ride.status || 
                     existingRide.driver !== ride.driver || 
                     existingRide.driver_name !== ride.driver_name || 
                     existingRide.pickup_location !== ride.pickup_location || 
                     existingRide.dropoff_location !== ride.dropoff_location || 
                     existingRide.ride_datetime !== ride.ride_datetime;
            });
            
          if (ridesChanged) {
            if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
              console.log(`Ride requests changed or refresh forced, updating UI with ${filteredRidesData.length} rides`);
            }
            setRideRequests(filteredRidesData);
          } else {
            if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
              console.log('No meaningful ride request changes, skipping update');
            }
          }
        }
        
        // Process drivers if we got a 200 OK response
        if (driversResponse.status === 200) {
          const driversData = driversResponse.data;
          // Transform the driver data to match our interface and filter available drivers
          const transformedDrivers = driversData
            .filter((driver: any) => driver.status === 'available' || driver.status === 'offline')
            .map((driver: any) => ({
              id: driver.id,
              username: driver.name, // Map name to username for modal compatibility
              profile: {
                phone_number: driver.phone || 'No phone',
                vehicle_type: driver.vehicle || 'N/A',
                current_location: driver.location || 'Unknown'
              }
            }));

          // Always update if we forced a refresh or if data has changed
          const driversChanged = forceRefresh ||
            transformedDrivers.length !== drivers.length ||
            transformedDrivers.some((newDriver: any) => {
              const currentDriver = drivers.find(d => d.id === newDriver.id);
              return !currentDriver ||
                     currentDriver.username !== newDriver.username ||
                     currentDriver.profile?.phone_number !== newDriver.profile?.phone_number ||
                     currentDriver.profile?.vehicle_type !== newDriver.profile?.vehicle_type ||
                     currentDriver.profile?.current_location !== newDriver.profile?.current_location;
            });

          if (driversChanged) {
            if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
              console.log(`Drivers data changed or refresh forced, updating UI with ${transformedDrivers.length} drivers`);
            }
            setDrivers(transformedDrivers);
          } else {
            if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
              console.log('No meaningful driver changes, skipping update');
            }
          }
        }
      } catch (apiError: any) {
        // Handle 304 Not Modified as a success case
        if (apiError?.response?.status === 304) {
          if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
            console.log('Server returned 304 Not Modified - data is still fresh');
          }

          // Check if we should do a full refresh anyway based on time
          if (shouldForceRefresh(lastFetchTime)) {
            if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
              console.log('304 received but forcing a full refresh due to time elapsed');
            }
            // Remove the timestamp to force a full refresh next time
            localStorage.removeItem('lastRidesFetchTime');
            // Schedule an immediate retry without If-Modified-Since
            setTimeout(() => fetchRideRequests(), 500);
          }
        } else {
          // Re-throw other errors to be handled by the outer catch
          throw apiError;
        }
      }
    } catch (err) {
      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.error('Error silently fetching data:', err);
      }
      // Don't set error state on silent fetch to avoid UI disruption
    }
  };
  
  // Helper function to determine if we should force a refresh
  const shouldForceRefresh = (lastFetchTime: string | null): boolean => {
    if (!lastFetchTime) return true;
    
    const lastFetch = new Date(lastFetchTime);
    const now = new Date();
    const elapsedMinutes = (now.getTime() - lastFetch.getTime()) / (1000 * 60);
    
    // Force a refresh every 2 minutes regardless of 304 responses
    return elapsedMinutes > 2;
  };

  const handleAssignDriver = (rideId: number) => {
    setSelectedRide(rideId);
    // If we have a preselected driver, the driver will already be selected in the modal
    if (selectedDriver !== null && preselectedDriverName) {
      console.log(`Pre-selected driver ${preselectedDriverName} (ID: ${selectedDriver}) will be shown for ride ${rideId}`);
    }
  };

  const handleRejectRide = async (rideId: number) => {
    setError(null);
    try {
      const baseUrl = import.meta.env.VITE_BACKEND_API_URL;
      const apiUrl = baseUrl.endsWith('/api') 
        ? `${baseUrl}/reject-ride/${rideId}/`
        : `${baseUrl}/api/reject-ride/${rideId}/`;
      await axios.post(apiUrl, {}, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${localStorage.getItem('access_token')}`,
        },
      });
      setRejectedRideIds(prev => new Set(prev).add(rideId));
      setRideRequests(prev => prev.filter(ride => ride.id !== rideId));
    } catch (err) {
      if (axios.isAxiosError(err) && err.response?.status === 401) {
        logout();
      } else {
        setError('Failed to reject ride. Please try again.');
      }
    }
  };

  const handleConfirmAssign = async () => {
    if (!selectedRide || !selectedDriver) return;
    setAssigning(true);
    setError(null);
    try {
      const baseUrl = import.meta.env.VITE_BACKEND_API_URL;
      const apiUrl = baseUrl.endsWith('/api') 
        ? `${baseUrl}/assign-driver/${selectedRide}/`
        : `${baseUrl}/api/assign-driver/${selectedRide}/`;
      await axios.post(apiUrl, { driver_id: selectedDriver }, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${localStorage.getItem('access_token')}`,
        },
      });
      fetchRideRequests();
      setSelectedRide(null);
    } catch (err) {
      if (axios.isAxiosError(err) && err.response?.status === 401) {
        logout();
      } else {
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.error('Error assigning driver:', err);
        }
        setError('Failed to assign driver. Please try again.');
      }
    } finally {
      setAssigning(false);
    }
  };

  // Initial data fetch and check for URL parameters
  useEffect(() => {
    fetchRideRequests();
    fetchDrivers();
    
    // Check for preselected driver from URL params (coming from DriversList)
    const queryParams = new URLSearchParams(location.search);
    const preselectDriverId = queryParams.get('preselect_driver');
    const driverName = queryParams.get('driver_name');
    
    if (preselectDriverId && !isNaN(Number(preselectDriverId))) {
      const driverId = Number(preselectDriverId);
      setSelectedDriver(driverId);
      if (driverName) {
        setPreselectedDriverName(decodeURIComponent(driverName));
      }
      // Clear the URL parameters after reading them
      navigate('/operator/pending-rides', { replace: true });
    }
  }, [location.search, navigate]);
  
  // Setup data polling for automatic refreshes
  useSilentPolling(
    fetchDataSilently,
    async () => {
      await fetchRideRequests();
      await fetchDrivers();
    },
    15000, // 15 seconds base interval for all data
    5000,   // Random offset between -2.5s and +2.5s
    [logout, rejectedRideIds]
  );

  if (error) return (
    <div className="operator-list-view">
      <OperatorSidebar />
      <div className="error-container">
        <p className="error-message">{error}</p>
        <button 
          className="btn custom-refresh-btn" 
          onClick={fetchRideRequests} 
          title="Retry"
        >
          <FiRefreshCw /> Retry
        </button>
      </div>
    </div>
  );

  return (
    <div className="operator-list-view">
      <OperatorSidebar />
      <div className="page-header" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h2>Pending Ride Requests</h2>
        <div className="page-actions">
          <button 
            className="btn custom-refresh-btn" 
            onClick={fetchRideRequests} 
            title="Refresh ride requests"
          >
            <FiRefreshCw className={isLoading ? 'spinning' : ''} /> Refresh Rides
          </button>
        </div>
      </div>  

      {rideRequests.length === 0 ? (
        <div className="no-rides">
          <p>No pending ride requests available at the moment.</p>
        </div>
      ) : (
        <div className="rides-list" style={{overflowX: 'auto', width: '100%'}}>
          <table className="rides-table" style={{width: '100%', tableLayout: 'auto'}}>
            <thead>
              <tr>
                <th>ID</th>
                <th>Rider Name</th>
                <th>Phone</th>
                <th>Pickup/Dropoff</th>
                <th>Date/Time</th>
                <th>Pax/Luggage/Details</th>
                <th>Actions</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              {rideRequests.map((request) => (
                <tr key={request.id}>
                  <td>{request.id}</td>
                  <td>{request.rider_name}</td>
                  <td>{request.rider_phone}</td>
                  <td>{request.pickup_location}<br/><hr/>{request.dropoff_location}</td>
                  <td style={{width: '150px'}}>{new Date(request.ride_datetime).toLocaleDateString()}<br/>{new Date(request.ride_datetime).toLocaleTimeString()}</td>
                  <td>
                    <span style={{marginRight: '8px'}}><FaUser /> {request.passenger_count}</span>
                    <span style={{marginRight: '8px'}}><FaSuitcase /> {request.luggage_count}</span>
                    <br/>
                    <span style={{marginRight: '8px'}}><span style={{fontFamily: 'FontAwesome'}}></span> {Number.isFinite(+request.distance_km)
   ? (+request.distance_km).toFixed(2)
   : 'N/A'}</span>
                    <br/>
                    <span><span style={{fontFamily: 'FontAwesome'}}></span> {request.calculated_fare ? `${parseFloat(request.calculated_fare).toFixed(2)}` : 'N/A'}</span>
                  </td>
                  <td>
                    {request.status.toLowerCase() === "pending" ? (
                      <div style={{display: 'flex', justifyContent: 'space-between'}}>
                        <button 
                          className="btn btn-outline-danger btn-sm me-2"
                          onClick={() => handleRejectRide(request.id)}
                        >
                          <FiXCircle /> Reject
                        </button>
                        <button 
                          className="btn btn-outline-primary btn-sm"
                          onClick={() => handleAssignDriver(request.id)}
                        >
                          <FiUser /> Assign
                        </button>
                      </div>
                    ) : (
                      <span>Status: {request.status}</span>
                    )}
                  </td>
                  <td>
                    {request.status}
                    {request.status === "Accepted" && request.driver_name
                      ? ` (${request.driver_name})`
                      : ""}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      <AssignDriverModal
        isOpen={selectedRide !== null}
        rideId={selectedRide}
        drivers={drivers}
        selectedDriver={selectedDriver}
        isAssigning={assigning}
        onClose={() => {
          setSelectedRide(null);
          setSelectedDriver(null);
          setPreselectedDriverName(null);
        }}
        onSelectDriver={setSelectedDriver}
        onConfirmAssign={handleConfirmAssign}
        onRefreshDrivers={fetchDrivers}
      />
    </div>
  );
};

export default OperatorRidersList;

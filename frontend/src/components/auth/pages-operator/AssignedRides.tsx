import React, { useState, useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import { <PERSON>a<PERSON>ser, FaMapMarkerAlt, FaPhone, FaSync, FaSpinner, FaEye, FaCarSide, FaEllipsisV, FaExchangeAlt, FaUserClock, FaTimes } from 'react-icons/fa';
import { FiFileText, FiCheckCircle } from 'react-icons/fi';
import '../../../styles/components/auth/pages-operator/AssignedRides.scss';
import { useAuth } from '../AuthContext';
import AssignDriverModal from './AssignDriverModal';
import OperatorActionsPanel from './OperatorActionsPanel';
import axios from 'axios';
import { useSilentPolling } from '../../../types/useSilentPolling';
import { TransformedDriver, RawDriverData, getAvailableDrivers } from '../../../types/driver';

interface AssignedRide {
  id: string;
  pickup: string;
  dropoff: string;
  distance_km: number;
  calculated_fare: number | null;
  passenger_count: number;
  luggage_count: number;
  rider_name: string;
  rider_phone: string;
  created_at: string;
  ride_datetime: string;
  driver: {
    id: string;
    name: string;
    phone: string;
    vehicle: string;
  };
  status: 'assigned' | 'picked_up' | 'in_progress' | 'completed';
  assignedTime: string;
  estimatedArrival: string;
}

const AssignedRides: React.FC = () => {
  const { logout } = useAuth();
  const [assignedRides, setAssignedRides] = useState<AssignedRide[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [detailRide, setDetailRide] = useState<AssignedRide | null>(null);
  const [showDetails, setShowDetails] = useState<boolean>(false);

  // Reassign functionality state
  const [showReassignModal, setShowReassignModal] = useState(false);
  const [selectedRideForReassign, setSelectedRideForReassign] = useState<string | null>(null);
  const [availableDrivers, setAvailableDrivers] = useState<TransformedDriver[]>([]);
  const [selectedDriver, setSelectedDriver] = useState<number | null>(null);
  const [isReassigning, setIsReassigning] = useState(false);

  // Operator actions panel state
  const [showActionsPanel, setShowActionsPanel] = useState(false);
  const [selectedRideForActions, setSelectedRideForActions] = useState<AssignedRide | null>(null);
  
  // Action dropdown state
  const [openActionMenu, setOpenActionMenu] = useState<string | null>(null);
  const actionButtonRefs = useRef<{[key: string]: HTMLButtonElement | null}>({});
  const dropdownRef = useRef<HTMLDivElement | null>(null);
  const [dropdownPosition, setDropdownPosition] = useState<{top: number, left: number, width: number}>({top: 0, left: 0, width: 0});
  const portalContainer = useRef<HTMLDivElement | null>(null);
  const createdPortalContainer = useRef<boolean>(false);

  // Create portal container on mount
  useEffect(() => {
    // Create portal container if it doesn't exist
    if (!document.getElementById('dropdown-portal-container')) {
      const div = document.createElement('div');
      div.id = 'dropdown-portal-container';
      document.body.appendChild(div);
      portalContainer.current = div;
      createdPortalContainer.current = true; // Mark that we created it
    } else {
      portalContainer.current = document.getElementById('dropdown-portal-container') as HTMLDivElement;
      createdPortalContainer.current = false; // We didn't create it
    }

    // Clean up on unmount - only remove if we created it
    return () => {
      if (createdPortalContainer.current && portalContainer.current && portalContainer.current.parentNode) {
        portalContainer.current.parentNode.removeChild(portalContainer.current);
      }
    };
  }, []);

  const fetchAssignedRidesSilently = async () => {
    try {
      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Silently fetching assigned rides data...');
      }
      const token = localStorage.getItem('access_token');
      if (!token) {
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.error('No auth token found, skipping silent refresh');
        }
        return;
      }

      const baseUrl = import.meta.env.VITE_BACKEND_API_URL || '';
      
      // CRITICAL FIX: First check if there's a change in assigned rides by making a count-only request
      // This ensures we can detect changes without relying on timestamps
      let currentRideCount = 0;
      try {
        const countUrl = baseUrl.endsWith('/api') 
          ? `${baseUrl}/assigned-rides/?count_only=true`
          : `${baseUrl}/api/assigned-rides/?count_only=true`;
        
        console.log('Making count-only request to check for assigned rides changes...');
        const countResponse = await axios.get(countUrl, {
          headers: { Authorization: `Bearer ${token}` }
        });
        currentRideCount = countResponse.data.count || 0;
        console.log(`Current assigned rides count from server: ${currentRideCount}`);
      } catch (countError) {
        console.error('Error checking assigned rides count:', countError);
        // Continue with normal flow if count check fails
      }
      
      // Force refresh if the ride count has changed or if time threshold exceeded
      const lastRideCount = localStorage.getItem('lastAssignedRideCount') ? 
        parseInt(localStorage.getItem('lastAssignedRideCount') || '0', 10) : 0;
      
      const lastFetchTime = localStorage.getItem('lastAssignedRidesFetchTime');
      const forceRefresh = shouldForceRefresh(lastFetchTime) || 
                         (currentRideCount > 0 && currentRideCount !== lastRideCount);
      
      const apiUrl = baseUrl.endsWith('/api') 
        ? `${baseUrl}/assigned-rides/`
        : `${baseUrl}/api/assigned-rides/`;
      
      const headers: Record<string, string> = {
        Authorization: `Bearer ${token}`
      };
      
      if (forceRefresh) {
        console.log(`Forcing assigned rides refresh: ${shouldForceRefresh(lastFetchTime) ? 'time elapsed' : 'ride count changed'} ` +
                    `(last: ${lastRideCount}, current: ${currentRideCount})`);
        // Don't add If-Modified-Since header to force a full refresh
        localStorage.removeItem('lastAssignedRidesFetchTime'); // Important: remove the timestamp to ensure we get fresh data
      } else if (lastFetchTime) {
        console.log(`Using If-Modified-Since header: ${lastFetchTime}`);
        headers['If-Modified-Since'] = lastFetchTime;
      }
      
      try {
        const response = await axios.get(apiUrl, { headers });
        
        // Store the current timestamp for future If-Modified-Since headers
        const currentTime = new Date().toISOString();
        localStorage.setItem('lastAssignedRidesFetchTime', currentTime);
        
        // CRITICAL FIX: Update the assigned ride count in localStorage
        if (response.status === 200 && response.data) {
          const currentCount = response.data.length;
          if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
            console.log(`Updating stored assigned rides count to: ${currentCount}`);
          }
          localStorage.setItem('lastAssignedRideCount', currentCount.toString());
        }
        
        // Process the data
        const data = response.data;
        
        if (data && data.length > 0) {
          // Transform the data in the same way as the main fetch function
          const mappedRides = data.map((ride: any) => ({
            id: ride.id.toString(),
            pickup: ride.pickup_location || 'N/A',
            dropoff: ride.dropoff_location || 'N/A',
            distance_km: ride.distance_km || 0,
            calculated_fare: ride.calculated_fare || null,
            passenger_count: ride.passenger_count || 0,
            luggage_count: ride.luggage_count || 0,
            rider_name: ride.rider_name || 'N/A',
            rider_phone: ride.rider_phone || 'N/A',
            created_at: ride.created_at || 'N/A',
            ride_datetime: ride.ride_datetime || 'N/A',
            driver: {
              id: ride.driver_id?.toString() || 'N/A',
              name: ride.driver_name || 'N/A',
              phone: ride.driver_phone || 'N/A',
              vehicle: ride.driver_vehicle || 'N/A',
            },
            status: ride.status === 'Assigned' ? 'assigned' :
              ride.status === 'PickedUp' ? 'picked_up' :
                ride.status === 'InProgress' ? 'in_progress' :
                  ride.status === 'Completed' ? 'completed' : 'assigned',
            assignedTime: ride.assigned_time || 'N/A',
            estimatedArrival: ride.estimated_arrival || 'N/A',
          }));
          
          // Always update if we forced a refresh or if data has changed
          const ridesChanged = forceRefresh || mappedRides.length !== assignedRides.length || 
            mappedRides.some((ride: AssignedRide) => {
              const existingRide = assignedRides.find(r => r.id === ride.id);
              return !existingRide || 
                    existingRide.status !== ride.status || 
                    existingRide.driver.name !== ride.driver.name;
            });
            
          if (ridesChanged) {
            console.log('Assigned rides changed or refresh forced, updating UI with', mappedRides.length, 'rides');
            setAssignedRides(mappedRides);
          } else {
            console.log('No meaningful assigned rides changes, skipping UI update');
          }
        } else {
          console.log('No assigned rides data received in silent update');
        }
      } catch (apiError: any) {
        // Handle 304 Not Modified as a success case
        if (apiError?.response?.status === 304) {
          if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
            console.log('Server returned 304 Not Modified - assigned rides data is still fresh');
          }

          // Check if we should do a full refresh anyway based on time
          if (shouldForceRefresh(lastFetchTime)) {
            if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
              console.log('304 received but forcing a full refresh due to time elapsed');
            }
            // Remove the timestamp to force a full refresh next time
            localStorage.removeItem('lastAssignedRidesFetchTime');
            // Schedule an immediate retry without If-Modified-Since
            setTimeout(() => fetchAssignedRides(), 500);
          }
        } else {
          // Re-throw other errors to be handled by the outer catch
          throw apiError;
        }
      }
    } catch (err) {
      console.error('Error silently refreshing assigned rides:', err);
      // Don't set error state on silent fetch to avoid UI disruption
    }
  };
  
  // Helper function to determine if we should force a refresh
  const shouldForceRefresh = (lastFetchTime: string | null): boolean => {
    if (!lastFetchTime) return true;
    
    const lastFetch = new Date(lastFetchTime);
    const now = new Date();
    const elapsedMinutes = (now.getTime() - lastFetch.getTime()) / (1000 * 60);
    
    // Force a refresh every 2 minutes regardless of 304 responses
    return elapsedMinutes > 2;
  };

  const fetchAssignedRides = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const token = localStorage.getItem('access_token');
      if (!token) {
        console.error('No authentication token found');
        setError('You must be logged in to view assigned rides');
        setIsLoading(false);
        logout();
        return;
      }

      console.log('Fetching assigned rides from API...');
      const baseUrl = import.meta.env.VITE_BACKEND_API_URL;
      if (!baseUrl) {
        throw new Error('VITE_BACKEND_API_URL is not configured');
      }

      const apiUrl = baseUrl.endsWith('/api')
        ? `${baseUrl}/assigned-rides/`
        : `${baseUrl}/api/assigned-rides/`;
      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Requesting from URL:', apiUrl);
        console.log('Headers:', { Authorization: `Bearer ${token}` });
      }

      const response = await axios.get(apiUrl, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('API Response status:', response.status);
      }

      const data = response.data;
      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Assigned rides data received:', data.length ? `${data.length} rides` : 'Empty array');
      }

      if (data && data.length > 0) {
        const mappedRides = data.map((ride: any) => ({
          id: ride.id.toString(),
          pickup: ride.pickup_location || 'N/A',
          dropoff: ride.dropoff_location || 'N/A',
          distance_km: ride.distance_km || 0,
          calculated_fare: ride.calculated_fare || null,
          passenger_count: ride.passenger_count || 0,
          luggage_count: ride.luggage_count || 0,
          rider_name: ride.rider_name || 'N/A',
          rider_phone: ride.rider_phone || 'N/A',
          created_at: ride.created_at || 'N/A',
          ride_datetime: ride.ride_datetime || 'N/A',
          driver: {
            id: ride.driver_id?.toString() || 'N/A',
            name: ride.driver_name || 'N/A',
            phone: ride.driver_phone || 'N/A',
            vehicle: ride.driver_vehicle || 'N/A',
          },
          status: ride.status === 'Assigned' ? 'assigned' :
            ride.status === 'PickedUp' ? 'picked_up' :
              ride.status === 'InProgress' ? 'in_progress' :
                ride.status === 'Completed' ? 'completed' : 'assigned',
          assignedTime: ride.assigned_time || 'N/A',
          estimatedArrival: ride.estimated_arrival || 'N/A',
        }));
        setAssignedRides(mappedRides);
        setError(null);
      } else {
        setAssignedRides([]);
        setError('No assigned rides found in the system.');
      }
    } catch (err) {
      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.error('Error fetching assigned rides:', err);
      }

      if (axios.isAxiosError(err) && err.response?.status === 401) {
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.log('Unauthorized error - logging out');
        }
        logout();
      } else {
        setError(`API Error: ${err instanceof Error ? err.message : 'Failed to fetch assigned rides'}.`);
      }
    } finally {
      setIsLoading(false);
    }
  };

  useSilentPolling(
    fetchAssignedRidesSilently,
    async () => {
      setIsLoading(true);
      await fetchAssignedRides();
    },
    10000, // Poll every 10 seconds for assigned rides
    5000,  // Random offset between -2.5s and +2.5s
    [logout]
  );

  useEffect(() => {
    // No need for manual interval setup, handled by useSilentPolling
    return () => {}; // Empty return for consistency, cleanup handled by hook
  }, []);

  const getStatusBadge = (status: string) => {
    const statusMap: Record<string, { label: string; className: string }> = {
      assigned: { label: 'Assigned', className: 'status-assigned' },
      picked_up: { label: 'Picked Up', className: 'status-picked-up' },
      in_progress: { label: 'In Progress', className: 'status-in-progress' },
      completed: { label: 'Completed', className: 'status-completed' },
    };

    const statusInfo = statusMap[status] || { label: 'Unknown', className: 'status-unknown' };

    return (
      <span className={`status-badge ${statusInfo.className}`}>
        {statusInfo.label}
      </span>
    );
  };

  const handleCompleteRide = async (rideId: string) => {
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        setError('Authentication required');
        logout();
        return;
      }

      const baseUrl = import.meta.env.VITE_BACKEND_API_URL || '';
      const response = await axios.post(
        `${baseUrl}/operator-complete-ride/${rideId}/`,
        {},
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status === 200) {
        // Remove the completed ride from the assigned rides list
        setAssignedRides(rides => rides.filter(ride => ride.id !== rideId));

        // Show success message (optional)
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.log('Ride completed successfully');
        }

        // Optionally refresh the data to ensure consistency
        setTimeout(() => {
          fetchAssignedRides(); // Refresh the data
        }, 500);
      }
    } catch (err) {
      if (axios.isAxiosError(err)) {
        if (err.response?.status === 401) {
          logout();
        } else if (err.response?.status === 404) {
          setError('Ride not found or already completed');
        } else if (err.response?.status === 403) {
          setError('You do not have permission to complete rides');
        } else {
          setError(err.response?.data?.error || 'Failed to complete ride');
        }
      } else {
        setError('An unexpected error occurred');
      }
      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.error('Error completing ride:', err);
      }
    }
  };

  // Fetch available drivers for reassignment
  const fetchAvailableDrivers = async () => {
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.error('No authentication token found');
        }
        logout();
        return;
      }

      const baseUrl = import.meta.env.VITE_BACKEND_API_URL;
      const apiUrl = baseUrl.endsWith('/api')
        ? `${baseUrl}/drivers/`
        : `${baseUrl}/api/drivers/`;

      const response = await axios.get(apiUrl, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      // Use shared helper to transform and filter available drivers
      const transformedDrivers = getAvailableDrivers(response.data as RawDriverData[]);
      setAvailableDrivers(transformedDrivers);
    } catch (err) {
      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.error('Error fetching drivers:', err);
      }
    }
  };

  // Handle reassign ride
  const handleReassignRide = (rideId: string) => {
    setSelectedRideForReassign(rideId);
    setShowReassignModal(true);
    fetchAvailableDrivers();
  };

  // Handle confirm reassignment
  const handleConfirmReassign = async () => {
    if (!selectedRideForReassign || !selectedDriver) return;

    try {
      setIsReassigning(true);
      const token = localStorage.getItem('access_token');
      if (!token) {
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.error('No authentication token found');
        }
        logout();
        return;
      }

      const baseUrl = import.meta.env.VITE_BACKEND_API_URL;
      const apiUrl = baseUrl.endsWith('/api')
        ? `${baseUrl}/reassign-driver/${selectedRideForReassign}/`
        : `${baseUrl}/api/reassign-driver/${selectedRideForReassign}/`;

      await axios.post(apiUrl, {
        driver_id: selectedDriver
      }, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      // Close modal and refresh data
      setShowReassignModal(false);
      setSelectedRideForReassign(null);
      setSelectedDriver(null);

      // Refresh the assigned rides list
      fetchAssignedRides();

      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Ride reassigned successfully');
      }
    } catch (err) {
      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.error('Error reassigning ride:', err);
      }
    } finally {
      setIsReassigning(false);
    }
  };

  // Handle cancel ride - send back to pending
  const handleCancelRide = async (rideId: string) => {
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.error('No authentication token found');
        }
        logout();
        return;
      }

      const baseUrl = import.meta.env.VITE_BACKEND_API_URL;
      const apiUrl = baseUrl.endsWith('/api')
        ? `${baseUrl}/cancel-assigned-ride/${rideId}/`
        : `${baseUrl}/api/cancel-assigned-ride/${rideId}/`;

      await axios.post(apiUrl, {}, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      // Refresh the assigned rides list
      fetchAssignedRides();

      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Ride cancelled and sent back to pending');
      }
    } catch (err) {
      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.error('Error cancelling ride:', err);
      }
      // You might want to show an error message to the user here
    }
  };

  const handleViewDetails = (ride: AssignedRide) => {
    setDetailRide(ride);
    setShowDetails(true);
  };

  // Handle opening operator actions panel
  const handleOpenActionsPanel = (ride: AssignedRide) => {
    setSelectedRideForActions(ride);
    setShowActionsPanel(true);
  };

  // Handle closing operator actions panel
  const handleCloseActionsPanel = () => {
    setShowActionsPanel(false);
    setSelectedRideForActions(null);
  };

  const handleCloseDetails = () => {
    setShowDetails(false);
    setDetailRide(null);
  };

  // Handle toggling the action dropdown menu
  const toggleActionMenu = (rideId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    
    // If already open, close it
    if (openActionMenu === rideId) {
      setOpenActionMenu(null);
      return;
    }
    
    // Otherwise, calculate the position and open the dropdown
    const buttonRef = actionButtonRefs.current[rideId];
    if (buttonRef) {
      const rect = buttonRef.getBoundingClientRect();
      setDropdownPosition({
        top: rect.bottom + window.scrollY,
        left: rect.right - 180 + window.scrollX, // 180px is the dropdown width
        width: 180
      });
      setOpenActionMenu(rideId);
    }
  };
  
  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // If no dropdown is open, do nothing
      if (!openActionMenu) return;
      
      // Check if click is on the button that opened the dropdown
      const buttonRef = actionButtonRefs.current[openActionMenu];
      if (buttonRef && buttonRef.contains(event.target as Node)) {
        return; // Click is on the trigger button, don't close
      }
      
      // Check if click is on the current dropdown
      if (dropdownRef.current && dropdownRef.current.contains(event.target as Node)) {
        return; // Click is on the dropdown, don't close
      }
      
      // If we got here, click is outside both button and dropdown, close it
      setOpenActionMenu(null);
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [openActionMenu]);
  
  // DropdownMenu component to encapsulate dropdown functionality
  const DropdownMenu = () => {
    // Only render if we have an open menu
    if (!openActionMenu || !portalContainer.current) return null;
    
    // Find the ride that matches our open menu
    const currentRide = assignedRides.find(ride => ride.id === openActionMenu);
    if (!currentRide) return null;
    
    // Only keep essential positioning styles to prevent conflicts with SCSS
    const style = {
      position: 'absolute' as 'absolute',
      top: `${dropdownPosition.top}px`,
      left: `${dropdownPosition.left}px`,
      width: `${dropdownPosition.width}px`,
      zIndex: 9999,
    };
    
    return createPortal(
      <div 
        className="action-dropdown show"
        style={style}
        ref={dropdownRef}
      >
        <button 
          className="dropdown-btn"
          onClick={() => {
            handleReassignRide(currentRide.id);
            setOpenActionMenu(null);
          }}
          title="Reassign this ride to a different driver"
        >
          <FaExchangeAlt />
          Reassign Driver
        </button>
        
        <button 
          className="dropdown-btn"
          onClick={() => {
            window.open(`tel:${currentRide.rider_phone}`);
            setOpenActionMenu(null);
          }}
          title="Call the rider"
        >
          <FaPhone />
          Call Rider
        </button>
        
        {currentRide.driver && (
          <button 
            className="dropdown-btn"
            onClick={() => {
              window.open(`tel:${currentRide.driver.phone}`);
              setOpenActionMenu(null);
            }}
            title="Call the driver"
          >
            <FaCarSide />
            Call Driver
          </button>
        )}
        
        <button 
          className="dropdown-btn"
          onClick={() => {
            // Implementation for updating status could be added here
            if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
              console.log('Update status clicked for ride:', currentRide.id);
            }
            setOpenActionMenu(null);
          }}
          title="Update the ride status"
        >
          <FaUserClock />
          Update Status
        </button>
      </div>,
      portalContainer.current
    );
  };

  return (
    <div className="assigned-rides">
      {isLoading && (
        <div className="loading-container">
          <FaSpinner className="spinner" />
          <p>Loading assigned rides...</p>
        </div>
      )}

      {error && !isLoading && (
        <div className="error-container">
          <p className="error-message">{error}</p>
          <button className="btn btn-primary" onClick={fetchAssignedRides}>
            Try Again
          </button>
        </div>
      )}

      <div className="page-header">
        <h1>Assigned Rides</h1>
        <div className="page-actions">
          <button className="btn btn-primary" onClick={fetchAssignedRides}>
            <FaSync /> Refresh
          </button>
        </div>
      </div>

      {!isLoading && !error && assignedRides.length === 0 && (
        <div className="no-rides">
          <p>No assigned rides available.</p>
        </div>
      )}

      <div className="rides-grid">
        {!isLoading && !error && assignedRides.map((ride) => (
          <div key={ride.id} className="ride-card">
            <div className="ride-header">
              <div className="ride-id-section">
                <div className="ride-id">Ride #{ride.id}</div>
                <button
                  className="btn-icon-details"
                  onClick={() => handleViewDetails(ride)}
                  title="View ride details"
                >
                  <FaEye />
                </button>
              </div>
              {getStatusBadge(ride.status)}
            </div>

            <div className="ride-details">
              <div className="detail-row">
                <FaMapMarkerAlt className="icon pickup" />
                <div className="detail">
                  <div className="label">Pickup Location</div>
                  <div className="value">{ride.pickup}</div>
                </div>
              </div>

              <div className="driver-info">
                <div className="driver-avatar">
                  <FaUser />
                </div>
                <div className="driver-details">
                  <div className="driver-name">{ride.driver?.name || 'No driver name'}</div>
                  <div className="driver-vehicle">{ride.driver?.vehicle || 'No vehicle info'}</div>
                  {ride.driver?.phone ? (
                    <a href={`tel:${ride.driver.phone}`} className="driver-phone">
                      <FaPhone /> {ride.driver.phone}
                    </a>
                  ) : (
                    <span className="driver-phone">No phone number</span>
                  )}
                </div>
              </div>

              <div className="ride-timing">
                <div className="timing">
                  <div className="label">Assigned</div>
                  <div className="value">{ride.assignedTime}</div>
                </div>
                <div className="timing">
                  <div className="label">ETA</div>
                  <div className="value">{ride.estimatedArrival}</div>
                </div>
              </div>
            </div>

            <div className="ride-actions">
              {/* Primary Actions Row */}
              <div className="primary-actions">
                <button
                  className="btn btn-special-operator"
                  onClick={() => handleOpenActionsPanel(ride)}
                  title="View detailed actions panel with maps and contacts"
                >
                  <FiFileText />
                  <span>Actions</span>
                </button>

                {ride.status !== 'completed' && (
                  <>
                    <button
                      className="btn btn-success"
                      onClick={() => handleCompleteRide(ride.id)}
                      title="Mark this ride as completed"
                    >
                      <FiCheckCircle />
                      <span>Complete</span>
                    </button>

                    <button
                      className="btn btn-cancel"
                      onClick={() => handleCancelRide(ride.id)}
                      title="Cancel ride and send back to pending"
                    >
                      <FaTimes />
                      <span>Cancel</span>
                    </button>
                  </>
                )}
              </div>

              {/* Secondary Actions - In dropdown */}
              <div className="secondary-actions">
                <button
                  className="btn btn-outline"
                  onClick={(e) => toggleActionMenu(ride.id, e)}
                  title="More actions"
                  ref={(el) => { actionButtonRefs.current[ride.id] = el; }}
                >
                  <FaEllipsisV />
                  <span>More</span>
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>



      {showDetails && detailRide && (
        <div className="ride-details-modal">
          <div className="modal-content details-content">
            <h3>Ride Details</h3>
            <div className="ride-details-grid">
              <div className="detail-section">
                <h4>Ride Information</h4>
                <div className="detail-item">
                  <span className="label">Ride ID:</span>
                  <span className="value">{detailRide.id}</span>
                </div>
                <div className="detail-item">
                  <span className="label">Status:</span>
                  <span className="value status">{detailRide && getStatusBadge(detailRide.status)}</span>
                </div>
                <div className="detail-item">
                  <span className="label">Distance:</span>
                  <span className="value">{detailRide && `${detailRide.distance_km} km`}</span>
                </div>
                <div className="detail-item">
                  <span className="label">Fare:</span>
                  <span className="value">{detailRide && (detailRide.calculated_fare ? `$${detailRide.calculated_fare.toFixed(2)}` : 'N/A')}</span>
                </div>
                <div className="detail-item">
                  <span className="label">Scheduled For:</span>
                  <span className="value">{detailRide && detailRide.ride_datetime}</span>
                </div>
                <div className="detail-item">
                  <span className="label">Created:</span>
                  <span className="value">{detailRide && detailRide.created_at}</span>
                </div>
                <div className="detail-item">
                  <span className="label">Assigned:</span>
                  <span className="value">{detailRide && detailRide.assignedTime}</span>
                </div>
              </div>

              <div className="detail-section">
                <h4>Rider Information</h4>
                <div className="detail-item">
                  <span className="label">Name:</span>
                  <span className="value">{detailRide && detailRide.rider_name}</span>
                </div>
                <div className="detail-item">
                  <span className="label">Phone:</span>
                  <span className="value">
                    {detailRide && <a href={`tel:${detailRide.rider_phone}`}>{detailRide.rider_phone}</a>}
                  </span>
                </div>
                <div className="detail-item">
                  <span className="label">Passengers:</span>
                  <span className="value">{detailRide && detailRide.passenger_count}</span>
                </div>
                <div className="detail-item">
                  <span className="label">Luggage:</span>
                  <span className="value">{detailRide && `${detailRide.luggage_count} items`}</span>
                </div>
              </div>

              <div className="detail-section">
                <h4>Driver Information</h4>
                <div className="detail-item">
                  <span className="label">Name:</span>
                  <span className="value">{detailRide && (detailRide.driver?.name || 'No driver name')}</span>
                </div>
                <div className="detail-item">
                  <span className="label">Phone:</span>
                  <span className="value">
                    {detailRide && (detailRide.driver?.phone ? (
                      <a href={`tel:${detailRide.driver.phone}`}>{detailRide.driver.phone}</a>
                    ) : (
                      'No phone number'
                    ))}
                  </span>
                </div>
                <div className="detail-item">
                  <span className="label">Vehicle:</span>
                  <span className="value">{detailRide && (detailRide.driver?.vehicle || 'No vehicle info')}</span>
                </div>
              </div>

              <div className="detail-section">
                <h4>Location Information</h4>
                <div className="detail-item">
                  <span className="label">Pickup:</span>
                  <span className="value">{detailRide && detailRide.pickup}</span>
                </div>
                <div className="detail-item">
                  <span className="label">Dropoff:</span>
                  <span className="value">{detailRide && detailRide.dropoff}</span>
                </div>
              </div>
            </div>

            <div className="modal-actions">
              <button
                className="btn btn-primary"
                onClick={handleCloseDetails}
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Reassign Driver Modal */}
      <AssignDriverModal
        isOpen={showReassignModal}
        rideId={selectedRideForReassign ? parseInt(selectedRideForReassign, 10) : null}
        drivers={availableDrivers}
        selectedDriver={selectedDriver}
        isAssigning={isReassigning}
        onClose={() => {
          setShowReassignModal(false);
          setSelectedRideForReassign(null);
          setSelectedDriver(null);
        }}
        onSelectDriver={setSelectedDriver}
        onConfirmAssign={handleConfirmReassign}
        onRefreshDrivers={fetchAvailableDrivers}
      />

      {/* Operator Actions Panel */}
      {showActionsPanel && selectedRideForActions && (
        <OperatorActionsPanel
          ride={selectedRideForActions}
          onClose={handleCloseActionsPanel}
          onRefresh={fetchAssignedRides}
        />
      )}
      
      {/* Render dropdown menu */}
      <DropdownMenu />
    </div>
  );
};

export default AssignedRides;

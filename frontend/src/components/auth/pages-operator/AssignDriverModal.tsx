// frontend/src/components/auth/pages-operator/AssignDriverModal.tsx
import React, { useState, useEffect, useRef } from 'react';
import { FiRefreshCw, FiX, FiUser, FiPhone, FiMapPin, FiTruck, FiSearch } from 'react-icons/fi';
import '../../../styles/components/auth/pages-operator/AssignDriverModal.scss';
import { TransformedDriver } from '../../../types/driver';

//~ future when first and last name gets
//~  re-implemented to make the search work on first name or last name also.

interface AssignDriverModalProps {
  isOpen: boolean;
  rideId: number | null;
  drivers: TransformedDriver[];
  selectedDriver: number | null;
  isAssigning: boolean;
  onClose: () => void;
  onSelectDriver: (driverId: number) => void;
  onConfirmAssign: () => void;
  onRefreshDrivers: () => void;
}

const AssignDriverModal: React.FC<AssignDriverModalProps> = ({
  isOpen,
  rideId,
  drivers,
  selectedDriver,
  isAssigning,
  onClose,
  onSelectDriver,
  onConfirmAssign,
  onRefreshDrivers
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const modalRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isOpen && modalRef.current) {
      // Focus the first focusable element when modal opens
      const focusableElements = modalRef.current.querySelectorAll('button, input, select, textarea, [tabindex]:not([tabindex="-1"])');
      if (focusableElements.length > 0) {
        (focusableElements[0] as HTMLElement).focus();
      }

      // Basic focus trapping
      const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'Tab') {
          if (focusableElements.length === 0) return;
          const firstElement = focusableElements[0] as HTMLElement;
          const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

          if (e.shiftKey && document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
          } else if (!e.shiftKey && document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
          }
        }
        if (e.key === 'Escape') {
          onClose();
        }
      };

      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isOpen, onClose]);

  if (!isOpen || !rideId) return null;

  // Filter drivers based on search term
  const filteredDrivers = drivers.filter(driver =>
    driver.username.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="assign-driver-modal" role="dialog" aria-modal="true" aria-labelledby="modal-title">
      <div className="modal-overlay" onClick={onClose}></div>
      <div className="modal-content" ref={modalRef}>
        <div className="modal-header">
          <div className="modal-title">
            <h3 id="modal-title">Assign Driver to Ride #{rideId}</h3>
            <p className="modal-subtitle">Select an available driver for this ride</p>
          </div>
          <div className="modal-header-actions">
            <button 
              className="btn btn-secondary btn-sm" 
              onClick={onRefreshDrivers} 
              title="Refresh drivers list"
            >
              <FiRefreshCw /> Refresh
            </button>
            <button 
              className="btn btn-ghost btn-sm" 
              onClick={onClose}
              title="Close modal"
            >
              <FiX />
            </button>
          </div>
        </div>

        <div className="modal-body">
          <div className="drivers-section">
            <div className="section-header">
              <h4>Available Drivers ({filteredDrivers.length})</h4>
              <div className="search-bar">
                <FiSearch className="search-icon" />
                <input 
                  type="text" 
                  placeholder="Search by name..." 
                  value={searchTerm} 
                  onChange={(e) => setSearchTerm(e.target.value)} 
                />
              </div>
            </div>
            
            {filteredDrivers.length > 0 ? (
              <div className="drivers-list">
                {filteredDrivers.map(driver => (
                  <div 
                    key={driver.id} 
                    className={`driver-card ${selectedDriver === driver.id ? 'selected' : ''}`}
                    onClick={() => onSelectDriver(driver.id)}
                  >
                    <div className="driver-avatar">
                      <FiUser />
                    </div>
                    
                    <div className="driver-info">
                      <div className="driver-name">
                        <h5>{driver.username}</h5>
                        <span className="driver-id">ID: {driver.id}</span>
                      </div>

                      <div className="driver-details">
                        <div className="detail-item">
                          <FiTruck className="detail-icon" />
                          <span>{driver.profile?.vehicle_type || 'No vehicle info'}</span>
                        </div>

                        <div className="detail-item">
                          <FiPhone className="detail-icon" />
                          <span>{driver.profile?.phone_number || 'No phone'}</span>
                        </div>

                        <div className="detail-item">
                          <FiMapPin className="detail-icon" />
                          <span>{driver.profile?.current_location || 'Location unknown'}</span>
                        </div>
                      </div>
                    </div>

                    <div className="driver-status">
                    <br></br>
                      <span className="status-badge available">Available</span>
                    </div>
                    
                    {selectedDriver === driver.id && (
                      <div className="selected-indicator">
                        <div className="checkmark">✓</div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="empty-state">
                <div className="empty-icon">🚗</div>
                <h4>No Drivers Available</h4>
                <p>There are currently no available drivers. Please try refreshing or check back later.</p>
                <button 
                  className="btn btn-outline" 
                  onClick={onRefreshDrivers}
                >
                  <FiRefreshCw /> Refresh Drivers
                </button>
              </div>
            )}
          </div>
        </div>

        <div className="modal-footer">
          <div className="footer-info">
            {selectedDriver && (
              <span className="selection-info">
                Driver selected: {drivers.find(d => d.id === selectedDriver)?.username}
              </span>
            )}
          </div>
          
          <div className="footer-actions">
            <button 
              type="button" 
              className="btn btn-secondary" 
              onClick={onClose}
              disabled={isAssigning}
            >
              Cancel
            </button>
            <button 
              type="button" 
              className="btn btn-primary" 
              onClick={onConfirmAssign}
              disabled={!selectedDriver || isAssigning}
            >
              {isAssigning ? (
                <>
                  <div className="spinner"></div>
                  Assigning...
                </>
              ) : (
                'Assign Driver'
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AssignDriverModal;

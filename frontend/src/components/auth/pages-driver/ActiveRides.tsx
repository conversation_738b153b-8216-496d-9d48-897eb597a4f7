import React, { useState, useCallback, useEffect } from 'react';
import { FiRefresh<PERSON><PERSON>, <PERSON><PERSON>opy, FiPhone } from 'react-icons/fi';
import { FaCheckCircle, FaTimesCircle, FaTrash, FaRoute } from 'react-icons/fa';
import '../../../styles/components/auth/pages-driver/ActiveRides.scss';
import DriverNav from './DriverNav';
import axios from 'axios';
import { useAuth } from '../AuthContext';
import { useSilentPolling } from '../../../types/useSilentPolling';

interface RideRequest {
  id: number;
  rider_name: string;
  pickup_location: string;
  dropoff_location: string;
  ride_datetime: string;
  passenger_count: number;
  luggage_count: number;
  distance_km: string;
  rider_phone: string;
  calculated_fare: string | null;
  created_at: string;
  status: string;
  driver?: number | null;
  driver_name?: string | null;
  completed_at?: string | null;
}

const ActiveRides: React.FC = () => {
  const [activeRides, setActiveRides] = useState<RideRequest[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState<number | null>(null);
  const { logout } = useAuth();

  // Toast state
  const [toastMessage, setToastMessage] = useState<string>('');
  const [showToast, setShowToast] = useState<boolean>(false);

  // Fetch active rides assigned to the current driver (with loading state)
  const fetchActiveRides = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        logout();
        return;
      }

      const baseUrl = import.meta.env.VITE_BACKEND_API_URL;
      const apiUrl = baseUrl.endsWith('/api')
        ? `${baseUrl}/ride-requests?type=accepted`
        : `${baseUrl}/api/ride-requests?type=accepted`;

      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Fetching driver active rides from:', apiUrl);
      }

      const response = await axios.get<RideRequest[]>(apiUrl, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Driver active rides response:', response.data.length, 'rides');
      }

      setActiveRides(response.data);
      setError(null);
    } catch (err) {
      if (axios.isAxiosError(err)) {
        if (err.response?.status === 401) {
          logout();
          return;
        } else if (err.response?.status === 304) {
          // Not Modified - data is still fresh
          if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
            console.log('Active rides data is still fresh (304)');
          }
          return;
        }
      }

      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.error('Error fetching active rides:', err);
      }
      setError('Failed to fetch active rides. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [logout]);

  // Silent fetch function (no loading state changes)
  const fetchActiveRidesSilently = useCallback(async () => {
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        logout();
        return;
      }

      const baseUrl = import.meta.env.VITE_BACKEND_API_URL;
      const apiUrl = baseUrl.endsWith('/api')
        ? `${baseUrl}/ride-requests?type=accepted`
        : `${baseUrl}/api/ride-requests?type=accepted`;

      const response = await axios.get<RideRequest[]>(apiUrl, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      // Only update state if data actually changed
      setActiveRides(prevRides => {
        const newRides = response.data;
        if (JSON.stringify(prevRides) !== JSON.stringify(newRides)) {
          if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
            console.log('Silent polling: Active rides updated');
          }
          return newRides;
        }
        return prevRides;
      });

      setError(null);
    } catch (err) {
      if (axios.isAxiosError(err)) {
        if (err.response?.status === 401) {
          logout();
          return;
        } else if (err.response?.status === 304) {
          // Not Modified - data is still fresh, this is expected
          if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
            console.log('Silent polling: Data is still fresh (304)');
          }
          return;
        }
      }

      // Don't show errors for silent polling unless it's critical
      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Silent polling error (non-critical):', err);
      }
    }
  }, [logout]);

  const handleFinishRide = async (rideId: number) => {
    setActionLoading(rideId);
    setError(null);
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        logout();
        return;
      }

      const baseUrl = import.meta.env.VITE_BACKEND_API_URL;
      const apiUrl = baseUrl.endsWith('/api')
        ? `${baseUrl}/finish-ride/${rideId}/`
        : `${baseUrl}/api/finish-ride/${rideId}/`;

      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Finishing ride:', rideId);
      }

      await axios.post(apiUrl, {}, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      });

      // Show success toast
      setToastMessage('Ride completed successfully!');
      setShowToast(true);
      setTimeout(() => setShowToast(false), 3000);

      // Refresh the rides list
      setTimeout(() => {
        fetchActiveRides(); // Refresh the data
      }, 500);
    } catch (err) {
      if (axios.isAxiosError(err) && err.response?.status === 401) {
        logout();
      } else {
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.error('Error finishing ride:', err);
        }
        setError('Failed to finish ride. Please try again.');
      }
    } finally {
      setActionLoading(null);
    }
  };

  const handleCancelRide = async (rideId: number) => {
    if (!confirm('Are you sure you want to cancel this ride?')) {
      return;
    }

    setActionLoading(rideId);
    setError(null);
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        logout();
        return;
      }

      const baseUrl = import.meta.env.VITE_BACKEND_API_URL;
      const apiUrl = baseUrl.endsWith('/api')
        ? `${baseUrl}/cancel-ride/${rideId}/`
        : `${baseUrl}/api/cancel-ride/${rideId}/`;

      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Cancelling ride:', rideId);
      }

      await axios.post(apiUrl, {}, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      });

      // Show success toast
      setToastMessage('Ride cancelled successfully!');
      setShowToast(true);
      setTimeout(() => setShowToast(false), 3000);

      // Refresh the rides list
      setTimeout(() => {
        fetchActiveRides(); // Refresh the data
      }, 500);
    } catch (err) {
      if (axios.isAxiosError(err) && err.response?.status === 401) {
        logout();
      } else {
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.error('Error cancelling ride:', err);
        }
        setError('Failed to cancel ride. Please try again.');
      }
    } finally {
      setActionLoading(null);
    }
  };

  const handleDeleteRide = async (rideId: number) => {
    if (!confirm('Are you sure you want to delete this ride? This action cannot be undone.')) {
      return;
    }

    setActionLoading(rideId);
    setError(null);
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        logout();
        return;
      }

      const baseUrl = import.meta.env.VITE_BACKEND_API_URL;
      const apiUrl = baseUrl.endsWith('/api')
        ? `${baseUrl}/delete-ride/${rideId}/`
        : `${baseUrl}/api/delete-ride/${rideId}/`;

      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Deleting ride:', rideId);
      }

      await axios.post(apiUrl, {}, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      });

      // Show success toast
      setToastMessage('Ride deleted successfully!');
      setShowToast(true);
      setTimeout(() => setShowToast(false), 3000);

      // Refresh the rides list
      setTimeout(() => {
        fetchActiveRides(); // Refresh the data
      }, 500);
    } catch (err) {
      if (axios.isAxiosError(err) && err.response?.status === 401) {
        logout();
      } else {
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.error('Error deleting ride:', err);
        }
        setError('Failed to delete ride. Please try again.');
      }
    } finally {
      setActionLoading(null);
    }
  };

  const handleGetDirections = (location: string) => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        ({ coords }) => {
          const origin = `${coords.latitude},${coords.longitude}`;
          const destination = encodeURIComponent(location);
          const url = `https://www.google.com/maps/dir/?api=1&origin=${origin}&destination=${destination}`;
          window.open(url, '_blank');
        },
        (error) => {
          console.error('Error getting location:', error);
          alert('Unable to get your current location. Please ensure location services are enabled and try again.');
        }
      );
    } else {
      alert('Geolocation is not supported by your browser.');
    }
  };

  const copyToClipboard = (text: string) => {
    const showCopiedToast = () => {
      setToastMessage('Phone number copied!');
      setShowToast(true);
      setTimeout(() => setShowToast(false), 2500);
    };

    if (navigator.clipboard) {
      navigator.clipboard.writeText(text)
        .then(showCopiedToast)
        .catch((err) => {
          if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
            console.error('Failed to copy: ', err);
          }
        });
    } else {
      const textarea = document.createElement('textarea');
      textarea.value = text;
      document.body.appendChild(textarea);
      textarea.select();
      try {
        document.execCommand('copy');
        showCopiedToast();
      } catch (err) {
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.error('Failed to copy: ', err);
        }
      } finally {
        document.body.removeChild(textarea);
      }
    }
  };

  const callPhoneNumber = (phoneNumber: string) => {
    window.location.href = `tel:${phoneNumber}`;
  };

  // Google Maps helper function for full route
  const getFullRouteUrl = (pickup: string, dropoff: string) => {
    const origin = encodeURIComponent(pickup);
    const destination = encodeURIComponent(dropoff);
    return `https://www.google.com/maps/dir/?api=1&origin=${origin}&destination=${destination}`;
  };

  // Silent polling for active rides - same pattern as operator dashboard
  const manualRefresh = useSilentPolling(
    fetchActiveRidesSilently,
    fetchActiveRides, // Initial fetch with loading state
    30000, // Poll every 30 seconds for driver rides
    10000, // Random offset between -5s and +5s (25-35 seconds)
    [logout]
  );

  useEffect(() => {
    // No need for manual interval setup, handled by useSilentPolling
    return () => {}; // Empty return for consistency, cleanup handled by hook
  }, []);

  if (loading) return <div className="spinner"></div>;
  if (error) return (
    <div className="error-container">
      <p className="error-message">{error}</p>
      <button className="retry-button" onClick={fetchActiveRides}>Retry</button>
    </div>
  );

  return (
    <div>
      <DriverNav />
      <div className="dashboard-container">

        {/* —— toast popup —— */}
        {showToast && (
          <div className="copy-toast">
            {toastMessage}
          </div>
        )}

        <br />
        <div className="dashboard-header">
          <h1>My Active Rides</h1>
          <button className="refresh-button" onClick={manualRefresh} title="Refresh rides">
            <FiRefreshCw />
          </button>
        </div>

        {activeRides.length === 0 ? (
          <p>No active rides available.</p>
        ) : (
          <div className="active-ride-cards">
            {activeRides.map((ride) => (
              <div className="active-ride-card" key={ride.id}>
                <div className="card-header">
                  <h2>{ride.rider_name}</h2>
                  <div className="phone-section">
                    <span className="label">Phone:</span>
                    <span className="value">{ride.rider_phone}</span>
                    <button
                      className="icon-button copy-button"
                      onClick={() => copyToClipboard(ride.rider_phone)}
                      title="Copy phone number"
                      aria-label="Copy phone number"
                    >
                      <FiCopy />
                    </button>
                    <button
                      className="icon-button call-button"
                      onClick={() => callPhoneNumber(ride.rider_phone)}
                      title="Call rider"
                      aria-label="Call rider"
                    >
                      <FiPhone />
                    </button>
                  </div>
                </div>

                <div className="card-body">
                  <div className="card-row">
                    <span className="label">Pickup</span>
                    <span className="value">{ride.pickup_location}</span>
                    <div className="buttons">
                      <button onClick={() => handleGetDirections(ride.pickup_location)}>
                        Get Directions
                      </button>
                    </div>
                  </div>
                  <div className="card-row">
                    <span className="label">Drop-off</span>
                    <span className="value">{ride.dropoff_location}</span>
                    <div className="buttons">
                      <button onClick={() => handleGetDirections(ride.dropoff_location)}>
                        Dropoff Help
                      </button>
                    </div>
                  </div>
                  <div className="card-row">
                    <span className="label">When</span>
                    <span className="value">{new Date(ride.ride_datetime).toLocaleString()}</span>
                  </div>
                  <div className="card-row">
                    <span className="label">Passengers</span>
                    <span className="value">{ride.passenger_count}</span>
                  </div>
                  <div className="card-row">
                    <span className="label">Luggage</span>
                    <span className="value">{ride.luggage_count}</span>
                  </div>
                  <div className="card-row">
                    <span className="label">Distance</span>
                    <span className="value">{parseFloat(ride.distance_km).toFixed(2)} km</span>
                  </div>
                  <div className="card-row">
                    <span className="label">Fare</span>
                    <span className="value">
                      {ride.calculated_fare ? `$${parseFloat(ride.calculated_fare).toFixed(2)}` : 'N/A'}
                    </span>
                  </div>
                  <div className="card-row">
                    <span className="label">Requested</span>
                    <span className="value">{new Date(ride.created_at).toLocaleString()}</span>
                  </div>
                  <div className="card-row">
                    <span className="label">Status</span>
                    <span className="value">{ride.status}</span>
                  </div>
                  <div className="card-row full-route-row">
                    <span className="label">Full Route</span>
                    <span className="value">Complete pickup to dropoff route</span>
                    <div className="buttons">
                      <a
                        href={getFullRouteUrl(ride.pickup_location, ride.dropoff_location)}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="map-link-button route-button"
                        title="View complete route in Google Maps"
                      >
                        <FaRoute /> View Full Route
                      </a>
                    </div>
                  </div>
                </div>

                <div className="card-actions">
                  <button
                    className="action-finish"
                    onClick={() => handleFinishRide(ride.id)}
                    disabled={actionLoading === ride.id}
                  >
                    {actionLoading === ride.id ? (
                      <>
                        <FaCheckCircle /> Completing...
                      </>
                    ) : (
                      <>
                        <FaCheckCircle /> Finish Ride
                      </>
                    )}
                  </button>
                  <button
                    className="action-cancel"
                    onClick={() => handleCancelRide(ride.id)}
                    disabled={actionLoading === ride.id}
                  >
                    {actionLoading === ride.id ? (
                      <>
                        <FaTimesCircle /> Cancelling...
                      </>
                    ) : (
                      <>
                        <FaTimesCircle /> Cancel Ride
                      </>
                    )}
                  </button>
                  <button
                    className="action-delete"
                    onClick={() => handleDeleteRide(ride.id)}
                    disabled={actionLoading === ride.id}
                  >
                    {actionLoading === ride.id ? (
                      <>
                        <FaTrash /> Deleting...
                      </>
                    ) : (
                      <>
                        <FaTrash /> Delete Ride
                      </>
                    )}
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ActiveRides;

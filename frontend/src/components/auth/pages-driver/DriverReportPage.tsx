import React, { useState, useEffect, useCallback } from 'react';
import axios from 'axios';
import { useAuth } from '../AuthContext';
import DriverNav from './DriverNav';
import { FiRefreshCw } from 'react-icons/fi';
import { FaDollarSign, FaStar, FaCar, FaCheckCircle, FaCalendarAlt, FaChartLine, FaMapMarkerAlt } from 'react-icons/fa';
import '../../../styles/components/auth/pages-driver/DriverReportPage.scss';
import { useSilentPolling } from '../../../types/useSilentPolling';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ChartData,
} from 'chart.js';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

// Define interfaces for API response
interface DriverProfile {
  username: string;
  license_number: string | null;
  vehicle_type: string | null;
  approval_date: string | null;
  current_location: string | null;
}

interface RideTypesBreakdown {
  economy: number;
  comfort: number;
  xl: number;
  cargo: number;
}

interface EarningsTrend {
  labels: string[];
  data: number[];
}

interface RideStatistics {
  total_trips: number;
  total_earnings: number;
  total_distance: number;
  completion_rate: number;
  acceptance_rate: number;
  average_fare: number;
  ride_types_breakdown: RideTypesBreakdown;
  earnings_trend: EarningsTrend;
}

interface DriverReportData {
  profile: DriverProfile;
  ride_statistics: RideStatistics;
}

const DriverReport: React.FC = () => {
  const [driverStats, setDriverStats] = useState<DriverReportData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [period, setPeriod] = useState<'daily' | 'weekly' | 'monthly' | 'yearly' | 'all'>('weekly');
  const { logout } = useAuth();

  const fetchDriverStats = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        logout();
        return;
      }

      const baseUrl = import.meta.env.VITE_BACKEND_API_URL;
      const apiUrl = baseUrl.endsWith('/api')
        ? `${baseUrl}/driver-report/?period=${period}`
        : `${baseUrl}/api/driver-report/?period=${period}`;

      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Fetching driver stats from:', apiUrl);
      }

      const response = await axios.get<DriverReportData>(apiUrl, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      setDriverStats(response.data);
      setError(null);
    } catch (err) {
      if (axios.isAxiosError(err)) {
        if (err.response?.status === 401) {
          logout();
          return;
        } else if (err.response?.status === 304) {
          if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
            console.log('Driver stats data is still fresh (304)');
          }
          return;
        }
      }
      
      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.error('Error fetching driver stats:', err);
      }
      setError('Failed to fetch driver statistics. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [logout, period]);

  // Silent fetch function (no loading state changes)
  const fetchDriverStatsSilently = useCallback(async () => {
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        logout();
        return;
      }

      const baseUrl = import.meta.env.VITE_BACKEND_API_URL;
      const apiUrl = baseUrl.endsWith('/api')
        ? `${baseUrl}/driver-report/?period=${period}`
        : `${baseUrl}/api/driver-report/?period=${period}`;

      const response = await axios.get<DriverReportData>(apiUrl, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      setDriverStats(prevStats => {
        if (JSON.stringify(prevStats) !== JSON.stringify(response.data)) {
          if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
            console.log('Silent polling: Driver stats updated');
          }
          return response.data;
        }
        return prevStats;
      });

      setError(null);
    } catch (err) {
      if (axios.isAxiosError(err)) {
        if (err.response?.status === 401) {
          logout();
          return;
        } else if (err.response?.status === 304) {
          if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
            console.log('Silent polling: Driver stats data is still fresh (304)');
          }
          return;
        }
      }
      
      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Silent polling error (non-critical):', err);
      }
    }
  }, [logout, period]);

  // Silent polling for driver stats
  const manualRefresh = useSilentPolling(
    fetchDriverStatsSilently,
    fetchDriverStats,
    60000,
    20000,
    [logout, period]
  );

  useEffect(() => {
    fetchDriverStats();
  }, [fetchDriverStats, period]);

  const getChartData = (): ChartData<'line'> => {
    if (!driverStats?.ride_statistics.earnings_trend) {
      return {
        labels: [],
        datasets: [{
          label: 'Earnings',
          data: [],
          borderColor: '#2ecc71',
          tension: 0.4,
        }]
      };
    }

    const { labels, data } = driverStats.ride_statistics.earnings_trend;

    return {
      labels,
      datasets: [{
        label: 'Earnings',
        data,
        borderColor: '#2ecc71',
        backgroundColor: 'rgba(46, 204, 113, 0.1)',
        tension: 0.4,
        fill: true,
      }]
    };
  };

  if (loading) return <div className="spinner"></div>;
  if (error) return (
    <div className="error-container">
      <p className="error-message">{error}</p>
      <button className="retry-button" onClick={fetchDriverStats}>Retry</button>
    </div>
  );

  return (
    <div>
      <DriverNav />
      <div className="driver-report-container">
        <div className="report-header">
          <h1>My Dashboard</h1>
          <div className="header-actions">
            <select 
              value={period} 
              onChange={(e) => setPeriod(e.target.value as 'daily' | 'weekly' | 'monthly' | 'yearly' | 'all')}
              className="period-selector"
            >
              <option value="daily">Today</option>
              <option value="weekly">This Week</option>
              <option value="monthly">This Month</option>
              <option value="yearly">This Year</option>
              <option value="all">All Time</option>
            </select>
            <button className="refresh-button" onClick={manualRefresh} title="Refresh stats">
              <FiRefreshCw />
            </button>
          </div>
        </div>

        {driverStats && (
          <div className="dashboard-content">
            {/* Welcome Section */}
            <div className="welcome-section">
              <div className="welcome-header">
                <h2>Welcome back, {driverStats.profile.username}!</h2>
                <div className="driver-info">
                  {driverStats.profile.vehicle_type && (
                    <span className="vehicle-type">
                      <FaCar /> {driverStats.profile.vehicle_type}
                    </span>
                  )}
                  {driverStats.profile.current_location && (
                    <span className="current-location">
                      <FaMapMarkerAlt /> {driverStats.profile.current_location}
                    </span>
                  )}
                </div>
              </div>
            </div>

            {/* Main Stats Grid */}
            <div className="dashboard-cards">
              <div className="metric-card earnings">
                <div className="card-icon">
                  <FaDollarSign />
                </div>
                <div className="card-content">
                  <h3>Total Earnings</h3>
                  <p className="metric-value">${driverStats.ride_statistics.total_earnings.toFixed(2)}</p>
                </div>
              </div>

              <div className="metric-card trips">
                <div className="card-icon">
                  <FaCar />
                </div>
                <div className="card-content">
                  <h3>Total Trips</h3>
                  <p className="metric-value">{driverStats.ride_statistics.total_trips}</p>
                </div>
              </div>

              <div className="metric-card distance">
                <div className="card-icon">
                  <FaCalendarAlt />
                </div>
                <div className="card-content">
                  <h3>Distance Driven</h3>
                  <p className="metric-value">{driverStats.ride_statistics.total_distance.toFixed(1)} km</p>
                </div>
              </div>

              <div className="metric-card average">
                <div className="card-icon">
                  <FaStar />
                </div>
                <div className="card-content">
                  <h3>Average Fare</h3>
                  <p className="metric-value">
                    ${driverStats.ride_statistics.average_fare.toFixed(2)}
                  </p>
                </div>
              </div>

              <div className="metric-card completion">
                <div className="card-icon">
                  <FaCheckCircle />
                </div>
                <div className="card-content">
                  <h3>Completion Rate</h3>
                  <p className="metric-value">
                    {driverStats.ride_statistics.completion_rate.toFixed(1)}%
                  </p>
                </div>
              </div>

              <div className="metric-card acceptance">
                <div className="card-icon">
                  <FaCheckCircle />
                </div>
                <div className="card-content">
                  <h3>Acceptance Rate</h3>
                  <p className="metric-value">
                    {driverStats.ride_statistics.acceptance_rate.toFixed(1)}%
                  </p>
                </div>
              </div>
            </div>

            {/* Earnings Chart */}
            <div className="chart-section">
              <h3><FaChartLine /> Earnings Trend</h3>
              <div className="chart-container">
                <Line 
                  data={getChartData()} 
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        display: false
                      },
                      tooltip: {
                        callbacks: {
                          label: (context) => `$${context.parsed.y.toFixed(2)}`
                        }
                      }
                    },
                    scales: {
                      y: {
                        beginAtZero: true,
                        ticks: {
                          callback: (value) => `$${value}`
                        }
                      }
                    }
                  }}
                />
              </div>
            </div>

            {/* Ride Types Breakdown */}
            <div className="ride-types-section">
              <h3>Ride Types Breakdown</h3>
              <div className="ride-types-grid">
                {Object.entries(driverStats.ride_statistics.ride_types_breakdown).map(([type, count]) => (
                  <div key={type} className={`ride-type-card ${type.toLowerCase()}`}>
                    <h4>{type.charAt(0).toUpperCase() + type.slice(1)}</h4>
                    <p>{count} rides</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Tips Section */}
            <div className="tips-section">
              <h3>💡 Performance Tips</h3>
              <ul>
                <li>Keep your acceptance rate above 80% for better ride offers</li>
                <li>Complete rides to maintain a high completion rate</li>
                <li>Check your active rides regularly for new assignments</li>
                <li>Keep your vehicle information updated</li>
                <li>Stay active in high-demand areas during peak hours</li>
              </ul>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DriverReport;

// frontend/src/components/auth/pages-driver/DriverNav.tsx
import React from 'react';
import { NavLink } from 'react-router-dom';
import '../../../styles/components/auth/pages-driver/DriverNav.scss';

const DriverNav: React.FC = () => {
  return (
    <nav className="driver-nav">
      <NavLink to="/driverpanel" className={({ isActive }) => (isActive ? 'active' : '')}>
        Ride Requests
      </NavLink>
      <NavLink to="/activerides" className={({ isActive }) => (isActive ? 'active' : '')}>
        Accepted Rides
      </NavLink>
      <NavLink to="/driverhistory" className={({ isActive }) => (isActive ? 'active' : '')}>
        Drivers History
      </NavLink>
      <NavLink to="/driverreport" className={({ isActive }) => (isActive ? 'active' : '')}>
        Reports
      </NavLink>
      <NavLink to="/wip" className={({ isActive }) => (isActive ? 'active' : '')}>
        My Profile
      </NavLink>
      <NavLink to="/" className={({ isActive }) => (isActive ? 'active' : '')}>
        Quit
      </NavLink>
    </nav>
  );
};

export default DriverNav;
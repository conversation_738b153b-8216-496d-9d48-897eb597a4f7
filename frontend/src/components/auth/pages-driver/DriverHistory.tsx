import React, { useState, useEffect } from 'react';
import { FiR<PERSON><PERSON><PERSON><PERSON>, <PERSON>Search, FiX, FiArrowUp, FiArrowDown } from 'react-icons/fi';
import '../../../styles/components/auth/pages-driver/DriverHistory.scss';
import DriverNav from './DriverNav';
import axios from 'axios';
import { useAuth } from '../AuthContext';

interface RideRequest {
    id: number;
    rider_name: string;
    pickup_location: string;
    dropoff_location: string;
    ride_datetime: string;
    passenger_count: number;
    luggage_count: number;
    distance_km: string;
    rider_phone: string;
    calculated_fare: string | null;
    created_at: string;
    status: string;
    driver?: number | null;
    driver_name?: string | null;
    completed_at?: string | null;
}

type SortColumn = 'name' | 'phone' | 'date' | 'completed' | 'fare' | 'status';
type SortOrder = 'asc' | 'desc';

const DriverHistory: React.FC = () => {
    const [completedRides, setCompletedRides] = useState<RideRequest[]>([]);
    const [searchQuery, setSearchQuery] = useState<string>('');
    const [selectedRide, setSelectedRide] = useState<RideRequest | null>(null);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const [sortColumn, setSortColumn] = useState<SortColumn>('date');
    const [sortOrder, setSortOrder] = useState<SortOrder>('desc');
    const { logout } = useAuth();

    const fetchCompletedRides = async () => {
        setLoading(true);
        setError(null);
        try {
            const apiUrl = `${import.meta.env.VITE_BACKEND_API_URL}/ride-requests?type=completed`;
            const response = await axios.get(apiUrl, {
                headers: {
                    Authorization: `Bearer ${localStorage.getItem('access_token')}`,
                },
            });
            const rides = response.data;
            // Default sort: latest to oldest by ride_datetime
            rides.sort((a: RideRequest, b: RideRequest) => new Date(b.ride_datetime).getTime() - new Date(a.ride_datetime).getTime());
            setCompletedRides(rides);
            setError(null);
        } catch (err) {
            if (axios.isAxiosError(err) && err.response?.status === 401) {
                logout();
            } else {
                setError('Failed to fetch completed rides. Please try again.');
            }
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchCompletedRides();
    }, []);

    const handleSort = (column: SortColumn) => {
        if (sortColumn === column) {
            setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
        } else {
            setSortColumn(column);
            setSortOrder('asc');
        }
    };

    // First, filter the rides based on the search query
    const filteredRides = searchQuery
        ? completedRides.filter(ride => {
            const searchLower = searchQuery.toLowerCase();
            return (
                ride.rider_name.toLowerCase().includes(searchLower) ||
                ride.pickup_location.toLowerCase().includes(searchLower) ||
                ride.rider_phone.toLowerCase().includes(searchLower) ||
                ride.dropoff_location.toLowerCase().includes(searchLower) ||
                new Date(ride.ride_datetime).toLocaleString().toLowerCase().includes(searchLower)
            );
        })
        : completedRides;

    // Then, sort the filtered rides based on the current sort column and order
    const sortedRides = [...filteredRides].sort((a, b) => {
        let comparison = 0;
        switch (sortColumn) {
            case 'name':
                comparison = a.rider_name.localeCompare(b.rider_name);
                break;
            case 'phone':
                comparison = a.rider_phone.localeCompare(b.rider_phone);
                break;
            case 'date':
                comparison = new Date(a.ride_datetime).getTime() - new Date(b.ride_datetime).getTime();
                break;
            case 'completed':
                comparison = (a.completed_at ? new Date(a.completed_at).getTime() : 0) - (b.completed_at ? new Date(b.completed_at).getTime() : 0);
                break;
            case 'fare':
                comparison = (a.calculated_fare ? parseFloat(a.calculated_fare) : 0) - (b.calculated_fare ? parseFloat(b.calculated_fare) : 0);
                break;
            case 'status':
                comparison = a.status.localeCompare(b.status);
                break;
        }
        return sortOrder === 'asc' ? comparison : -comparison;
    });

    if (loading) return <div className="spinner"></div>;
    if (error) return (
        <div className="error-container">
            <p className="error-message">{error}</p>
            <button className="retry-button" onClick={fetchCompletedRides}>Retry</button>
        </div>
    );

    return (
        <div>
            <DriverNav />
            <div className="driver-history-container">
                <br />
                <div className="history-header">
                    <h1>Completed Rides</h1>
                    <button className="refresh-button" onClick={fetchCompletedRides}>
                        <FiRefreshCw />
                    </button>
                </div>
                {completedRides.length === 0 ? (
                    <p>No completed rides available.</p>
                ) : (
                    <>
                        <div className="search-container sticky-header">
                            <input
                                type="text"
                                placeholder="Search rides by name, location, phone or date..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                            />
                            <FiSearch className="search-icon" />
                            {searchQuery && (
                                <button
                                    className="clear-search-button"
                                    onClick={() => setSearchQuery('')}
                                >
                                    Clear
                                </button>
                            )}
                        </div>
                        {filteredRides.length === 0 ? (
                            <p>No rides match your search.</p>
                        ) : (
                            <div className="ride-list-container">
                                <table className="ride-list-table">
                                    <thead>
                                        <tr className="ride-list-header">
                                            <th className="header-name" onClick={() => handleSort('name')}>
                                                Name {sortColumn === 'name' && (sortOrder === 'asc' ? <FiArrowUp /> : <FiArrowDown />)}
                                            </th>
                                            <th className="header-phone" onClick={() => handleSort('phone')}>
                                                Phone {sortColumn === 'phone' && (sortOrder === 'asc' ? <FiArrowUp /> : <FiArrowDown />)}
                                            </th>
                                            <th className="header-date" onClick={() => handleSort('date')}>
                                                Requested {sortColumn === 'date' && (sortOrder === 'asc' ? <FiArrowUp /> : <FiArrowDown />)}
                                            </th>
                                            <th className="header-completed" onClick={() => handleSort('completed')}>
                                                Completed {sortColumn === 'completed' && (sortOrder === 'asc' ? <FiArrowUp /> : <FiArrowDown />)}
                                            </th>
                                            <th className="header-fare" onClick={() => handleSort('fare')}>
                                                Fare {sortColumn === 'fare' && (sortOrder === 'asc' ? <FiArrowUp /> : <FiArrowDown />)}
                                            </th>
                                            <th className="header-status" onClick={() => handleSort('status')}>
                                                Status {sortColumn === 'status' && (sortOrder === 'asc' ? <FiArrowUp /> : <FiArrowDown />)}
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="ride-history-list">
                                        {sortedRides.map(ride => (
                                            <tr
                                                className="ride-list-item"
                                                key={ride.id}
                                                onClick={() => setSelectedRide(ride)}
                                            >
                                                <td className="rider-name">{ride.rider_name}</td>
                                                <td className="rider-phone">{ride.rider_phone}</td>
                                                <td className="ride-date">
                                                    {new Date(ride.ride_datetime).toLocaleDateString()} {new Date(ride.ride_datetime).toLocaleTimeString([], {
                                                        hour: '2-digit',
                                                        minute: '2-digit',
                                                    })}
                                                </td>
                                                <td className="ride-completed">
                                                    {ride.completed_at ? `${new Date(ride.completed_at).toLocaleDateString()} ${new Date(ride.completed_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}` : 'N/A'}
                                                </td>
                                                <td className="ride-fare">
                                                    {ride.calculated_fare ? `$${parseFloat(ride.calculated_fare).toFixed(2)}` : 'N/A'}
                                                </td>
                                                <td className="ride-status">{ride.status}</td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        )}
                        {selectedRide && (
                            <div
                                className="ride-details-modal"
                                onClick={(e) => {
                                    if (e.target instanceof HTMLElement && e.target.classList.contains('ride-details-modal')) {
                                        setSelectedRide(null);
                                    }
                                }}
                            >
                                <div className="modal-content">
                                    <button
                                        className="close-button"
                                        onClick={() => setSelectedRide(null)}
                                        aria-label="Close modal"
                                    >
                                        <FiX />
                                    </button>
                                    <h2>{selectedRide.rider_name}</h2>
                                    <div className="section">
                                        <h3>Contact</h3>
                                        <div className="detail-row">
                                            <span className="label">Phone:</span>
                                            <span className="value">{selectedRide.rider_phone}</span>
                                        </div>
                                    </div>
                                    <div className="section">
                                        <h3>Ride Details</h3>
                                        <div className="detail-row">
                                            <span className="label">Pickup:</span>
                                            <span className="value">{selectedRide.pickup_location}</span>
                                        </div>
                                        <div className="detail-row">
                                            <span className="label">Drop-off:</span>
                                            <span className="value">{selectedRide.dropoff_location}</span>
                                        </div>
                                        <div className="detail-row">
                                            <span className="label">When:</span>
                                            <span className="value">{new Date(selectedRide.ride_datetime).toLocaleString()}</span>
                                        </div>
                                        <div className="detail-row">
                                            <span className="label">Passengers:</span>
                                            <span className="value">{selectedRide.passenger_count}</span>
                                        </div>
                                        <div className="detail-row">
                                            <span className="label">Luggage:</span>
                                            <span className="value">{selectedRide.luggage_count}</span>
                                        </div>
                                    </div>
                                    <div className="section">
                                        <h3>Trip Information</h3>
                                        <div className="detail-row">
                                            <span className="label">Distance:</span>
                                            <span className="value">{parseFloat(selectedRide.distance_km).toFixed(2)} km</span>
                                        </div>
                                        <div className="detail-row">
                                            <span className="label">Fare:</span>
                                            <span className="value">{selectedRide.calculated_fare ? `$${parseFloat(selectedRide.calculated_fare).toFixed(2)}` : 'N/A'}</span>
                                        </div>
                                    </div>
                                    <div className="section">
                                        <h3>Request Information</h3>
                                        <div className="detail-row">
                                            <span className="label">Requested:</span>
                                            <span className="value">{new Date(selectedRide.created_at).toLocaleString()}</span>
                                        </div>
                                        <div className="detail-row">
                                            <span className="label">Completed:</span>
                                            <span className="value">{selectedRide.completed_at ? new Date(selectedRide.completed_at).toLocaleString() : 'N/A'}</span>
                                        </div>
                                        <div className="detail-row">
                                            <span className="label">Status:</span>
                                            <span className="value">{selectedRide.status}</span>
                                        </div>
                                    </div>
                                    <button className="close-modal-button" onClick={() => setSelectedRide(null)}>
                                        Close
                                    </button>
                                </div>
                            </div>
                        )}
                    </>
                )}
            </div>
        </div>
    );
};

export default DriverHistory;
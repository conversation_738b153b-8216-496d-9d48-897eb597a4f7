// frontend/src/components/auth/pages/DashboardDriver.tsx
import React, { useState, useEffect, useCallback } from 'react';
import { FiRefreshCw } from 'react-icons/fi';
import '../../../styles/components/auth/pages-driver/DashBoardDriver.scss';
import DriverNav from './DriverNav';
import axios from 'axios';
import { useAuth } from '../AuthContext';
import { useSilentPolling } from '../../../types/useSilentPolling';

interface RideRequest {
  id: number;
  rider_name: string;
  pickup_location: string;
  dropoff_location: string;
  ride_datetime: string;
  passenger_count: number;
  luggage_count: number;
  distance_km: string;
  rider_phone: string;
  calculated_fare: string | null;
  created_at: string;
  status: string;
  driver?: number | null;
  driver_name?: string | null;
}

const DashboardDriver: React.FC = () => {
  const [rideRequests, setRideRequests] = useState<RideRequest[]>([]);
  const [rejectedRideIds, setRejectedRideIds] = useState<Set<number>>(new Set());
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState<number | null>(null);
  const { logout } = useAuth();

  // Fetch pending ride requests (with loading state)
  const fetchRideRequests = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        logout();
        return;
      }

      const baseUrl = import.meta.env.VITE_BACKEND_API_URL;
      const apiUrl = baseUrl.endsWith('/api')
        ? `${baseUrl}/ride-requests?type=pending`
        : `${baseUrl}/api/ride-requests?type=pending`;

      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Fetching pending ride requests from:', apiUrl);
      }

      const response = await axios.get<RideRequest[]>(apiUrl, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Pending ride requests response:', response.data.length, 'rides');
      }

      const data: RideRequest[] = response.data;
      const filteredData = data.filter(ride => !rejectedRideIds.has(ride.id));
      setRideRequests(filteredData);
      setError(null);
    } catch (err) {
      if (axios.isAxiosError(err)) {
        if (err.response?.status === 401) {
          logout();
          return;
        } else if (err.response?.status === 304) {
          // Not Modified - data is still fresh
          if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
            console.log('Pending ride requests data is still fresh (304)');
          }
          return;
        }
      }

      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.error('Error fetching pending ride requests:', err);
      }
      setError('Failed to fetch ride requests. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [logout, rejectedRideIds]);

  // Silent fetch function (no loading state changes)
  const fetchRideRequestsSilently = useCallback(async () => {
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        logout();
        return;
      }

      const baseUrl = import.meta.env.VITE_BACKEND_API_URL;
      const apiUrl = baseUrl.endsWith('/api')
        ? `${baseUrl}/ride-requests?type=pending`
        : `${baseUrl}/api/ride-requests?type=pending`;

      const response = await axios.get<RideRequest[]>(apiUrl, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      // Only update state if data actually changed
      setRideRequests(prevRequests => {
        const newData = response.data;
        const filteredData = newData.filter(ride => !rejectedRideIds.has(ride.id));

        if (JSON.stringify(prevRequests) !== JSON.stringify(filteredData)) {
          if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
            console.log('Silent polling: Pending ride requests updated');
          }
          return filteredData;
        }
        return prevRequests;
      });

      setError(null);
    } catch (err) {
      if (axios.isAxiosError(err)) {
        if (err.response?.status === 401) {
          logout();
          return;
        } else if (err.response?.status === 304) {
          // Not Modified - data is still fresh, this is expected
          if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
            console.log('Silent polling: Pending data is still fresh (304)');
          }
          return;
        }
      }

      // Don't show errors for silent polling unless it's critical
      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Silent polling error (non-critical):', err);
      }
    }
  }, [logout, rejectedRideIds]);

  // Silent polling for pending ride requests - same pattern as ActiveRides
  const manualRefresh = useSilentPolling(
    fetchRideRequestsSilently,
    fetchRideRequests, // Initial fetch with loading state
    30000, // Poll every 30 seconds for pending rides
    10000, // Random offset between -5s and +5s (25-35 seconds)
    [logout, rejectedRideIds]
  );

  useEffect(() => {
    // No need for manual interval setup, handled by useSilentPolling
    return () => {}; // Empty return for consistency, cleanup handled by hook
  }, []);

  const handleAcceptRide = async (rideId: number) => {
    setActionLoading(rideId);
    setError(null);
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        logout();
        return;
      }

      const baseUrl = import.meta.env.VITE_BACKEND_API_URL;
      const apiUrl = baseUrl.endsWith('/api')
        ? `${baseUrl}/accept-ride/${rideId}/`
        : `${baseUrl}/api/accept-ride/${rideId}/`;

      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Accepting ride:', rideId);
      }

      await axios.post(apiUrl, {}, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      });

      // Remove the accepted ride from the list immediately
      setRideRequests(prev => prev.filter(ride => ride.id !== rideId));

      // Refresh the data after a short delay
      setTimeout(() => {
        fetchRideRequests();
      }, 500);

      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Ride accepted successfully');
      }
    } catch (err) {
      if (axios.isAxiosError(err) && err.response?.status === 401) {
        logout();
      } else {
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.error('Error accepting ride:', err);
        }
        setError('Failed to accept ride. Please try again.');
      }
    } finally {
      setActionLoading(null);
    }
  };

  const handleRejectRide = async (rideId: number) => {
    setActionLoading(rideId);
    setError(null);
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        logout();
        return;
      }

      const baseUrl = import.meta.env.VITE_BACKEND_API_URL;
      const apiUrl = baseUrl.endsWith('/api')
        ? `${baseUrl}/reject-ride/${rideId}/`
        : `${baseUrl}/api/reject-ride/${rideId}/`;

      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Rejecting ride:', rideId);
      }

      await axios.post(apiUrl, {}, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      });

      // Update rejected rides list and remove from current list
      setRejectedRideIds(prev => new Set(prev).add(rideId));
      setRideRequests(prev => prev.filter(ride => ride.id !== rideId));

      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Ride rejected successfully');
      }
    } catch (err) {
      if (axios.isAxiosError(err) && err.response?.status === 401) {
        logout();
      } else {
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.error('Error rejecting ride:', err);
        }
        setError('Failed to reject ride. Please try again.');
      }
    } finally {
      setActionLoading(null);
    }
  };

  if (loading) return <div className="spinner"></div>;
  if (error) return (
    <div className="error-container">
      <p className="error-message">{error}</p>
      <button className="retry-button" onClick={fetchRideRequests}>Retry</button>
    </div>
  );

  return (
    <div>
      <DriverNav />
      <div className="dashboard-container">
        <br />
        <div className="dashboard-header">
          <h1>Available Rides</h1>
          <button className="refresh-button" onClick={manualRefresh} title="Refresh rides">
            <FiRefreshCw />
          </button>
        </div>
        {rideRequests.length === 0 ? (
          <p>No ride requests available.</p>
        ) : (
          <>
            <table className="dashboard-table">
              <thead>
                <tr>
                  <th>Rider Name</th>
                  <th>Phone Number</th>
                  <th>Pickup Location</th>
                  <th>Drop-off Location</th>
                  <th>Ride Date/Time</th>
                  <th>Passengers</th>
                  <th>Luggage</th>
                  <th>Distance (km)</th>
                  <th>Fare</th>
                  {/* <th>Created At</th> */}
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {rideRequests.map((request) => (
                  <tr key={request.id}>
                    <td>{request.rider_name}</td>
                    <td>{request.rider_phone}</td>
                    <td>{request.pickup_location}</td>
                    <td>{request.dropoff_location}</td>
                    <td>{new Date(request.ride_datetime).toLocaleString()}</td>
                    <td>{request.passenger_count}</td>
                    <td>{request.luggage_count}</td>
                    <td>{parseFloat(request.distance_km).toFixed(2)}</td>
                    <td>{request.calculated_fare ? `$${parseFloat(request.calculated_fare).toFixed(2)}` : 'N/A'}</td>
                    {/* <td>{new Date(request.created_at).toLocaleString()}</td> */}
                    <td>
                      {request.status}
                      {request.status === "Assigned" && request.driver_name
                        ? ` (Assigned to ${request.driver_name})`
                        : ""}
                    </td>
                    <td className="actions-cell">
                      {request.status === "Pending" && (
                        <div className="action-buttons">
                          <button
                            className="action-accept"
                            onClick={() => handleAcceptRide(request.id)}
                            disabled={actionLoading === request.id}
                          >
                            {actionLoading === request.id ? 'Accepting...' : 'Accept'}
                          </button>
                          <button
                            className="action-reject"
                            onClick={() => handleRejectRide(request.id)}
                            disabled={actionLoading === request.id}
                          >
                            {actionLoading === request.id ? 'Rejecting...' : 'Reject'}
                          </button>
                        </div>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            <div className="ride-request-cards">
              {rideRequests.map((request) => (
                <div className="ride-request-card" key={request.id}>
                  <div className="card-row"><span className="label">Rider</span><span>{request.rider_name}</span></div>
                  <div className="card-row"><span className="label">Phone</span><span>{request.rider_phone}</span></div>
                  <div className="card-row"><span className="label">Pickup</span><span>{request.pickup_location}</span></div>
                  <div className="card-row"><span className="label">Drop-off</span><span>{request.dropoff_location}</span></div>
                  <div className="card-row"><span className="label">When</span><span>{new Date(request.ride_datetime).toLocaleString()}</span></div>
                  <div className="card-row"><span className="label">Passengers</span><span>{request.passenger_count}</span></div>
                  <div className="card-row"><span className="label">Luggage</span><span>{request.luggage_count}</span></div>
                  <div className="card-row"><span className="label">Distance</span><span>{parseFloat(request.distance_km).toFixed(2)} km</span></div>
                  <div className="card-row"><span className="label">Fare</span><span>{request.calculated_fare ? `$${parseFloat(request.calculated_fare).toFixed(2)}` : 'N/A'}</span></div>
                  <div className="card-row"><span className="label">Requested</span><span>{new Date(request.created_at).toLocaleString()}</span></div> {/* didnt comment this one, for some reason yet */}
                  <div className="card-row">
                    <span className="label">Status</span>
                    <span>
                      {request.status}
                      {request.status === "Assigned" && request.driver_name
                        ? ` (Assigned to ${request.driver_name})`
                        : ""}
                    </span>
                  </div>
                  {request.status === "Pending" && (
                    <div className="card-row actions">
                      <button
                        className="action-accept"
                        onClick={() => handleAcceptRide(request.id)}
                        disabled={actionLoading === request.id}
                      >
                        {actionLoading === request.id ? 'Accepting...' : 'Accept'}
                      </button>
                      <button
                        className="action-reject"
                        onClick={() => handleRejectRide(request.id)}
                        disabled={actionLoading === request.id}
                      >
                        {actionLoading === request.id ? 'Rejecting...' : 'Reject'}
                      </button>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default DashboardDriver;
import React, { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import axios from "axios";
import "../../styles/components/auth/Register.scss";
import { useAuth } from "./AuthContext"; // Import useAuth

const RegisterRider: React.FC = () => {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState("");
  const [email, setEmail] = useState("");
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [error, setError] = useState("");

  const navigate = useNavigate();
  const apiUrl = import.meta.env.VITE_BACKEND_API_URL as string;
  const { setAuthData } = useAuth(); // Get setAuthData from context

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    // Check passwords match before making the request
    if (password !== confirmPassword) {
      setError("Passwords do not match.");
      return;
    }

    // Validate phone number is exactly 7 digits and contains only numbers
    if (phoneNumber.length !== 7 || !/^[0-9]{7}$/.test(phoneNumber)) {
      setError("Phone number must be exactly 7 digits.");
      return;
    }

    try {
      const response = await axios.post(`${apiUrl}/register-rider/`, {
        username,
        password,
        phone_number: phoneNumber,
        email,
        first_name: firstName,
        last_name: lastName,
      });

      // Store tokens and update auth context
      const { access, role } = response.data;
      setAuthData(username, role, access); // Update context state
      navigate("/");
    } catch (err) {
      if (axios.isAxiosError(err) && err.response) {
        setError(err.response.data.error || "Registration failed");
      } else {
        setError("An unexpected error occurred");
      }
    }
  };

  return (
    <div className="register-container">
      <Link to="/" className="logo-link">
        <img
          src="/logo-no-background.png"
          alt="Sinusta Taxi Logo"
          className="logo"
        />
      </Link>

      <h2>Create Your Sinusta Account</h2>
      <p className="subtitle">
        Create a Sinusta account for a convenient ride experience and extra benefits.
        Fill in the registration form below and get started for free!
      </p>

      <form onSubmit={handleSubmit}>
        <div className="input-group">
          <label htmlFor="username">Username</label>
          <input
            type="text"
            id="username"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            required
            placeholder="Choose a username"
          />
        </div>

        <div className="input-group">
          <label htmlFor="email">Email</label>
          <input
            type="email"
            id="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            placeholder="e.g. <EMAIL>"
          />
        </div>

        <div className="input-group">
          <label htmlFor="firstName">First Name</label>
          <input
            type="text"
            id="firstName"
            value={firstName}
            onChange={(e) => setFirstName(e.target.value)}
            required
          />
        </div>

        <div className="input-group">
          <label htmlFor="lastName">Last Name</label>
          <input
            type="text"
            id="lastName"
            value={lastName}
            onChange={(e) => setLastName(e.target.value)}
            required
          />
        </div>

        <div className="input-group">
          <label htmlFor="phone">Phone Number</label>
          <input
            type="tel"
            id="phone"
            value={phoneNumber}
            onChange={(e) => setPhoneNumber(e.target.value)}
            required
            placeholder="e.g. 59995211257"
            minLength={7}
            maxLength={7} 
            pattern="[0-9]{7}"
            title="Phone number must be exactly 7 digits"
          />{/* Phone number must be exactly 7 digits */}
        </div>

        <div className="input-group">
          <label htmlFor="password">Password</label>
          <div className="password-wrapper">
            <input
              type={showPassword ? "text" : "password"}
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              placeholder="Create a secure password"
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? "Hide" : "Show"}
            </button>
          </div>
        </div>

        <div className="input-group">
          <label htmlFor="confirmPassword">Confirm Password</label>
          <div className="password-wrapper">
            <input
              type={showConfirmPassword ? "text" : "password"}
              id="confirmPassword"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              required
              placeholder="Re-type the password"
            />
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            >
              {showConfirmPassword ? "Hide" : "Show"}
            </button>
          </div>
        </div>

        {error && <p className="error">{error}</p>}

        <button type="submit" className="register-button">
          Register
        </button>
      </form>

      <p className="switch-form">
        Already have an account?{" "}
        <Link to="/login" className="login-link">
          Sign In
        </Link>
      </p>
    </div>
  );
};

export default RegisterRider;
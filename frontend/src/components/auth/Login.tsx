// frontend/src/components/auth/Login.tsx
import React, { useState } from "react";
import { useNavigate, Link } from "react-router-dom";
import axios from "axios";
import "../../styles/components/auth/Login.scss";
import { useAuth } from "./AuthContext";

const Login: React.FC = () => {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const navigate = useNavigate();
  const { setAuthData } = useAuth();

  const apiUrl = import.meta.env.VITE_BACKEND_API_URL as string;
  const devMode = import.meta.env.VITE_FRONTEND_DEV_MODE === "true";

  const debugLog = (...args: unknown[]) => {
    if (devMode) {
      console.log(...args);
    }
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const response = await axios.post(`${apiUrl}/login/`, {
        username,
        password,
      });

      debugLog("Login response:", response.data);

      const { access, refresh, role, last_visited_page } = response.data;

      localStorage.setItem('access_token', access);
      localStorage.setItem('refresh_token', refresh);
      setAuthData(username, role, access);

      if (rememberMe) {
        localStorage.setItem("username", username);
      }

      const isValidPath =
        last_visited_page && last_visited_page.startsWith("/") && !last_visited_page.startsWith("//");

      navigate(isValidPath ? last_visited_page : "/");
    } catch (err: unknown) {
      if (axios.isAxiosError(err) && err.response) {
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.error("Login error:", err.response.data);
        }
        setError(err.response.data?.error || "Invalid credentials");
      } else {
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.error("Login error:", err);
        }
        setError("An unexpected error occurred");
      }
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className="login-container">
      <Link to="/" className="logo-link">
        <img src="/logo-no-background.png" alt="Sinusta Taxi Logo" className="logo" />
      </Link>
      <h2>Sign in to your account</h2>

      <form onSubmit={handleLogin}>
        <div className="input-group">
          <label htmlFor="username">Username</label>
          <input
            type="text"
            id="username"
            placeholder="Enter your username"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            required
          />
        </div>

        <div className="input-group">
          <label htmlFor="password">Password</label>
          <div className="password-wrapper">
            <input
              type={showPassword ? "text" : "password"}
              id="password"
              placeholder="Enter your password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
            <button
              type="button"
              className="toggle-password"
              onClick={togglePasswordVisibility}
            >
              {showPassword ? "Hide" : "Show"}
            </button>
          </div>
        </div>

        <div className="options">
          <label className="remember-me">
            <input
              type="checkbox"
              checked={rememberMe}
              onChange={(e) => setRememberMe(e.target.checked)}
            />
            Remember Me
          </label>
          <Link to="/wip" className="forgot-password">
            Forgot Password?
          </Link>
        </div>

        <button type="submit" className="login-button">
          Log In
        </button>

        {error && <p className="error">{error}</p>}
      </form>

      <p className="signup-link">
        Don’t have an account? <Link to="/register">Sign Up</Link>
      </p>

      <p className="disclaimer">
        By logging in, you agree to our <Link to="/wip">Terms of Service</Link> and{" "}
        <Link to="/wip">Privacy Policy</Link>.
      </p>
    </div>
  );
};

export default Login;
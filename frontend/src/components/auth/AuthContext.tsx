// frontend/src/components/auth/AuthContext.tsx
import React, { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom'; // Import useNavigate

interface AuthContextType {
  username: string | null;
  role: string | null;
  setAuthData: (username: string | null, role: string | null, token?: string | null) => void;
  logout: () => void;
  refreshAccessToken: () => Promise<string>;
}

const AuthContext = createContext<AuthContextType>({
  username: null,
  role: null,
  setAuthData: () => {},
  logout: () => {},
  refreshAccessToken: async () => '',
});

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [username, setUsername] = useState<string | null>(null);
  const [role, setRole] = useState<string | null>(null);
  const apiUrl = import.meta.env.VITE_BACKEND_API_URL as string;
  const navigate = useNavigate(); // Add navigation hook

  useEffect(() => {
    const token = localStorage.getItem('access_token');
    if (token && !username) {
      axios
        .get(`${apiUrl}/dashboard/`, {
          headers: { Authorization: `Bearer ${token}` },
        })
        .then((response) => {
          setUsername(response.data.username);
          setRole(response.data.role || 'Unknown');
        })
        .catch((err) => {
          console.error('Failed to fetch dashboard data:', err);
          logout(); // Call logout to clear data and redirect
        });
    }
  }, [apiUrl, username]);

  const setAuthData = (newUsername: string | null, newRole: string | null, token: string | null = null) => {
    if (newUsername && newRole && token) {
      localStorage.setItem('access_token', token);
      localStorage.setItem('username', newUsername);
      localStorage.setItem('role', newRole);
      setUsername(newUsername);
      setRole(newRole);
    } else {
      localStorage.removeItem('access_token');
      localStorage.removeItem('username');
      localStorage.removeItem('role');
      setUsername(null);
      setRole(null);
    }
  };

  const logout = () => {
    setAuthData(null, null, null);
    navigate('/login'); // Redirect to login page
  };

  const refreshAccessToken = async () => {
    const refreshToken = localStorage.getItem('refresh_token');
    if (!refreshToken) {
      logout(); // Redirect if no refresh token
      throw new Error('No refresh token available');
    }

    try {
      const response = await axios.post(`${apiUrl}/token/refresh/`, {
        refresh: refreshToken,
      });
      const { access } = response.data;
      localStorage.setItem('access_token', access);
      return access;
    } catch (error) {
      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.error('Token refresh failed:', error);
      }
      logout(); // Redirect on refresh failure
      throw error;
    }
  };

  return (
    <AuthContext.Provider value={{ username, role, setAuthData, logout, refreshAccessToken }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);
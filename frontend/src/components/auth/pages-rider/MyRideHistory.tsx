import React, { useState, useEffect } from 'react';
import { FiR<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ch, FiX, FiArrowUp, FiArrowDown } from 'react-icons/fi';
import '../../../styles/components/auth/pages-rider/MyRideHistory.scss';
// import RiderNav from './RiderNav'; // Assuming you have a RiderNav component
import axios from 'axios';
import { useAuth } from '../AuthContext';

interface RideRequest {
    id: number;
    pickup_location: string;
    dropoff_location: string;
    ride_datetime: string;
    passenger_count: number;
    luggage_count: number;
    distance_km: string;
    calculated_fare: string | null;
    created_at: string;
    status: string;
    driver_name?: string | null;
    driver_phone?: string | null;
    completed_at?: string | null;
}

type SortColumn = 'date' | 'pickup' | 'dropoff' | 'driver' | 'fare' | 'status';
type SortOrder = 'asc' | 'desc';

const MyRideHistory: React.FC = () => {
    const [rides, setRides] = useState<RideRequest[]>([]);
    const [searchQuery, setSearchQuery] = useState<string>('');
    const [selectedRide, setSelectedRide] = useState<RideRequest | null>(null);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const [sortColumn, setSortColumn] = useState<SortColumn>('date');
    const [sortOrder, setSortOrder] = useState<SortOrder>('desc');
    const { logout } = useAuth();

    const fetchRides = async () => {
        setLoading(true);
        setError(null);
        try {
            const apiUrl = `${import.meta.env.VITE_BACKEND_API_URL}/ride-requests`;
            const response = await axios.get(apiUrl, {
                headers: {
                    Authorization: `Bearer ${localStorage.getItem('access_token')}`,
                },
            });
            const ridesData = response.data;
            // Default sort: latest to oldest by ride_datetime
            ridesData.sort((a: RideRequest, b: RideRequest) => new Date(b.ride_datetime).getTime() - new Date(a.ride_datetime).getTime());
            setRides(ridesData);
        } catch (err) {
            if (axios.isAxiosError(err) && err.response?.status === 401) {
                logout();
            } else {
                setError('Failed to fetch ride history. Please try again.');
            }
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchRides();
    }, []);

    const handleSort = (column: SortColumn) => {
        if (sortColumn === column) {
            setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
        } else {
            setSortColumn(column);
            setSortOrder('asc');
        }
    };

    // Filter rides based on search query
    const filteredRides = searchQuery
        ? rides.filter(ride => {
            const searchLower = searchQuery.toLowerCase();
            return (
                ride.pickup_location.toLowerCase().includes(searchLower) ||
                ride.dropoff_location.toLowerCase().includes(searchLower) ||
                (ride.driver_name && ride.driver_name.toLowerCase().includes(searchLower)) ||
                new Date(ride.ride_datetime).toLocaleString().toLowerCase().includes(searchLower) ||
                ride.status.toLowerCase().includes(searchLower)
            );
        })
        : rides;

    // Sort the filtered rides
    const sortedRides = [...filteredRides].sort((a, b) => {
        let comparison = 0;
        switch (sortColumn) {
            case 'date':
                comparison = new Date(a.ride_datetime).getTime() - new Date(b.ride_datetime).getTime();
                break;
            case 'pickup':
                comparison = a.pickup_location.localeCompare(b.pickup_location);
                break;
            case 'dropoff':
                comparison = a.dropoff_location.localeCompare(b.dropoff_location);
                break;
            case 'driver':
                const driverA = a.driver_name || '';
                const driverB = b.driver_name || '';
                comparison = driverA.localeCompare(driverB);
                break;
            case 'fare':
                comparison = (a.calculated_fare ? parseFloat(a.calculated_fare) : 0) - (b.calculated_fare ? parseFloat(b.calculated_fare) : 0);
                break;
            case 'status':
                comparison = a.status.localeCompare(b.status);
                break;
        }
        return sortOrder === 'asc' ? comparison : -comparison;
    });

    if (loading) return <div className="spinner"></div>;
    if (error) return (
        <div className="error-container">
            <p className="error-message">{error}</p>
            <button className="retry-button" onClick={fetchRides}>Retry</button>
        </div>
    );

    return (
        <div>
            {/* <RiderNav /> */}
            <div className="rider-history-container">
                <br />
                <div className="history-header">
                    <h1>My Ride History</h1>
                    <button className="refresh-button" onClick={fetchRides}>
                        <FiRefreshCw />
                    </button>
                </div>
                {rides.length === 0 ? (
                    <p>No ride history available.</p>
                ) : (
                    <>
                        <div className="search-container sticky-header">
                            <input
                                type="text"
                                placeholder="Search rides by location, driver, date, or status..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                            />
                            <FiSearch className="search-icon" />
                            {searchQuery && (
                                <button
                                    className="clear-search-button"
                                    onClick={() => setSearchQuery('')}
                                >
                                    Clear
                                </button>
                            )}
                        </div>
                        {filteredRides.length === 0 ? (
                            <p>No rides match your search.</p>
                        ) : (
                            <div className="ride-list-container">
                                <div className="ride-list-header">
                                    <span className="header-date" onClick={() => handleSort('date')}>
                                        Ride Date {sortColumn === 'date' && (sortOrder === 'asc' ? <FiArrowUp /> : <FiArrowDown />)}
                                    </span>
                                    <span className="header-pickup" onClick={() => handleSort('pickup')}>
                                        Pickup {sortColumn === 'pickup' && (sortOrder === 'asc' ? <FiArrowUp /> : <FiArrowDown />)}
                                    </span>
                                    <span className="header-dropoff" onClick={() => handleSort('dropoff')}>
                                        Dropoff {sortColumn === 'dropoff' && (sortOrder === 'asc' ? <FiArrowUp /> : <FiArrowDown />)}
                                    </span>
                                    <span className="header-driver" onClick={() => handleSort('driver')}>
                                        Driver {sortColumn === 'driver' && (sortOrder === 'asc' ? <FiArrowUp /> : <FiArrowDown />)}
                                    </span>
                                    <span className="header-fare" onClick={() => handleSort('fare')}>
                                        Fare {sortColumn === 'fare' && (sortOrder === 'asc' ? <FiArrowUp /> : <FiArrowDown />)}
                                    </span>
                                    <span className="header-status" onClick={() => handleSort('status')}>
                                        Status {sortColumn === 'status' && (sortOrder === 'asc' ? <FiArrowUp /> : <FiArrowDown />)}
                                    </span>
                                </div>
                                <div className="ride-history-list">
                                    {sortedRides.map(ride => (
                                        <div
                                            className="ride-list-item"
                                            key={ride.id}
                                            onClick={() => setSelectedRide(ride)}
                                        >
                                            <div className="ride-summary">
                                                <span className="ride-date">
                                                    {new Date(ride.ride_datetime).toLocaleDateString()} {new Date(ride.ride_datetime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                                                </span>
                                                <span className="ride-pickup">{ride.pickup_location}</span>
                                                <span className="ride-dropoff">{ride.dropoff_location}</span>
                                                <span className="ride-driver">{ride.driver_name || 'Not Assigned'}</span>
                                                <span className="ride-fare">
                                                    {ride.calculated_fare ? `$${parseFloat(ride.calculated_fare).toFixed(2)}` : 'N/A'}
                                                </span>
                                                <span className="ride-status">{ride.status}</span>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}
                        {selectedRide && (
                            <div
                                className="ride-details-modal"
                                onClick={(e) => {
                                    if (e.target instanceof HTMLElement && e.target.classList.contains('ride-details-modal')) {
                                        setSelectedRide(null);
                                    }
                                }}
                            >
                                <div className="modal-content">
                                    <button
                                        className="close-button"
                                        onClick={() => setSelectedRide(null)}
                                        aria-label="Close modal"
                                    >
                                        <FiX />
                                    </button>
                                    <h2>Ride Details</h2>
                                    <div className="section">
                                        <h3>Locations</h3>
                                        <div className="detail-row">
                                            <span className="label">Pickup:</span>
                                            <span className="value">{selectedRide.pickup_location}</span>
                                        </div>
                                        <div className="detail-row">
                                            <span className="label">Drop-off:</span>
                                            <span className="value">{selectedRide.dropoff_location}</span>
                                        </div>
                                    </div>
                                    <div className="section">
                                        <h3>Ride Information</h3>
                                        <div className="detail-row">
                                            <span className="label">Date & Time:</span>
                                            <span className="value">{new Date(selectedRide.ride_datetime).toLocaleString()}</span>
                                        </div>
                                        <div className="detail-row">
                                            <span className="label">Passengers:</span>
                                            <span className="value">{selectedRide.passenger_count}</span>
                                        </div>
                                        <div className="detail-row">
                                            <span className="label">Luggage:</span>
                                            <span className="value">{selectedRide.luggage_count}</span>
                                        </div>
                                        <div className="detail-row">
                                            <span className="label">Distance:</span>
                                            <span className="value">{parseFloat(selectedRide.distance_km).toFixed(2)} km</span>
                                        </div>
                                        <div className="detail-row">
                                            <span className="label">Fare:</span>
                                            <span className="value">{selectedRide.calculated_fare ? `$${parseFloat(selectedRide.calculated_fare).toFixed(2)}` : 'N/A'}</span>
                                        </div>
                                    </div>
                                    <div className="section">
                                        <h3>Driver Information</h3>
                                        <div className="detail-row">
                                            <span className="label">Driver Name:</span>
                                            <span className="value">{selectedRide.driver_name || 'Not Assigned'}</span>
                                        </div>
                                        <div className="detail-row">
                                            <span className="label">Driver Phone:</span>
                                            <span className="value">{selectedRide.driver_phone || 'N/A'}</span>
                                        </div>
                                    </div>
                                    <div className="section">
                                        <h3>Request Information</h3>
                                        <div className="detail-row">
                                            <span className="label">Requested At:</span>
                                            <span className="value">{new Date(selectedRide.created_at).toLocaleString()}</span>
                                        </div>
                                        <div className="detail-row">
                                            <span className="label">Completed At:</span>
                                            <span className="value">{selectedRide.completed_at ? new Date(selectedRide.completed_at).toLocaleString() : 'N/A'}</span>
                                        </div>
                                        <div className="detail-row">
                                            <span className="label">Status:</span>
                                            <span className="value">{selectedRide.status}</span>
                                        </div>
                                    </div>
                                    <button className="close-modal-button" onClick={() => setSelectedRide(null)}>
                                        Close
                                    </button>
                                </div>
                            </div>
                        )}
                    </>
                )}
            </div>
        </div>
    );
};

export default MyRideHistory;
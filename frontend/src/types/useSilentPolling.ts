import { useEffect, useRef, useCallback } from 'react';

/**
 * Custom hook to handle silent data polling with randomized intervals to prevent synchronized requests.
 * @param fetchFunction The function to fetch data silently.
 * @param initialFetchFunction The initial fetch function to load data on mount (optional, can be same as fetchFunction).
 * @param baseInterval The base polling interval in milliseconds.
 * @param randomOffsetRange The range for random offset in milliseconds (±range/2).
 * @param dependencies Dependency array for the effect.
 * @returns A function to manually trigger an immediate fetch.
 */
export const useSilentPolling = <T>(
  fetchFunction: () => Promise<T>,
  initialFetchFunction: () => Promise<T> = fetchFunction,
  baseInterval: number = 30000,
  randomOffsetRange: number = 20000,
  dependencies: React.DependencyList = []
) => {
  const isMounted = useRef(true);

  // Create a manual fetch function that can be called immediately
  const manualFetch = useCallback(async () => {
    if (isMounted.current) {
      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Manual fetch triggered while component is mounted.');
      }
      await initialFetchFunction();
    } else {
      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Manual fetch attempted but component is unmounted.');
      }
    }
  }, [initialFetchFunction]);

  useEffect(() => {
    if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
      console.log('Component mounted, initializing polling.');
    }
    isMounted.current = true;
    // Initial fetch on component mount
    initialFetchFunction();

    // Set up polling with randomization
    const randomOffset = Math.floor(Math.random() * randomOffsetRange) - randomOffsetRange / 2;
    const intervalDuration = baseInterval + randomOffset;
    if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
      console.log(`Setting polling interval to ${intervalDuration/1000} seconds with randomization`);
    }

    const interval = setInterval(async () => {
      if (isMounted.current) {
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.log('Polling interval triggered, fetching data...');
        }
        try {
          await fetchFunction();
        } catch (error: any) {
          // Handle 304 Not Modified as a success case
          if (error?.response?.status === 304) {
            if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
              console.log('Server returned 304 Not Modified - data is still fresh');
            }
          } else {
            // Re-throw other errors to be handled by the component
            throw error;
          }
        }
      } else {
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.log('Component unmounted, skipping fetch.');
        }
      }
    }, intervalDuration);

    return () => {
      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Component unmounting, cleaning up polling interval.');
      }
      isMounted.current = false;
      clearInterval(interval); // Cleanup on unmount
      if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
        console.log('Polling interval cleared.');
      }
    };
  }, dependencies);

  return manualFetch;
};

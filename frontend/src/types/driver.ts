// Shared driver types and utilities for operator components

export interface DriverProfile {
  phone_number: string;
  vehicle_type: string;
  current_location: string;
}

export interface TransformedDriver {
  id: number;
  username: string;
  profile: DriverProfile;
}

export interface RawDriverData {
  id: string | number;
  name: string;
  phone?: string;
  vehicle?: string;
  location?: string;
  status: string;
}

/**
 * Transforms raw driver data from API to the format expected by operator components
 * @param driver Raw driver data from API
 * @returns Transformed driver object
 */
export const transformDriverData = (driver: RawDriverData): TransformedDriver => {
  return {
    id: typeof driver.id === 'string' ? (() => {
   const parsed = parseInt(driver.id, 10);
   if (isNaN(parsed)) {
     throw new Error(`Invalid driver ID: ${driver.id}`);
   }
   return parsed;
 })() : driver.id,
    username: driver.name,
    profile: {
      phone_number: driver.phone || 'No phone',
      vehicle_type: driver.vehicle || 'N/A',
      current_location: driver.location || 'Unknown'
    }
  };
};

/**
 * Filters and transforms an array of drivers to only include available ones
 * @param drivers Array of raw driver data
 * @returns Array of transformed available drivers
 */
export const getAvailableDrivers = (drivers: RawDriverData[]): TransformedDriver[] => {
  return drivers
    .filter((driver) => driver.status === 'available' || driver.status === 'offline')
    .map(transformDriverData);
};

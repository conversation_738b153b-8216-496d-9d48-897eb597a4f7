// src/types/global.d.ts
declare global {
  interface Window {
    /**
     * Optional flag to track the loading status of the Google Maps API script.
     * Prevents multiple load attempts across different components.
     */
    googleMapsApiLoading?: boolean;

    /**
     * Allows dynamically named callback functions (e.g., googleMapsApiLoadedCallback)
     * to be attached to the window object for the Google Maps API script.
     */
    [key: string]: any;

    // Optional: Explicitly declare the google object if needed elsewhere or if 
    // type inference isn't sufficient after script load.
    // google?: typeof google;
  }
}

// Adding this empty export statement turns this file into a module,
// which is necessary for augmenting the global scope.
export {};

// TypeScript interfaces for dashboard-related data structures
export interface RideRequest {

  id: number;
  rider_name: string;
  pickup_location: string;
  dropoff_location: string;
  ride_datetime: string;
  passenger_count: number;
  luggage_count: number;
  distance_km?: number;
  calculated_fare?: number | null;
  created_at: string;
  status: string;
  driver?: number | null;
  driver_name?: string | null;
}

export interface Driver {
  id: string | number;
  name: string;
  phone: string;
  vehicle: string;
  status: 'available' | 'on_ride' | 'offline' | 'inactive';
  rating: number;
  ridesCompleted: number;
  location: string;
  lastActive: string;
}

export interface ActiveRide {
  id: number;
  pickup_location: string;
  dropoff_location: string;
  status: string;
  driver_name: string;
  assigned_time: string;
  rider_name?: string;
  rider_phone?: string;
  driver?: {
    id: number;
    name: string;
    phone?: string;
    vehicle?: string;
  };
}

export interface OperationalStats {
  pendingCount: number;
  assignedCount: number;
  availableDrivers: number;
  busyDrivers: number;
  todayCompletedRides: number;
  avgResponseTime: string;
}

export interface ApiError {
  response?: {
    status: number;
    data?: {
      message?: string;
      detail?: string;
      [key: string]: any;
    };
  };
  message?: string;
  code?: string;
}

export interface DashboardState {
  pendingRides: RideRequest[];
  availableDrivers: Driver[];
  activeRides: ActiveRide[];
  operationalStats: OperationalStats;
  isLoading: boolean;
  error: string | null;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
}

export interface RideApiResponse<T> extends ApiResponse<T> {
  status: 'pending' | 'assigned' | 'completed' | 'cancelled';
}

export interface PaginatedResponse<T> {
  results: T[];
  count: number;
  next: string | null;
  previous: string | null;
}

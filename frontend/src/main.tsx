// frontend/src/main.tsx
import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import App from './App.tsx';
import axios from 'axios';

declare global {
  interface Window {
    googleMapsLoaded?: boolean;
  }
}

const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;
if (!apiKey) {
  if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
    console.error('Google Maps API key is missing.');
  }
} else {
  const script = document.createElement('script');
  script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places`;
  script.async = true;
  script.defer = true;
  script.onload = () => {
    if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
      console.log('Google Maps API loaded successfully');
    }
    window.googleMapsLoaded = true;
  };
  script.onerror = () => {
    if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
      console.error('Failed to load Google Maps API');
    }
  };
  document.head.appendChild(script);
}

axios.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      try {
        const refreshToken = localStorage.getItem('refresh_token');
        if (!refreshToken) throw new Error('No refresh token');
        const apiUrl = import.meta.env.VITE_BACKEND_API_URL as string;
        const response = await axios.post(`${apiUrl}/token/refresh/`, {
          refresh: refreshToken,
        });
        const newAccessToken = response.data.access;
        localStorage.setItem('access_token', newAccessToken);
        originalRequest.headers['Authorization'] = `Bearer ${newAccessToken}`;
        return axios(originalRequest);
      } catch (refreshError) {
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.error('Refresh token failed:', refreshError);
        }
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }
    return Promise.reject(error);
  }
);

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>
);
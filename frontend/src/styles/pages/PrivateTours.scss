// frontend/src/styles/pages/PrivateTours.scss
@use "../variables" as *;

.page-content {
  padding-top: $navbar-height; // Pushes content below fixed navbar
  // min-height: calc(100vh - $navbar-height); // Ensures footer stays at bottom
}

.private-tours {
  padding: $spacing-xl $spacing-md;
  background: $light-gray;

  h1 {
    font-size: $font-size-xxl;
    color: $primary-blue;
    text-align: center;
    margin-bottom: $spacing-lg;
    font-weight: $font-weight-bolder;
    padding-top: $spacing-lg;
  }

  .tours-container {
    display: flex;
    flex-wrap: wrap;
    gap: $spacing-lg;
    justify-content: center;
    max-width: 1200px;
    margin: 0 auto;
  }

  .tour-card {
    display: flex;
    background: $white;
    border-radius: $border-radius-lg;
    box-shadow: $box-shadow-md;
    overflow: hidden;
    width: 100%;
    max-width: 800px;
    transition: transform $transition-default;
    position: relative; // Needed for absolute positioning of pseudo-elements

    .tour-image {
      flex: 0 0 40%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: $primary-blue;
      overflow: hidden;
      position: relative; // For overlay and icon positioning

      // Background overlay
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba($primary-blue-dark, 0.1); // Subtle dark overlay
        z-index: 1; // Below the icon and image
      }

      // Centered placeholder icon
      &::after {
        content: "\f5a0"; // FontAwesome unicode for fa-map-marked-alt
        font-family: "Font Awesome 5 Free";
        font-weight: 900; // Solid icon
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 5em; // Large icon size
        color: rgba($white, 0.15); // Faded white icon
        z-index: 2; // Above overlay, below image
      }

      img {
        width: 100%;
        height: auto;
        max-height: 300px;
        object-fit: contain;
        object-position: center;
        position: relative; // Ensure image is on top
        z-index: 3; // Above icon and overlay
      }
    }

    .tour-details {
      flex: 1;
      padding: $spacing-lg; // Increased padding
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .tour-header {
        h2 {
          font-size: $font-size-xl;
          color: $primary-blue-dark;
          margin-bottom: $spacing-sm;
        }

        .tour-meta {
          display: flex; 
          flex-wrap: wrap; 
          gap: $spacing-sm $spacing-md; 
          margin-bottom: $spacing-md; 

          p {
            font-size: $font-size-md;
            color: $black;
            margin: 0; 
            display: flex;
            align-items: center;

            &.meta-item i { 
              margin-right: $spacing-xs; 
              color: $primary-blue; 
              width: 1.2em; 
              text-align: center;
            }

            &.price {
              font-weight: $font-weight-bold;
              color: $accent-yellow; 
              i {
                color: $accent-yellow; 
              }
            }
          }
        }
      }

      .tour-description {
        p {
          font-size: $font-size-md;
          color: $black;
          line-height: 1.5;
          margin-bottom: $spacing-lg; // Consistent large margin
        }
      }

      .tour-includes {
        h3 {
          font-size: $font-size-lg;
          color: $primary-blue;
          margin-bottom: $spacing-sm;
        }

        ul {
          list-style: none;
          padding: 0;
          margin: 0 0 $spacing-lg 0; // Ensure consistent bottom margin like description

          li {
            font-size: $font-size-md;
            color: $black;
            margin-bottom: calc($spacing-sm / 2); // Replaced $spacing-sm / 2 with calc() for division
            position: relative;
            padding-left: 20px;

            &:before {
              content: "\f00c"; 
              font-family: "Font Awesome 5 Free"; 
              font-weight: 900; 
              color: $primary-success-code-color; 
              position: absolute;
              left: 0;
              top: 2px; 
            }
          }
        }
      }

      .tour-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .cancellation {
          font-size: $font-size-sm;
          color: $gray-border;
        }

        .more-info-btn {
          background: $button-primary-bg;
          color: $button-primary-text;
          display: inline-flex; // Use inline-flex for icon alignment
          align-items: center;
          justify-content: center;
          border: none;
          padding: $spacing-sm $spacing-lg; // Adjust padding slightly for icon
          border-radius: $border-radius-md;
          font-size: $font-size-md;
          cursor: pointer;
          transition: background $transition-default;

          &:hover {
            background: $button-primary-hover;
          }

          i { // Style the icon inside the button
            margin-left: $spacing-sm; // Space between text and icon
            transition: transform $transition-default; // Add transition for hover effect
          }

          &:hover i { // Slight move effect on hover
            transform: translateX(3px);
          }
        }
      }
    }
  }
}

/* Tablet (768px and below) */
@media (max-width: 768px) {
  .private-tours {
    padding: $spacing-lg $spacing-sm;

    .tour-card {
      flex-direction: column;
      max-width: 500px;

      .tour-image {
        height: 200px;
        img {
          max-height: 200px;
        }
      }
    }
  }
}

/* Mobile (480px and below) */
@media (max-width: 480px) {
  .private-tours {
    padding: $spacing-md $spacing-sm;

    h1 {
      font-size: $font-size-xl;
    }

    .tour-card {
      .tour-image {
        height: 150px;
        img {
          max-height: 150px;
        }
      }

      .tour-details {
        padding: $spacing-sm;

        .tour-header h2 {
          font-size: $font-size-lg;
        }

        .tour-meta p {
          font-size: $font-size-sm;
        }

        .tour-description p {
          font-size: $font-size-sm;
        }

        .tour-includes {
          h3 {
            font-size: $font-size-md;
          }

          ul li {
            font-size: $font-size-sm;
          }
        }

        .tour-footer {
          flex-direction: column;
          gap: $spacing-sm;
          text-align: center;

          .more-info-btn {
            width: 100%;
          }
        }
      }
    }
  }
}
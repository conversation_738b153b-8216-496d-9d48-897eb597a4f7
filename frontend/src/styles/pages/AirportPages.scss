// frontend/src/styles/pages/AirportPages.scss
@use "../variables.scss" as *;

body {
  margin: 0;
  display: flex;
  flex-direction: column;
  min-height: 100vh; // Ensure the body takes up full viewport height
}

.airport-page {
  max-width: 800px; // Keeping this as-is since no variable matches exactly
  margin: $spacing-xxxl auto $spacing-lg auto; // 80px top, 30px bottom, auto sides (increased from 60px)
  padding: $spacing-lg; // 30px all sides
  font-family: $font-primary; // Visby CF
  background-color: $light-gray; // #f8f9fa
  border-radius: $border-radius-lg; // 10px
  box-shadow: $box-shadow-lg; // 0 4px 8px rgba(0, 0, 0, 0.1)
  flex: 1 0 auto; // Allow content to grow and fill space

  h1 {
    font-size: $font-size-xxl; // 2.5em
    color: $primary-blue; // #002B7F (Curaçao flag blue)
    margin-bottom: $spacing-md; // 20px
  }

  .destination {
    font-size: $font-size-lg; // 18px
    color: $primary-blue-dark; // #003580
    margin-bottom: $spacing-lg; // 30px
  }

  .options-section {
    display: flex;
    gap: $spacing-md; // 20px
    margin-bottom: $spacing-lg; // 30px
    justify-content: center; // Center the inputs for balance

    label {
      display: flex;
      flex-direction: column;
      font-weight: $font-weight-bold; // 500
      color: $primary-blue; // #002B7F

      input {
        margin-top: $spacing-sm; // 10px
        padding: $spacing-sm; // 10px
        width: 80px; // No variable, keeping as-is
        font-size: $font-size-md; // 16px
        border: 1px solid $gray-border; // #5B5B5B
        border-radius: $border-radius-sm; // 4px
        outline: none;
        transition: border-color $transition-default; // 0.3s ease

        &:focus {
          border-color: $button-primary-bg; // #005EB8
        }

        &:disabled {
          background-color: $button-secondary-bg; // #E6E6E6
          cursor: not-allowed;
        }
      }
    }
  }

  .fare-button {
    width: 100%;
    max-width: 300px; // Keeping as-is for layout
    padding: $spacing-md; // 20px
    font-size: $font-size-lg; // 18px
    background-color: $button-accent-bg; // #FFB107 (yellow from Curaçao theme)
    color: $button-accent-text; // #000000 (black for contrast)
    border: none;
    border-radius: $border-radius-md; // 5px
    cursor: pointer;
    transition: background-color $transition-default; // 0.3s ease
    display: block;
    margin: $spacing-lg auto $spacing-lg auto; // 30px top and bottom

    &:hover:not(:disabled) {
      background-color: $button-accent-hover; // #FFA000
    }

    &:disabled {
      background-color: $button-secondary-active; // #B8B8B8
      cursor: not-allowed;
    }
  }

  .result {
    margin-top: $spacing-md; // 20px
    font-size: $font-size-lg; // 18px
    color: $primary-success-code-color; // #27ae60
    text-align: center;

    strong {
      font-weight: $font-weight-bolder; // bold
    }
  }

  .error {
    margin-top: $spacing-md; // 20px
    font-size: $font-size-md; // 16px
    color: $primary-error-code-color; // #ff0000
    text-align: center;
  }

  /* Style the loading text */
  p:not(.result):not(.error):not(.destination) {
    margin-top: $spacing-md; // 20px
    font-size: $font-size-md; // 16px
    color: $primary-blue-darker; // #003b8a (slightly muted blue for loading)
    text-align: center;
  }

  // Google Map Styles
  .google-map-container {
    margin-bottom: $spacing-lg; // Add space below the map container
  }

  .map-canvas {
    width: 100%;
    height: 400px; // Standard map height
    border-radius: $border-radius-md; // Consistent rounded corners
    border: 1px solid $gray-border; // Subtle border
    box-shadow: $box-shadow-sm; // Slight shadow for depth
    margin-bottom: $spacing-md; // Space between map and status messages
  }

  // Map Status Message Base Style
  .map-status {
    padding: $spacing-sm $spacing-md; // Padding around text
    margin-top: $spacing-sm; // Space above message if multiple show
    border-radius: $border-radius-sm;
    text-align: center;
    font-size: $font-size-md;

    // Status Variants
    &.map-loading,
    &.map-locating {
      background-color: $primary-blue-darkest; // Darker blue for info
      color: $light-gray;
    }

    &.map-success {
      background-color: $primary-success-light-45; // Replaced lighten($primary-success-code-color, 45%) with pre-calculated color
      color: $primary-success-dark-10; // Replaced darken($primary-success-code-color, 10%) with pre-calculated color
      border: 1px solid $primary-success-code-color;
    }

    &.map-error {
      background-color: $primary-error-light-55; // Replaced lighten($primary-error-code-color, 55%) with pre-calculated color
      color: $primary-error-dark-10; // Replaced darken($primary-error-code-color, 10%) with pre-calculated color
      border: 1px solid $primary-error-code-color;
      display: flex; // To align button
      justify-content: center;
      align-items: center;
      gap: $spacing-sm;
    }

    &.map-info {
      background-color: $primary-blue-light-50; // Replaced lighten($primary-blue, 50%) with pre-calculated color
      color: $primary-blue-dark;
      border: 1px solid $primary-blue;
    }
  }

  // Retry Location Button Style
  .retry-location-button {
    padding: $spacing-xs $spacing-sm; // Smaller padding
    font-size: $font-size-sm; // Smaller font
    background-color: $button-secondary-bg;
    color: $primary-blue;
    border: 1px solid $gray-border;
    border-radius: $border-radius-sm;
    cursor: pointer;
    transition: background-color $transition-default;

    &:hover {
      background-color: $button-secondary-hover;
    }
  }

  // Loading Spinner Styles
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .loading-spinner {
    display: inline-block;
    width: 1em; // Size relative to button font size
    height: 1em;
    margin-right: $spacing-sm; // Space between spinner and text
    border: 2px solid rgba(255, 255, 255, 0.3); // Light border
    border-radius: 50%;
    border-top-color: $white; // Solid top border for spin effect
    animation: spin 1s linear infinite;
    vertical-align: middle; // Align with button text
  }
}

/* Ensure footer has breathing room */
footer {
  flex-shrink: 0; // Prevent footer from shrinking
  margin-top: $spacing-xl; // 40px to give space above footer
}

/* Mobile responsiveness */
@media (max-width: 600px) {
  .airport-page {
    margin: $spacing-xxxl+50 $spacing-md $spacing-lg $spacing-md; // 80px top, 15px sides, 30px bottom
    padding: $spacing-md; // 20px all sides
    max-width: 100%; // Full width on mobile

    h1 {
      font-size: $font-size-xl; // 2em, smaller for mobile
    }

    .destination {
      font-size: $font-size-md; // 16px
    }

    .options-section {
      flex-direction: column; // Stack inputs vertically
      align-items: center;
      gap: $spacing-sm; // 10px

      label {
        width: 100%; // Full width for labels
        input {
          width: 100%; // Full width inputs
          max-width: 120px; // Cap width for usability
          margin: $spacing-sm auto 0 auto; // Center inputs
        }
      }
    }

    .fare-button {
      font-size: $font-size-md; // 16px
      padding: $spacing-sm $spacing-md; // 10px vertical, 20px horizontal
      max-width: 100%; // Full width on mobile
    }

    .result,
    .error,
    p:not(.result):not(.error):not(.destination) {
      font-size: $font-size-sm; // 14px
    }
  }

  footer {
    margin-top: $spacing-lg; // 30px, slightly less on mobile
  }
}

// Add these styles to the existing AirportPages.scss file

.button-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: $spacing-md; // 20px
  flex-wrap: wrap; // Allow wrapping on smaller screens
  margin: $spacing-lg auto; // 30px top and bottom

  .retry-button {
    width: 100%;
    max-width: 150px; // Smaller than fare-button
    padding: $spacing-sm; // 10px
    font-size: $font-size-md; // 16px
    background-color: $button-secondary-bg; // #E6E6E6
    color: $primary-blue; // #002B7F
    border: 1px solid $gray-border; // #5B5B5B
    border-radius: $border-radius-md; // 5px
    cursor: pointer;
    transition: background-color $transition-default; // 0.3s ease

    &:hover:not(:disabled) {
      background-color: $button-secondary-hover; // Slightly darker, e.g., #D6D6D6
    }

    &:disabled {
      background-color: $button-secondary-active; // #B8B8B8
      cursor: not-allowed;
    }
  }

  .success-indicator {
    font-size: $font-size-md; // 16px
    color: $primary-success-code-color; // #27ae60
    font-weight: $font-weight-bold; // 500
    margin-left: $spacing-sm; // 10px
  }
}

// Adjust mobile responsiveness for the button container
@media (max-width: 600px) {
  .button-container {
    flex-direction: column;
    gap: $spacing-sm; // 10px

    .fare-button,
    .retry-button {
      width: 100%;
      max-width: 100%; // Full width on mobile
    }

    .success-indicator {
      margin-left: 0;
      margin-top: $spacing-sm; // 10px
    }
  }
}
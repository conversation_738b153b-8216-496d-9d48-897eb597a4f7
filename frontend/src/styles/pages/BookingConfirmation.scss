// BookingConfirmation.scss
.booking-confirmation {
  min-height: calc(100vh - 120px);
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 2rem 0;
  font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;

  .confirmation-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 1rem;
  }

  .success-header {
    text-align: center;
    margin-bottom: 2rem;
    padding: 2rem;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

    .success-icon {
      font-size: 4rem;
      color: #10b981;
      margin-bottom: 1rem;
      animation: successPulse 2s ease-in-out infinite;
    }

    h1 {
      margin: 0 0 1rem 0;
      color: #1f2937;
      font-size: 2.5rem;
      font-weight: 700;
    }

    p {
      margin: 0;
      color: #6b7280;
      font-size: 1.2rem;
      line-height: 1.5;
    }
  }

  @keyframes successPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
  }

  .booking-details-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
    overflow: hidden;

    .booking-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 2rem;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      flex-wrap: wrap;
      gap: 1rem;

      .booking-id {
        h2 {
          margin: 0 0 0.5rem 0;
          font-size: 1.8rem;
          font-weight: 600;
        }
      }

      .tracking-ref {
        text-align: right;

        .label {
          display: block;
          font-size: 0.9rem;
          opacity: 0.9;
          margin-bottom: 0.5rem;
        }

        .ref-code {
          display: block;
          background: rgba(255, 255, 255, 0.2);
          padding: 0.5rem 1rem;
          border-radius: 8px;
          font-family: 'Courier New', monospace;
          font-weight: bold;
          letter-spacing: 1px;
        }
      }
    }

    .booking-content {
      padding: 2rem;
    }
  }

  .status-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;

    &.pending {
      background: rgba(251, 191, 36, 0.2);
      color: #f59e0b;
    }
  }

  .route-section {
    margin-bottom: 2rem;

    h3 {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin: 0 0 1.5rem 0;
      color: #1f2937;
      font-size: 1.3rem;

      .section-icon {
        color: #667eea;
      }
    }

    .route-display {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 1.5rem;
      background: #f8f9fa;
      border-radius: 12px;
      border: 2px solid #e5e7eb;

      .route-point {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 1rem;

        .location-icon {
          font-size: 1.5rem;
          
          &.pickup-icon {
            color: #10b981;
          }
          
          &.dropoff-icon {
            color: #ef4444;
          }
        }

        .location-details {
          display: flex;
          flex-direction: column;

          .location-label {
            font-size: 0.8rem;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 0.25rem;
          }

          .location-address {
            color: #1f2937;
            font-weight: 500;
            line-height: 1.4;
          }
        }
      }

      .route-line {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 60px;

        .arrow-icon {
          color: #667eea;
          font-size: 1.5rem;
        }
      }
    }
  }

  .trip-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;

    .detail-item {
      padding: 1rem;
      background: #f8f9fa;
      border-radius: 8px;
      display: flex;
      align-items: center;
      gap: 0.75rem;

      .detail-icon {
        color: #667eea;
        font-size: 1.1rem;
      }

      .detail-content {
        display: flex;
        flex-direction: column;

        .detail-label {
          font-size: 0.8rem;
          color: #6b7280;
          font-weight: 500;
          margin-bottom: 0.25rem;
        }

        .detail-value {
          font-weight: 600;
          color: #1f2937;
        }
      }

      .detail-label {
        font-size: 0.8rem;
        color: #6b7280;
        font-weight: 500;
      }

      .detail-value {
        font-weight: 600;
        color: #1f2937;
      }

      &.fare-item {
        background: #f0f9ff;
        border: 2px solid #e0f2fe;

        .fare-amount {
          color: #10b981;
          font-size: 1.2rem;
        }
      }
    }
  }

  .contact-section {
    margin-bottom: 2rem;

    h3 {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin: 0 0 1rem 0;
      color: #1f2937;
      font-size: 1.1rem;

      .section-icon {
        color: #667eea;
      }
    }

    .contact-details {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;

      .contact-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem;
        background: #f8f9fa;
        border-radius: 8px;

        .contact-label {
          font-weight: 500;
          color: #6b7280;
        }

        .contact-value {
          font-weight: 600;
          color: #1f2937;
        }
      }
    }
  }

  .next-steps {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

    h3 {
      margin: 0 0 1.5rem 0;
      color: #1f2937;
      font-size: 1.5rem;
      text-align: center;
    }

    .steps-list {
      display: flex;
      justify-content: space-between;
      gap: 1rem;

      .step {
        flex: 1;
        text-align: center;
        position: relative;

        &:not(:last-child)::after {
          content: '';
          position: absolute;
          top: 25px;
          right: -50%;
          width: 100%;
          height: 2px;
          background: #e5e7eb;
          z-index: 1;
        }

        .step-number {
          width: 50px;
          height: 50px;
          border-radius: 50%;
          background: #667eea;
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: bold;
          font-size: 1.2rem;
          margin: 0 auto 1rem auto;
          position: relative;
          z-index: 2;
        }

        .step-content {
          h4 {
            margin: 0 0 0.5rem 0;
            color: #1f2937;
            font-size: 1.1rem;
          }

          p {
            margin: 0;
            color: #6b7280;
            font-size: 0.9rem;
            line-height: 1.4;
          }
        }
      }
    }
  }

  .action-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    justify-content: center;

    .btn {
      padding: 0.875rem 2rem;
      border-radius: 8px;
      font-weight: 600;
      text-decoration: none;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      transition: all 0.3s ease;
      font-size: 1rem;

      &.btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
      }

      &.btn-secondary {
        background: white;
        color: #667eea;
        border: 2px solid #667eea;

        &:hover {
          background: #667eea;
          color: white;
        }
      }
    }
  }

  .auto-redirect {
    text-align: center;
    padding: 1rem;
    background: #fef3c7;
    border-radius: 8px;
    border: 1px solid #f59e0b;
    margin-bottom: 2rem;

    p {
      margin: 0 0 0.5rem 0;
      color: #92400e;
      font-weight: 500;
    }

    .skip-btn {
      background: none;
      border: none;
      color: #f59e0b;
      font-weight: 600;
      cursor: pointer;
      text-decoration: underline;

      &:hover {
        color: #d97706;
      }
    }
  }

  .important-notice {
    background: #f0f9ff;
    border: 2px solid #e0f2fe;
    border-radius: 12px;
    padding: 1.5rem;

    h4 {
      margin: 0 0 1rem 0;
      color: #0369a1;
      font-size: 1.1rem;
    }

    ul {
      margin: 0;
      padding-left: 1.5rem;

      li {
        color: #374151;
        margin-bottom: 0.5rem;
        line-height: 1.5;

        strong {
          color: #1f2937;
        }
      }
    }
  }

  .booking-error {
    text-align: center;
    padding: 4rem 2rem;
    color: #6b7280;

    h2 {
      color: #1f2937;
      margin-bottom: 1rem;
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    padding: 1rem 0;

    .confirmation-container {
      padding: 0 0.5rem;
    }

    .success-header {
      padding: 1.5rem;

      .success-icon {
        font-size: 3rem;
      }

      h1 {
        font-size: 2rem;
      }

      p {
        font-size: 1rem;
      }
    }

    .booking-details-card .booking-header {
      padding: 1.5rem 1rem;
      flex-direction: column;
      align-items: center;
      text-align: center;

      .tracking-ref {
        text-align: center;
      }
    }

    .booking-content {
      padding: 1rem;
    }

    .route-display {
      flex-direction: column;
      text-align: center;

      .route-line {
        transform: rotate(90deg);
        margin: 0.5rem 0;
      }
    }

    .trip-details {
      grid-template-columns: 1fr;
    }

    .contact-details {
      grid-template-columns: 1fr;
    }

    .next-steps {
      padding: 1.5rem 1rem;

      .steps-list {
        flex-direction: column;
        gap: 2rem;

        .step {
          &:not(:last-child)::after {
            display: none;
          }
        }
      }
    }

    .action-buttons {
      flex-direction: column;

      .btn {
        justify-content: center;
      }
    }
  }

  @media (max-width: 480px) {
    .success-header {
      h1 {
        font-size: 1.5rem;
      }
    }

    .detail-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }
  }
}
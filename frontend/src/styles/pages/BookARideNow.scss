// frontend/src/styles/pages/BookARideNow.scss
@use "../variables.scss" as *;

body {
  margin: 0;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.ridenow-page {
  margin: $spacing-xxxl auto $spacing-lg auto; // Center with 80px top, 30px bottom
  padding: $spacing-md; // 20px
  font-family: $font-primary; // Visby CF
  flex: 1 0 auto;
  max-width: 800px; // Cap width for better control

  .ride-booking {
    background-color: $light-gray; // #f8f9fa
    border-radius: $border-radius-lg; // 10px
    box-shadow: $box-shadow-lg; // 0 4px 8px rgba(0, 0, 0, 0.1)
    padding: $spacing-lg; // 30px for a bit more breathing room

    h1 {
      font-size: $font-size-xl; // 2em
      color: $primary-blue; // #002B7F
      margin-bottom: $spacing-sm; // 10px
      text-align: center;
    }

    .subtitle {
      font-size: $font-size-sm; // 14px
      color: $primary-blue-dark; // #003580
      margin-bottom: $spacing-lg; // 30px
      text-align: center;
    }

    .ride-form {
      display: flex;
      flex-direction: column;
      gap: $spacing-md; // 20px for better spacing
      max-width:750px; // Slightly smaller than parent

      .input-group {
        display: flex;
        flex-direction: column;
        align-items: center; // Center inputs and labels

        label {
          font-size: $font-size-md; // 16px
          color: $primary-blue; // #002B7F
          font-weight: $font-weight-bold; // 500
          margin-bottom: $spacing-xs; // 5px
        }

        input, .react-datepicker-wrapper {
          width: 250px; // Fixed width for consistency
          max-width: 100%; // Prevent overflow
        }

        input {
          padding: $spacing-sm $spacing-md; // 10px 20px
          font-size: $font-size-md; // 16px
          border: 1px solid $gray-border; // #5B5B5B
          border-radius: $border-radius-sm; // 4px
          outline: none;
          transition: border-color $transition-default; // 0.3s ease

          &:focus {
            border-color: $button-primary-bg; // #005EB8
            box-shadow: 0 0 0 2px rgba(0, 94, 184, 0.2);
          }

          &::placeholder {
            color: $gray-border; // #5B5B5B
            opacity: 0.7;
          }

          &[type="number"] {
            width: 100px; // Smaller width for number inputs
            appearance: textfield;
            -moz-appearance: textfield;
            &::-webkit-outer-spin-button,
            &::-webkit-inner-spin-button {
              -webkit-appearance: none;
              margin: 0;
            }
          }
        }

        .location-input-wrapper {
          display: flex;
          align-items: center;
          gap: $spacing-sm; // 10px spacing between input and button

          input {
            flex: 1; // Input takes up remaining space
          }

          button {
            padding: $spacing-xs $spacing-sm; // 5px 10px
            font-size: $font-size-sm; // 14px
            background-color: $button-secondary-bg; // #E6E6E6
            color: $button-secondary-text; // #000000
            border: none;
            border-radius: $border-radius-sm; // 4px
            cursor: pointer;
            transition: background-color $transition-default; // 0.3s ease

            &:hover {
              background-color: $button-secondary-hover; // #D1D1D1
            }

            &:disabled {
              background-color: $button-secondary-active; // #B8B8B8
              cursor: not-allowed;
            }
          }
        }
      }

      .options-group, .datetime-group {
        display: flex;
        justify-content: center; // Center the group
        gap: $spacing-md; // 20px
      }

      .picker-wrapper {
        display: flex;
        align-items: center;
        gap: $spacing-sm; // 10px

        .react-datepicker-wrapper {
          flex: 1;
        }

        .react-datepicker__input-container input {
          width: 100%;
          padding: $spacing-sm $spacing-md; // 10px 20px
          font-size: $font-size-md; // 16px
          border: 1px solid $gray-border; // #5B5B5B
          border-radius: $border-radius-sm; // 4px
          outline: none;
          transition: border-color $transition-default;

          &:focus {
            border-color: $button-primary-bg; // #005EB8
            box-shadow: 0 0 0 2px rgba(0, 94, 184, 0.2);
          }
        }

        button {
          padding: $spacing-xs $spacing-sm; // 5px 10px
          font-size: $font-size-sm; // 14px
          background-color: $button-secondary-bg;
          color: $button-secondary-text;
          border: none;
          border-radius: $border-radius-sm; // 4px
          cursor: pointer;
          transition: background-color $transition-default;

          &:hover {
            background-color: $button-secondary-hover;
          }
        }
      }

      .book-button {
        width: 250px; // Match input width
        align-self: center; // Center the button
        padding: $spacing-md; // 20px
        font-size: $font-size-lg; // 18px
        background-color: $button-accent-bg; // #FFB107
        color: $button-accent-text; // #000000
        border: none;
        border-radius: $border-radius-md; // 5px
        cursor: pointer;
        transition: background-color $transition-default;
        font-weight: $font-weight-bold;

        &:hover {
          background-color: $button-accent-hover; // #FFA000
        }
      }
    }
  }
}

footer {
  flex-shrink: 0;
  margin-top: $spacing-xl; // 40px
}

/* Mobile Responsiveness */
@media (max-width: 600px) {
  .ridenow-page {
    margin: $spacing-xxxl $spacing-md $spacing-lg $spacing-md; // Adjust sides
    padding: $spacing-md; // 20px

    .ride-booking {
      padding: $spacing-md; // 20px

      h1 {
        font-size: $font-size-lg; // 18px
      }

      .subtitle {
        font-size: $font-size-sm; // 14px
      }

      .ride-form {
        gap: $spacing-sm; // 10px for tighter spacing

        .options-group, .datetime-group {
          flex-direction: column; // Stack vertically
          gap: $spacing-sm; // 10px
        }

        .input-group {
          align-items: stretch; // Full width on mobile
        }

        input, .react-datepicker-wrapper {
          width: 100%; // Full width on mobile
        }

        .location-input-wrapper {
          flex-direction: column; // Stack vertically on mobile
          align-items: stretch;

          button {
            width: 100%; // Full width button
            margin-top: $spacing-xs; // 5px spacing above button
          }
        }

        .picker-wrapper {
          flex-direction: column; // Stack picker and button
          align-items: stretch;

          button {
            width: 100%; // Full width button
            margin-top: $spacing-xs; // 5px
          }
        }

        .book-button {
          width: 100%; // Full width
          padding: $spacing-sm $spacing-md; // 10px 20px
          font-size: $font-size-md; // 16px
        }
      }
    }
  }
}
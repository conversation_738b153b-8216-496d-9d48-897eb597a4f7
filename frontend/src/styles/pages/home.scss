@use "../variables.scss" as *;
@use '../components/blades/navbar.scss';
@use '../components/blades/footer.scss';



/* ============================= */
/* Hero Section with Video Background */
/* ============================= */
.hero {
  position: relative;
  text-align: center;
  padding: $spacing-xxl $spacing-md;
  overflow: hidden;
}

.hero-video {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 160%;
  object-fit: cover;
  transform: translate(-50%, -67.5%);
  z-index: -1;
}

.hero-content {
  position: relative;
  color: $white;
  z-index: 1;
}

/* Typography for hero section */
.hero-content h1 {
  font-size: $font-size-xxl;
  margin-bottom: $spacing-sm;
}

.hero-content p {
  font-size: $font-size-xl;
  margin-bottom: $spacing-md;
}

/* ============================= */
/* CTA Buttons (merged styling) */
/* ============================= */
.cta-buttons {
  display: flex;
  justify-content: center;
  gap: $spacing-md;
  margin-top: $spacing-md;
}

.cta-button {
  display: inline-block;
  padding: $spacing-sm $spacing-md;
  margin: 0 $spacing-sm;
  font-size: $font-size-lg;
  font-weight: $font-weight-bolder;
  text-align: center;
  text-decoration: none;
  color: $button-primary-text; // Updated to use button-specific text color
  background-color: $button-primary-bg; // Updated to new primary button color
  border-radius: $border-radius-md;
  transition: $transition-default;
  cursor: pointer;
}

.cta-button:hover {
  background-color: $button-primary-hover; // Updated to button hover state
  color: $button-primary-text; // Consistent text color
  text-decoration: none;
}

/* ============================= */
/* Booking Section */
/* ============================= */
.booking {
  text-align: center;
  padding: $spacing-xl $spacing-md;
  background-color: $light-gray;
}

.booking h2 {
  margin-bottom: $spacing-md;
}

.booking-form {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $spacing-sm;
}

.booking-form input {
  width: 80%;
  max-width: 400px;
  padding: $spacing-sm;
  border: 1px solid $gray-border;
  border-radius: $border-radius-md;
}

.booking-form button {
  background-color: $button-primary-bg; // Updated to new primary button color
  color: $button-primary-text; // Updated to button-specific text color
  border: none;
  padding: $spacing-sm $spacing-md;
  cursor: pointer;
  border-radius: $border-radius-md;
  transition: $transition-default;
}

.booking-form button:hover {
  background-color: $button-primary-hover; // Updated to button hover state
}

/* ============================= */
/* Services Section */
/* ============================= */
.services {
  text-align: center;
  padding: $spacing-xl $spacing-md;
  background-color: $white;

  h2 {
    font-size: $font-size-xl;
    color: $primary-blue-dark-20;
    margin-bottom: $spacing-md;
  }

  .service-cards {
    display: flex;
    justify-content: center;
    gap: $spacing-md;
    flex-wrap: wrap;
  }

  .service-card {
    background-color: $light-gray;
    padding: $spacing-md;
    border-radius: $border-radius-lg;
    width: 250px;
    box-shadow: $box-shadow-lg;
    transition: $transition-default;

    &:hover {
      transform: translateY(-5px);
    }

    .service-icon {
      font-size: $font-size-xl;
      color: $primary-blue;
      margin-bottom: $spacing-md;
    }

    h3 {
      font-size: $font-size-lg;
      color: $primary-blue-dark;
      margin-bottom: $spacing-sm;
    }

    p {
      font-size: $font-size-md;
      color: $black-light-40;
    }
  }
}

/* ============================= */
/* Signup Benefits Section */
/* ============================= */
.signup-benefits {
  text-align: center;
  padding: $spacing-xl $spacing-md;
  background-color: $light-gray;

  h2 {
    font-size: $font-size-xl;
    color: $primary-blue-dark-20;
    margin-bottom: $spacing-lg;
  }

  .benefits-cards {
    display: flex;
    justify-content: center;
    gap: $spacing-lg;
    flex-wrap: wrap;
    margin-bottom: $spacing-lg;
  }

  .benefit-card {
    background-color: $white;
    border-radius: $border-radius-md;
    box-shadow: $box-shadow-md;
    padding: $spacing-lg;
    width: 250px;
    transition: transform $transition-default;

    &:hover {
      transform: translateY(-5px);
    }

    .benefit-icon {
      font-size: $font-size-xl;
      color: $primary-blue;
      margin-bottom: $spacing-md;
    }

    h3 {
      font-size: $font-size-lg;
      color: $primary-blue-dark;
      margin-bottom: $spacing-sm;
    }

    p {
      font-size: $font-size-md;
      color: $black-light-40;
    }
  }

  .signup-cta {
    background-color: $primary-blue-dark;
    color: $white;
    font-size: $font-size-lg;
    padding: $spacing-md $spacing-xl;
    border: none;
    border-radius: $border-radius-md;
    text-decoration: none;
    display: inline-block;
    font-weight: $font-weight-bold;
    transition: background-color $transition-default;

    &:hover {
      background-color: $primary-blue-dark-20;
    }
  }
}

/* ============================= */
/* API Test Section */
/* ============================= */
.api-test {
  text-align: center;
  padding: $spacing-xl;
  background-color: $accent-yellow;
  color: $black;
}
.instant-ride-form {
  h2 {
    text-align: center;
  }
}

/*Below are Beta stuffs to test*/
.instant-ride-title , .services-title {
  color:$primary-blue-dark-20
}

.logo {
    display: flex;
    align-items: center;
  }
  
  .logo-image {
    height: 40px;
    width: auto; /* Adjust the size as needed */
    margin-right: 10px; /* Space between the image and text */
  }

.map-section {
    padding: 20px;
    text-align: center;
  
    h2 {
      margin-bottom: 20px;
      color:$primary-blue-dark-20
    }
  }

/* Mobile Responsive Styles */
@media only screen and (max-width: 768px) {
  .hero {
    padding: $spacing-md $spacing-sm;
    margin-top: $navbar-height-mobile
  }

  .hero-content h1 {
    font-size: $font-size-xl;
  }

  .hero-content p {
    font-size: $font-size-lg;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
    gap: $spacing-sm;
  }

  .cta-button {
    width: 80%;
    max-width: 300px;
    text-align: center;
  }

  .instant-ride-form {
    padding: $spacing-md $spacing-sm;
  }

  .services {
    padding: $spacing-md $spacing-sm;
  }

  .service-cards {
    flex-direction: column;
    align-items: center;
    gap: $spacing-md;
  }

  .service-card {
    width: 90%;
    max-width: 300px;
  }

  .api-test {
    padding: $spacing-md $spacing-sm;
  }
}

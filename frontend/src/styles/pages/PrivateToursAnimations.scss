// frontend/src/styles/pages/PrivateToursAnimations.scss
@use "../variables" as *;

.private-tours {
  .tour-card {
    &:hover {
      transform: translateY(-10px);
      box-shadow: $box-shadow-lg;
    }

    .tour-image img {
      transition: transform $transition-default;
    }

    &:hover .tour-image img {
      transform: scale(1.05); // Slight zoom on hover
    }

    .more-info-btn {
      position: relative;
      overflow: hidden;

      &:after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba($button-primary-hover, 0.3);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: width $transition-default, height $transition-default;
      }

      &:hover:after {
        width: 200%;
        height: 200%;
      }
    }
  }
}
// frontend/src/styles/pages/about/OurFleet.scss
@use "../../variables.scss" as *;

.fleet-page {
  padding: $spacing-md;
  max-width: 1200px;
  margin: 0 auto;

  h1 {
    font-family: $font-primary;
    font-size: $font-size-xxl;
    font-weight: $font-weight-bolder;
    color: $primary-blue-dark;
    margin-bottom: $spacing-lg;
  }

  .text-lg {
    font-family: $font-secondary;
    font-size: $font-size-lg;
    color: $primary-blue;
    margin-bottom: $spacing-lg;
  }

  .fleet-gallery {
    display: grid;
    grid-template-columns: repeat(4, 1fr); // 4 per row
    gap: $spacing-md;
  }

  .vehicle-card {
    background-color: $light-gray;
    border-radius: $border-radius-lg;
    box-shadow: $box-shadow-md;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative; // For potential overflow effects

    .vehicle-image {
      width: 100%;
      height: 200px; // Fixed height for consistency
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: $gray-border; // Placeholder bg

      img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain; // Keeps logo proportions
      }
    }

    .vehicle-details {
      padding: $spacing-md;

      .vehicle-name {
        font-family: $font-primary;
        font-size: $font-size-lg;
        font-weight: $font-weight-bold;
        color: $primary-blue-darker;
        margin-bottom: $spacing-sm;
      }

      .vehicle-info {
        display: flex;
        flex-direction: column;
        gap: $spacing-sm;
        margin-bottom: $spacing-sm;

        .info-item {
          display: flex;
          justify-content: space-between;
          font-family: $font-secondary;
          font-size: $font-size-sm;

          .label {
            font-weight: $font-weight-bold;
            color: $primary-blue-dark;
          }

          .value {
            color: $black;
          }
        }
      }

      .vehicle-description {
        font-family: $font-secondary;
        font-size: $font-size-md;
        color: $black;
        line-height: 1.5;
      }
    }
  }

  @media (max-width: 1024px) {
    .fleet-gallery {
      grid-template-columns: repeat(2, 1fr); // 2 per row on tablets
    }
  }

  @media (max-width: 768px) {
    padding: $spacing-sm;

    h1 {
      font-size: $font-size-xl;
    }

    .fleet-gallery {
      grid-template-columns: 1fr; // 1 per row on mobile
    }

    .vehicle-card {
      .vehicle-image {
        height: 150px; // Smaller on mobile
      }
    }
  }
}
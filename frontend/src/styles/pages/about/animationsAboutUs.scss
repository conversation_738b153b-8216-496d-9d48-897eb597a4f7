// frontend/src/styles/pages/about/animationsAboutUs.scss
@use "../../variables.scss" as *;

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInLeft {
  from { transform: translateX(-50px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInRight {
  from { transform: translateX(50px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes cardBounce {
  0% { transform: translateY(30px); opacity: 0; }
  60% { transform: translateY(-5px); opacity: 1; }
  100% { transform: translateY(0); opacity: 1; }
}

@keyframes subtleSway {
  0% { transform: rotate(0deg); }
  50% { transform: rotate(1deg); }
  100% { transform: rotate(0deg); }
}

.animate-fade-in {
  animation: fadeIn 1s ease-in-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.8s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.8s ease-out;
}

.animate-team-card {
  animation: cardBounce 0.8s ease-out forwards;
  @for $i from 1 through 3 {
    &:nth-child(#{$i}) {
      animation-delay: #{$i * 0.2}s; // Staggered entrance
    }
  }
  // Subtle sway after entrance
  &:hover {
    animation: subtleSway 2s ease-in-out infinite;
  }
}
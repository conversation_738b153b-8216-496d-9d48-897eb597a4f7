// frontend/src/styles/pages/about/contactUs.scss

@use "../../variables.scss" as *;

// Scoped to the contact-page class to avoid global conflicts
.contact-page {
  padding: $spacing-md;
  max-width: 1200px;
  margin: 0 auto;

  h1 {
    font-family: $font-primary;
    font-size: $font-size-xxl;
    font-weight: $font-weight-bolder;
    color: $primary-blue-dark;
    margin-bottom: $spacing-lg;
  }

  p {
    font-family: $font-secondary;
    font-size: $font-size-md;
    color: $black;
  }

  // Intro tagline
  .text-lg {
    font-size: $font-size-lg;
    color: $primary-blue;
    margin-bottom: $spacing-md;
  }

  // Business Card Contact Info
  .contact-info-card {
    background-color: $white;
    border-radius: $border-radius-lg;
    box-shadow: $box-shadow-md;
    padding: $spacing-lg;
    margin-bottom: $spacing-xl;
    display: flex;
    flex-wrap: wrap;
    border: 3px double $primary-blue-dark; // Double border for document style
    background: linear-gradient(135deg, $white 70%, $light-gray 100%);

    .contact-card-left {
      h2 {
        font-family: $font-primary;
        font-size: $font-size-xl;
        font-weight: $font-weight-bolder;
        color: $primary-blue;
        margin-bottom: $spacing-sm;
      }

      .permit-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: $spacing-md;
        border-bottom: 2px solid $primary-blue; // Section divider
        padding-bottom: $spacing-sm;

        .permit-badge {
          background-color: $accent-yellow;
          color: $black;
          padding: $spacing-xs $spacing-sm;
          border-radius: 9999px;
          font-size: $font-size-sm;
          font-weight: $font-weight-bold;
          text-transform: uppercase;
        }
      }

      .permit-id {
        font-size: $font-size-sm;
        color: $gray-border;
        margin-bottom: $spacing-md;
        font-style: italic;
      }

      .permit-details-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: $spacing-md;

        .permit-detail-item {
          p {
            margin: 0;
          }
        }
      }

      p {
        font-size: $font-size-md;
        line-height: 1.6;

        .icon-circle {
          font-size: 1rem;
        }

        a {
          color: $primary-blue;
          transition: $transition-default;

          &:hover {
            color: $primary-blue-darkest;
            text-decoration: underline;
          }
        }

        .text-sm {
          font-size: $font-size-sm;
          color: $gray-border;
        }
      }
    }

    .contact-card-right {
      display: flex;
      flex-direction: column;
      align-items: center;

      .permit-stamp {
        background-color: $primary-blue-dark;
        color: $white;
        border-radius: 50%;
        width: 6rem;
        height: 6rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        margin-bottom: $spacing-md;
        font-weight: $font-weight-bold;
        transform: rotate(-5deg); // Slight tilt for authenticity
        border: 2px solid $accent-yellow;

        .stamp-valid {
          font-size: $font-size-lg;
          font-weight: $font-weight-bolder;
        }
        .stamp-location {
          font-size: $font-size-md;
        }
        .stamp-year {
          font-size: $font-size-sm;
        }
      }

      .social-links {
        display: flex;
        justify-content: center;

        .social-icon {
          transition: $transition-default;
          margin: 0 $spacing-sm;

          &:first-child {
            margin-left: 0;
          }

          &:last-child {
            margin-right: 0;
          }

          &.facebook {
            color: $primary-blue //$facebook-blue;
          }

          &.twitter {
            color: $primary-blue//$twitter-blue;
          }

          &.instagram {
            color: $primary-blue//$instagram-pink;
          }
        }
      }
    }
  }

  // Contact Form Section
  .contact-form-section {
    background-color: $light-gray;
    padding: $spacing-lg;
    border-radius: $border-radius-lg;
    box-shadow: $box-shadow-md;
    margin-bottom: $spacing-xl;

    h2 {
      font-family: $font-primary;
      font-size: $font-size-xl;
      font-weight: $font-weight-bold;
      color: $primary-blue-dark;
      margin-bottom: $spacing-md;
    }

    form {
      display: flex;
      flex-direction: column;
      gap: $spacing-md;

      .form-group {
        label {
          font-family: $font-secondary;
          font-size: $font-size-sm;
          font-weight: $font-weight-bold;
          color: $primary-blue-darker;
          margin-bottom: $spacing-sm;
        }

        input,
        textarea {
          width: 100%;
          padding: $spacing-sm;
          border: 1px solid $gray-border;
          border-radius: $border-radius-md;
          font-family: $font-secondary;
          font-size: $font-size-md;
          color: $black;
          background-color: $white;
          transition: $transition-default;

          &:focus {
            border-color: $primary-blue;
            box-shadow: 0 0 5px $shadow-dark;
            outline: none;
          }

          &::placeholder {
            color: $gray-border;
          }
        }

        textarea {
          resize: vertical;
          min-height: 120px;
        }
      }

      button {
        background-color: $button-primary-bg;
        color: $button-primary-text;
        padding: $spacing-sm $spacing-md;
        border: none;
        border-radius: $border-radius-md;
        font-family: $font-primary;
        font-size: $font-size-md;
        font-weight: $font-weight-bold;
        cursor: pointer;
        transition: $transition-default;

        &:hover {
          background-color: $button-primary-hover;
          box-shadow: $box-shadow-sm;
        }

        &:active {
          background-color: $button-primary-active;
        }

        &:disabled {
          background-color: $gray-border;
          cursor: not-allowed;
        }
      }
    }
  }

  // Map Section
  .map-section {
    margin-top: $spacing-xl;

    h2 {
      font-family: $font-primary;
      font-size: $font-size-xl;
      font-weight: $font-weight-bold;
      color: $primary-blue-dark;
      margin-bottom: $spacing-md;
    }

    p {
      font-size: $font-size-md;
      color: $black;
      margin-bottom: $spacing-md;
    }

    .map-placeholder {
      height: 20rem;
      background-color: $gray-border;
      border-radius: $border-radius-lg;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      position: relative;
      overflow: hidden;

      p {
        font-family: $font-secondary;
        color: $white;
        z-index: 10;
      }
    }
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    padding: $spacing-sm;

    h1 {
      font-size: $font-size-xl;
    }

    .contact-info-card,
    .contact-form-section,
    .map-section {
      padding: $spacing-md;

      h2 {
        font-size: $font-size-lg;
      }
    }

    .contact-info-card {
      flex-direction: column;
      align-items: center;
      text-align: center;

      .contact-card-right {
        width: 100%;
        margin-top: $spacing-md;
      }
    }
  }
}
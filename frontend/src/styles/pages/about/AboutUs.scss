// frontend/src/styles/pages/about/AboutUs.scss
@use "../../variables.scss" as *;

.page-content {
  padding-top: $navbar-height; // Pushes content below navbar
}

.about-page {
  padding: $spacing-md;
  max-width: 1200px;
  margin: 0 auto;
  background: linear-gradient(to bottom, rgba($button-primary-bg, 0.1), rgba($accent-yellow, 0.1)); // Subtle island gradient

  h1 {
    font-family: $font-primary;
    font-size: $font-size-xxl;
    font-weight: $font-weight-bolder;
    color: $primary-blue-dark;
    margin-bottom: $spacing-lg;
    text-shadow: 1px 1px 3px rgba($black, 0.2); // Depth effect
    position: relative;

    &::after { // Decorative underline
      content: "";
      position: absolute;
      bottom: -10px;
      left: 0;
      width: 80px;
      height: 4px;
      background: linear-gradient(to right, $primary-blue, $accent-yellow);
      border-radius: 2px;
    }
  }

  .about-section {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: $spacing-lg;
    margin-bottom: $spacing-xl;
    background-color: $white;
    border-radius: $border-radius-lg;
    box-shadow: 0 10px 30px rgba($primary-blue-dark, 0.1); // Soft card shadow
    overflow: hidden;
    transform: translateY(20px);
    opacity: 0.3;
    transition: transform 0.8s ease-out, opacity 0.8s ease-out;

    &.in-view {
      transform: translateY(0);
      opacity: 1; // Fade in and slide up on scroll
    }

    .section-image {
      flex: 1 1 300px;
      max-width: 400px;
      border-radius: $border-radius-lg 0 0 $border-radius-lg;
      object-fit: cover;
      width: 100%;
      height: 100%;
      box-shadow: 5px 0 15px rgba($black, 0.1);
      transition: transform $transition-default;

      &:hover {
        transform: scale(1.03); // Subtle zoom on hover
      }
    }

    .section-text {
      flex: 1 1 300px;
      padding: $spacing-md;

      h2 {
        font-family: $font-primary;
        font-size: $font-size-xl;
        font-weight: $font-weight-bold;
        color: $primary-blue-darker;
        margin-bottom: $spacing-sm;
        position: relative;

        &::before { // Icon-like decor
          content: "🌴"; // Island vibe emoji
          margin-right: 8px;
          font-size: $font-size-lg;
        }
      }

      p {
        font-family: $font-secondary;
        font-size: $font-size-md;
        color: $black;
        line-height: 1.8;
        //Betterreadabilitybackground: rgba($primary-blue-light, 0.05);
        //Subtlebgtintpadding: $spacing-sm;
        border-radius: $border-radius-md;
        border-left: 3px solid $primary-blue;
        padding-left: $spacing-sm; // Added spacing to the left of the blue border
         // Highlight bar      :;
        -webkit-border-radius: $border-radius-md;
        -moz-border-radius: $border-radius-md;
        -ms-border-radius: $border-radius-md;
        -o-border-radius: $border-radius-md;
}
    }

    &.reverse {
      flex-direction: row-reverse;

      .section-image {
        border-radius: 0 $border-radius-lg $border-radius-lg 0;
        box-shadow: -5px 0 15px rgba($black, 0.1);
      }
    }
  }

  .team-section {
    margin-bottom: $spacing-xl;
    background-color: $light-gray;
    padding: $spacing-lg;
    border-radius: $border-radius-lg;
    box-shadow: inset 0 0 10px rgba($primary-blue, 0.05); // Inner glow
    transform: translateY(20px);
    opacity: 0.3;
    transition: transform 0.8s ease-out, opacity 0.8s ease-out;

    &.in-view {
      transform: translateY(0);
      opacity: 1;
    }

    h2 {
      font-family: $font-primary;
      font-size: $font-size-xl;
      font-weight: $font-weight-bold;
      color: $primary-blue-darker;
      margin-bottom: $spacing-lg;
      text-align: center;
      position: relative;

      &::after { // Decorative wave
        content: "~";
        position: absolute;
        bottom: -5px;
        left: 50%;
        transform: translateX(-50%);
        font-size: $font-size-lg;
        color: $primary-blue;
        letter-spacing: 3px;
        width: 100px;
        display: block;
        opacity: 0.5;
      }
    }

    .team-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: $spacing-md;

      .team-member {
        display: flex;
        flex-direction: column;
        align-items: center;
        background-color: $white;
        padding: $spacing-md;
        border-radius: $border-radius-md;
        box-shadow: 0 5px 15px rgba($black, 0.05), 0 0 0 2px rgba($primary-blue, 0.2); // Subtle blue border
        transition: transform $transition-default, box-shadow $transition-default, border-color $transition-default;
        position: relative;
        overflow: hidden;

        &::before { // Gradient overlay on hover
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: linear-gradient(to top, rgba($primary-blue, 0.1), transparent);
          opacity: 0;
          transition: opacity $transition-default;
        }

        &:hover {
          transform: scale(1.05) rotate(2deg); // Scale and slight tilt
          box-shadow: 0 15px 30px rgba($black, 0.1);
          border-color: $primary-blue; // Brighten border on hover

          &::before {
            opacity: 1;
          }
        }

        .team-image {
          width: 200px;
          height: 200px;
          object-fit: contain;
          border-radius: $border-radius-lg;
          margin-bottom: $spacing-sm;
          transition: transform $transition-default, box-shadow $transition-default;
          border: 3px solid rgba($accent-yellow, 0.5);
        }

        &:hover .team-image {
          transform: translateY(-5px) scale(1.02); // Lift image on hover
          box-shadow: 0 5px 15px rgba($black, 0.2);
        }

        h3 {
          font-family: $font-primary;
          font-size: $font-size-lg;
          font-weight: $font-weight-bold;
          color: $primary-blue-dark;
          text-align: center;
          position: relative;
          margin-bottom: $spacing-sm;
          z-index: 1;

          &::after { // Underline effect
            content: "";
            position: absolute;
            bottom: -4px;
            left: 50%;
            transform: translateX(-50%);
            width: 50px;
            height: 2px;
            background-color: $accent-yellow;
            border-radius: 2px;
          }
        }

        p {
          font-family: $font-secondary;
          font-size: $font-size-md;
          color: $gray-border;
          text-align: center;
          z-index: 1;
        }

        .text-sm {
          font-size: $font-size-sm;
          color: $gray-border-dark-10; // Replaced darken($gray-border, 10%) with pre-calculated color
          padding: 2px 8px;
          border-radius: 12px;
        }
      }
    }
  }

  @media (max-width: 768px) {
    padding: $spacing-sm;

    h1 {
      font-size: $font-size-xl;
    }

    .about-section {
      flex-direction: column;
      gap: $spacing-md;

      &.reverse {
        flex-direction: column;
      }

      .section-image {
        max-width: 100%;
        border-radius: $border-radius-lg $border-radius-lg 0 0;
      }
    }

    .team-section {
      padding: $spacing-md;
      .team-grid {
        grid-template-columns: 1fr;
        gap: $spacing-sm;

        .team-image {
          width: 150px;
          height: 150px;
        }
      }
    }
  }
}
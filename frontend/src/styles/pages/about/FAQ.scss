// frontend/src/styles/pages/about/FAQ.scss
@use "../../variables.scss" as *;

.faq-page {
  padding: $spacing-md;
  max-width: 1200px;
  margin: 0 auto;

  h1 {
    font-family: $font-primary;
    font-size: $font-size-xxl;
    font-weight: $font-weight-bolder;
    color: $primary-blue-dark;
    margin-bottom: $spacing-lg;
  }

  p {
    font-family: $font-secondary;
    font-size: $font-size-md;
    color: $black;
  }

  .text-lg {
    font-size: $font-size-lg;
    color: $primary-blue;
    margin-bottom: $spacing-lg;
  }

  .faq-list {
    .faq-item {
      background-color: $light-gray;
      padding: $spacing-md;
      border-radius: $border-radius-md;
      box-shadow: $box-shadow-sm;

      h2 {
        font-family: $font-primary;
        font-size: $font-size-xl;
        font-weight: $font-weight-bold;
        color: $primary-blue-darker;
        margin-bottom: $spacing-sm;
      }

      p {
        font-size: $font-size-md;
        line-height: 1.6;

        a {
          color: $primary-blue;
          transition: $transition-default;
          &:hover {
            color: $primary-blue-darkest;
            text-decoration: underline;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .faq-page {
    padding: $spacing-sm;

    h1 {
      font-size: $font-size-xl;
    }

    .faq-list .faq-item {
      padding: $spacing-sm;

      h2 {
        font-size: $font-size-lg;
      }
    }
  }
}
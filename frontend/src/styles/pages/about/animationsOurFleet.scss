// frontend/src/styles/pages/about/animationsOurFleet.scss
@use "../../variables.scss" as *;

// Keyframes for entrance animation
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

// Animation styles for fleet page
.fleet-page {
  .fleet-gallery {
    .vehicle-card {
      // Entrance animation
      animation: fadeInUp 0.6s ease-out forwards;
      // Stagger the animation for each card
      @for $i from 1 through 4 {
        &:nth-child(#{$i}) {
          animation-delay: #{$i * 0.15}s;
        }
      }

      // Smooth transitions for hover effects
      transition: transform 0.3s ease, box-shadow 0.3s ease;

      &:hover {
        transform: scale(1.05) rotate(1deg); // Slight scale and playful tilt
        box-shadow: 0 8px 20px rgba($primary-blue, 0.4); // Colorful glow
      }

      .vehicle-image {
        cursor: pointer; // Indicate clickable image
        transition: opacity 0.3s ease;

        &:hover {
          opacity: 0.9; // Subtle dimming to suggest interaction
        }
      }

      .vehicle-details {
        .vehicle-name {
          transition: color 0.3s ease;
        }

        &:hover .vehicle-name {
          color: $primary-blue; // Brighten name on hover
        }
      }
    }
  }
}
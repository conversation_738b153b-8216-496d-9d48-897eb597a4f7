// frontend/src/styles/components/maps/InstantRideForm.scss
@use "../../variables.scss" as *;

.ride-form {
  max-width: 400px;
  margin: 0 auto;
  padding: $spacing-md;
  background-color: $white;
  border-radius: $border-radius-md;
  box-shadow: $box-shadow-sm;
}

.input-group {
  margin-bottom: $spacing-md;

  label {
    display: block;
    margin-bottom: $spacing-xs;
    font-weight: $font-weight-bolder;
    color: $primary-blue-dark;
    font-size: $font-size-sm;
  }

  input,
  .datetime-input {
    width: 100%;
    max-width: 300px;
    padding: $spacing-sm;
    border: 1px solid $gray-light;
    border-radius: $border-radius-sm;
    font-size: $font-size-md;
    outline: none;
    transition: border-color $transition-default;
    box-sizing: border-box;

    &:focus {
      border-color: $primary-blue;
    }
  }
}

.location-input-wrapper,
.datetime-wrapper {
  display: flex;
  align-items: center;
  gap: $spacing-sm;

  input,
  .datetime-input {
    flex: 1;
    max-width: 300px;
  }

  .location-btn,
  .now-btn {
    width: 36px;
    height: 36px;
    padding: 0;
    background-color: $button-primary-bg;
    color: $button-primary-text;
    border: none;
    border-radius: $border-radius-sm;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color $transition-default;

    &:hover {
      background-color: $button-primary-hover;
    }

    &:disabled {
      background-color: $black-light-70;
      cursor: not-allowed;
    }
  }

  .location-btn .spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid $white;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .now-btn {
    font-size: $font-size-sm;
    width: auto;
    padding: 0 $spacing-sm;
  }
}

.options-group {
  display: flex;
  gap: $spacing-md;
  max-width:180px;
  .input-group {
    flex: 1;

    input {
      max-width: 100px;
    }
  }
}

.book-button {
  width: 100%;
  max-width: 300px;
  padding: 12px;
  background-color: $button-primary-bg;
  color: $button-primary-text;
  border: none;
  border-radius: $border-radius-lg;
  font-size: $font-size-md;
  font-weight: $font-weight-bold;
  cursor: pointer;
  transition: background-color $transition-default;

  &:hover {
    background-color: $button-primary-hover;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Mobile and Tablet Responsiveness
@media (max-width: 768px) {
  .ride-form {
    max-width: 100%;
    padding: $spacing-sm;
  }

  .input-group {
    input,
    .datetime-input {
      max-width: 100%;
    }
  }

  .location-input-wrapper,
  .datetime-wrapper {
    flex-direction: row;
    gap: $spacing-xs;

    input,
    .datetime-input {
      flex: 1;
      max-width: calc(100% - 40px);
    }

    .location-btn,
    .now-btn {
      width: 36px;
      height: 36px;
      min-width: 36px;
    }

    .now-btn {
      padding: 0;
    }
  }

  .options-group {
    flex-direction: row;
    gap: $spacing-sm;

    .input-group {
      input {
        max-width: 100%;
      }
    }
  }

  .book-button {
    max-width: 100%;
  }
}

@media (max-width: 480px) {
  .location-input-wrapper,
  .datetime-wrapper {
    flex-direction: row;
    align-items: center;

    input,
    .datetime-input {
      flex: 1;
      max-width: calc(100% - 40px);
    }

    .location-btn,
    .now-btn {
      width: 36px;
      height: 36px;
      min-width: 36px;
      margin-top: 0;
      align-self: center;
    }
  }

  .options-group {
    flex-direction: row;
    gap: $spacing-xs;

    .input-group {
      flex: 1;
      input {
        max-width: 100%;
      }
    }
  }
}

// Add to frontend/src/styles/components/maps/InstantRideForm.scss

.fare-and-booking {
  margin-top: $spacing-md;
}

.fare-display {
  font-size: $font-size-lg;
  font-weight: $font-weight-bolder;
  margin-bottom: $spacing-md;
  color: $black;
}

.error {
  color: $primary-error-code-color;
  font-size: $font-size-sm;
  margin-top: $spacing-sm;
}

// Mobile adjustments
@media (max-width: 768px) {
  .fare-and-booking {
    margin-top: $spacing-sm;
  }

  .fare-display {
    font-size: $font-size-md;
  }

  .input-group input {
    max-width: 100%;
  }
}

@media (max-width: 480px) {
  .fare-and-booking {
    margin-top: $spacing-xs;
  }

  .fare-display {
    font-size: $font-size-sm;
  }
}
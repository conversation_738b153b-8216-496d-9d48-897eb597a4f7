@use "../../../variables" as *;

$fade-duration: 0.5s;
$slide-duration: 0.3s;

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.fade-in {
  animation: fadeIn $fade-duration ease-in;
}

.slide-in {
  animation: slideIn $slide-duration ease-out;
}

.table-row-hover {
  transition: background-color 0.2s ease;
  &:hover {
    background-color: $light-gray !important;
  }
}
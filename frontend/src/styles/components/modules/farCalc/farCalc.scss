@use "../../../variables" as *;

$primary-color: $primary-blue;
$secondary-color: #3498db;
$background-color: $light-gray;
$text-color: $black;
$error-color: $primary-error-code-color;
$success-color: $primary-success-code-color;
$border-radius: $border-radius-lg;
$box-shadow: $box-shadow-md;

body {
  background-color: $transparent-white;
}

.logo-link {
  display: block;
  text-align: center;
  margin-bottom: $spacing-lg;

  .logo {
    width: 150px;
    max-width: 100%;
    margin: 0 auto;
    display: block;
  }
}

.image-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba($black, 0.7);
  cursor: zoom-out;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.image-modal-content {
  position: relative;
  background: transparent;
  padding: 0;
  border: none;
}

.image-modal-img {
  max-width: 90vw;
  max-height: 90vh;
  object-fit: contain;
  cursor: auto;
}

.fare-calculator {
  max-width: 650px;
  margin: 0 auto;
  padding: $spacing-md;
  background-color: $background-color;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  text-align: center;
  font-family: $font-primary, sans-serif;

  h2 {
    color: $primary-color;
    font-size: $font-size-lg;
    margin-bottom: $spacing-md;
    font-weight: $font-weight-bold;
  }

  .input-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: $spacing-md;

    label {
      text-align: center;
      color: $text-color;
      font-size: $font-size-md;
      margin-bottom: $spacing-sm;
      font-weight: $font-weight-bold;
    }

    input {
      width: 250px;
      max-width: 100%;
      padding: $spacing-sm;
      border: 1px solid lighten($black, 40%);
      border-radius: $border-radius;
      font-size: $font-size-md;
      color: $text-color;
      background-color: $white;
      transition: border-color $transition-default, box-shadow $transition-default;
      text-align: center;

      &:focus {
        outline: none;
        border-color: $secondary-color;
        box-shadow: 0 0 5px rgba($secondary-color, 0.3);
      }

      &[type="number"] {
        appearance: textfield;
        -moz-appearance: textfield;
        &::-webkit-outer-spin-button,
        &::-webkit-inner-spin-button {
          -webkit-appearance: none;
          margin: 0;
        }
      }
    }
  }

  .button-group {
    display: flex;
    justify-content: center;
    margin-top: $spacing-md;
  }

  button {
    width: 250px;
    max-width: 100%;
    padding: $spacing-sm * 0.875;
    background-color: $secondary-color;
    color: $white;
    border: none;
    border-radius: $border-radius;
    font-size: $font-size-md;
    font-weight: $font-weight-bold;
    cursor: pointer;
    transition: background-color $transition-default, transform 0.2s ease;

    &:hover {
      background-color: darken($secondary-color, 10%);
      transform: translateY(-2px);
    }

    &:active {
      transform: translateY(0);
      background-color: darken($secondary-color, 15%);
    }

    &:disabled {
      background-color: lighten($secondary-color, 20%);
      cursor: not-allowed;
      transform: none;
    }
  }

  .result,
  .error {
    margin-top: $spacing-md;
    font-size: $font-size-md;
    animation: fadeIn 0.5s ease-in;
  }

  .result {
    color: $text-color;
    strong {
      color: $success-color;
      font-weight: $font-weight-bolder;
    }
  }

  .error {
    color: $error-color;
  }

  .formula-image {
    margin-top: $spacing-md;

    .formula-note {
      font-style: italic;
      margin-bottom: $spacing-sm;
    }

    .formula-thumbnail {
      max-width: 300px;
      border: 1px solid #ccc;
      cursor: pointer;
    }
  }

  .cheat-sheet-wrapper {
    margin-top: $spacing-md;
    overflow-x: auto;
    border-radius: $border-radius;
    box-shadow: $box-shadow;
    background-color: $white;
    padding: $spacing-sm;

    .cheat-sheet-table {
      width: 100%;
      border-collapse: collapse;
      font-size: 0.9rem;
      table-layout: auto;

      th {
        border-bottom: 2px solid #ccc;
        background: #eee;
        padding: 10px;
        text-align: center;
        position: sticky;
        top: 0;
        z-index: 1;
        cursor: pointer;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: lighten($secondary-color, 40%);
        }
      }

      td {
        border-bottom: 1px solid #ccc;
        padding: 8px;
        text-align: center;
        white-space: nowrap;
      }
    }
  }

  .section-spacer {
    display: block;
    margin: $spacing-xl 0;
  }

  .section-divider {
    margin: $spacing-xl 0;
    border: 0;
    height: 1px;
    background-color: lighten($black, 70%);
  }
}

@media (max-width: 480px) {
  .fare-calculator {
    padding: $spacing-sm;
    margin: $spacing-sm;
    max-width: 100%;

    h2 {
      font-size: $font-size-md;
    }

    .input-group {
      margin-bottom: $spacing-sm;

      label {
        font-size: $font-size-sm;
      }

      input {
        padding: $spacing-sm * 0.6;
        font-size: $font-size-sm;
      }
    }

    .button-group {
      margin-top: $spacing-sm;
    }

    button {
      padding: $spacing-sm;
      font-size: $font-size-sm;
    }
  }
}
@use "../../../variables" as *;

$mobile-breakpoint: 768px;
$tablet-breakpoint: 992px;
$mobile-breakpoint-plus: $mobile-breakpoint + 1px;

@media (max-width: $tablet-breakpoint) and (min-width: $mobile-breakpoint-plus) {
  .fare-calculator {
    max-width: 90%;
    margin: 20px auto;
    padding: $spacing-md;

    .calculator-container {
      padding: $spacing-md;
    }

    .input-section {
      gap: $spacing-sm;
    }

    .input-group {
      min-width: 180px;
    }

    .cheat-sheet-wrapper {
      margin-top: $spacing-sm;

      .cheat-sheet-table {
        font-size: 0.85rem;

        th,
        td {
          padding: $spacing-sm $spacing-xs;
          min-width: 60px;
        }
      }
    }
  }
}

@media (max-width: $mobile-breakpoint) {
  .fare-calculator {
    max-width: 100%;
    margin: 15px $spacing-sm;
    padding: $spacing-md;
    border-radius: $border-radius-md;

    h2 {
      font-size: $font-size-lg;
    }

    .calculator-container {
      padding: $spacing-md;
    }

    .input-section {
      flex-direction: column;
      gap: $spacing-sm;
      margin-bottom: $spacing-md;
    }

    .input-group {
      margin-bottom: $spacing-sm;

      label {
        font-size: $font-size-sm;
      }

      input {
        width: 100%;
        padding: $spacing-sm;
        font-size: $font-size-sm;
      }
    }

    button {
      padding: $spacing-sm;
      font-size: $font-size-sm;
    }

    .result,
    .error {
      padding: $spacing-sm;
      font-size: $font-size-sm;
    }

    .formula-image {
      padding: $spacing-sm;

      .formula-thumbnail {
        max-width: 250px;
      }
    }

    .cheat-sheet-wrapper {
      margin-top: $spacing-sm;
      padding: $spacing-sm;

      .cheat-sheet-table {
        font-size: 0.8rem;

        th,
        td {
          padding: $spacing-xs;
          min-width: 50px;
        }
      }
    }
  }

  .cheat-sheet-wrapper {
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    scrollbar-color: $accent-yellow $light-gray;

    &::-webkit-scrollbar {
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: $accent-yellow;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background: $light-gray;
    }
  }
}
# Fare Calculator Design

## Fare Calculator Designs
This directory contains the styling rules, both visual and layout, for the Fare Calculator functionality. It outlines the look of the fare calculator, including how it resizes with different screen sizes, and adds subtle animations meant to enhance the user experience.

## What's in This Folder?

### `farCalc.scss`
- It demonstrates the main look and design of the Fare Calculator.
- It includes choices of color—like primary colors and background colors—along with font sizes, spacing, and general layout.
- It defines the visual structure of input fields, buttons, error messages, and outcomes.
- Uses global file variables, like `variables.scss`, to provide consistent colors, font sizes, and spacing throughout your website.

### `animation.scss`
- Defines basic animations, such as fading in and sliding in elements.
- Augments the Fare Calculator, making it more interactive and visually engaging by animating the content as it fades in elegantly on the page.

### `responsiveness.scss`
- It reconfigures the layout to fit smaller screens, like smartphones and tablets.
- It ensures that the Fare Calculator is both readable and interactive on a broad range of devices.
- Tunes the spacing, arranges table layouts, and improves scrollbars so that the calculator is mobile-friendly.

## Key Features

### Consistency and Theme
They all use the same variables for their styling settings—colors, spacing, and fonts—providing a uniform design for the entire application.

### Ease of Maintenance
Separating styles into separate files, like `farCalc.scss`, `animation.scss`, and `responsiveness.scss`, makes it easier to keep and change appearances and behaviors without the hassle of dealing with a single, large stylesheet.

### Mobile-Friendly Approach
Due to the carefully designed rules in `responsiveness.scss`, the Fare Calculator seamlessly adjusts for smaller screens, ensuring a seamless user experience regardless of where it is being accessed.

### Animations for Clarity
The subtle animations defined in `animation.scss` highlight major changes, including computing a new fare, while maintaining a seamless user experience.

## When to Update

| Purpose | File to Edit |
|---------|-------------|
| **Layout or Design Changes** | To change the overall look of the Fare Calculator—whether modifying button aesthetics or changing background colors—you will typically need to edit the `farCalc.scss` file. |
| **Animations** | To change the way items fade or slide into the screen, you will need to go into `animation.scss`. |
| **Diverse Apparatuses** | If there are any new design demands for mobile or tablet views or changes in how the fare calculator works on tiny screens, you will alter modifications in `responsiveness.scss`. |

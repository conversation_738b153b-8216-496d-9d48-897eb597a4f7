// FindMyRide.scss
.find-my-ride {
  width: 100%;
  max-width: 600px;
  margin: 2rem auto;
  padding: 1rem;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

  .find-ride-container {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  .find-ride-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    text-align: center;

    h2 {
      margin: 0 0 0.5rem 0;
      font-size: 1.8rem;
      font-weight: 600;
    }

    p {
      margin: 0;
      opacity: 0.9;
      font-size: 1rem;
    }
  }

  .ride-search-form {
    padding: 2rem;
    background: #f8f9fa;

    .form-group {
      margin-bottom: 1.5rem;

      label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 600;
        color: #333;
        font-size: 0.9rem;
      }

      input {
        width: 100%;
        padding: 0.75rem;
        border: 2px solid #e1e5e9;
        border-radius: 8px;
        font-size: 1rem;
        transition: border-color 0.3s ease;
        box-sizing: border-box;

        &:focus {
          outline: none;
          border-color: #667eea;
          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        &::placeholder {
          color: #9ca3af;
        }
      }
    }

    .search-btn {
      width: 100%;
      padding: 0.875rem 1.5rem;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      border-radius: 8px;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;

      &:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
      }

      &:disabled {
        opacity: 0.7;
        cursor: not-allowed;
        transform: none;
      }

      .spinner {
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
      }
    }
  }

  .error-message {
    padding: 1rem 2rem;
    background: #fee2e2;
    border-left: 4px solid #ef4444;
    margin: 0;

    p {
      margin: 0;
      color: #dc2626;
      font-weight: 500;
    }
  }

  .no-results {
    padding: 2rem;
    text-align: center;
    color: #6b7280;
    font-style: italic;
  }

  .ride-details-card {
    margin: 0;
    background: white;

    .ride-header {
      padding: 1.5rem 2rem;
      border-bottom: 1px solid #e5e7eb;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      flex-wrap: wrap;
      gap: 1rem;

      .ride-id {
        h3 {
          margin: 0 0 0.5rem 0;
          color: #1f2937;
          font-size: 1.4rem;
        }
      }

      .booking-info {
        .booking-date {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          color: #6b7280;
          font-size: 0.9rem;
        }
      }
    }

    .ride-details-content {
      padding: 2rem;
    }

    .ride-route {
      margin-bottom: 2rem;
      position: relative;

      .route-point {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        margin-bottom: 1rem;

        .icon {
          width: 20px;
          height: 20px;
          margin-top: 0.2rem;
          
          &.pickup-icon {
            color: #10b981;
          }
          
          &.dropoff-icon {
            color: #ef4444;
          }
        }

        .location-info {
          flex: 1;

          .label {
            display: block;
            font-size: 0.8rem;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 0.25rem;
          }

          .address {
            display: block;
            color: #1f2937;
            font-weight: 500;
            line-height: 1.4;
          }
        }
      }

      .route-line {
        position: absolute;
        left: 9px;
        top: 30px;
        width: 2px;
        height: 20px;
        background: linear-gradient(to bottom, #10b981, #ef4444);
        border-radius: 1px;
      }
    }

    .ride-info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 1rem;
      margin-bottom: 2rem;

      .info-item {
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 8px;
        display: flex;
        align-items: center;
        gap: 0.75rem;

        .icon {
          color: #667eea;
          font-size: 1.1rem;
        }

        .info-content {
          display: flex;
          flex-direction: column;

          .label {
            font-size: 0.8rem;
            color: #6b7280;
            font-weight: 500;
            margin-bottom: 0.25rem;
          }

          .value {
            font-weight: 600;
            color: #1f2937;
          }
        }

        .label {
          font-size: 0.8rem;
          color: #6b7280;
          font-weight: 500;
        }

        .value {
          font-weight: 600;
          color: #1f2937;
          
          &.fare-amount {
            color: #10b981;
            font-size: 1.1rem;
          }
        }
      }
    }

    .driver-info {
      margin-bottom: 1.5rem;
      padding: 1.5rem;
      background: #f0f9ff;
      border-radius: 8px;
      border: 1px solid #e0f2fe;

      h4 {
        margin: 0 0 1rem 0;
        color: #0369a1;
        font-size: 1.1rem;
      }

      .driver-details {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
      }

      .driver-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;

        .icon {
          color: #0369a1;
          font-size: 1rem;
        }

        .driver-name {
          font-weight: 600;
          color: #1f2937;
        }

        .driver-phone {
          color: #0369a1;
          text-decoration: none;
          font-weight: 500;
          
          &:hover {
            text-decoration: underline;
          }
        }

        .driver-vehicle {
          color: #374151;
          font-weight: 500;
        }
      }
    }

    .status-message {
      padding: 1rem;
      background: #f0f9ff;
      border-radius: 8px;
      border-left: 4px solid #0369a1;

      p {
        margin: 0;
        color: #0369a1;
        font-weight: 500;
        line-height: 1.5;
      }
    }
  }

  .status-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;

    &.pending {
      background: #fef3c7;
      color: #d97706;
    }

    &.assigned {
      background: #dbeafe;
      color: #1d4ed8;
    }

    &.active {
      background: #d1fae5;
      color: #065f46;
    }

    &.completed {
      background: #d1fae5;
      color: #065f46;
    }

    &.cancelled {
      background: #fee2e2;
      color: #dc2626;
    }

    &.rejected {
      background: #fee2e2;
      color: #dc2626;
    }

    &.unknown {
      background: #f3f4f6;
      color: #374151;
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    margin: 1rem;
    padding: 0;

    .find-ride-header {
      padding: 1.5rem 1rem;

      h2 {
        font-size: 1.5rem;
      }
    }

    .ride-search-form {
      padding: 1.5rem 1rem;
    }

    .ride-details-card {
      .ride-header {
        padding: 1rem;
        flex-direction: column;
        align-items: flex-start;
      }

      .ride-details-content {
        padding: 1rem;
      }

      .ride-info-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
      }

      .driver-info {
        padding: 1rem;
      }
    }
  }

  @media (max-width: 480px) {
    .ride-info-grid {
      .info-item {
        padding: 0.75rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;

        .info-content {
          width: 100%;
        }
      }
    }
  }
}
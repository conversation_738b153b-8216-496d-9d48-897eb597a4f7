@use "../../variables.scss" as *;

.user-dropdown {
  position: relative;
  cursor: pointer;

  .username {
    display: flex;
    align-items: center;
    gap: 5px;
  }

  .dropdown-menu {
    position: absolute;
    right: 0;
    top: 100%;
    background-color: $white;
    border: 1px solid $gray-border;
    border-radius: 4px;
    box-shadow: 0 2px 5px $shadow-dark;
    min-width: 200px;
    padding: 5px 0;
    z-index: 1000;

    &.align-left {
      right: auto;
      left: 0;
    }

    li {
      padding: 8px 15px;
      list-style: none;

      &:hover {
        background-color: $white;
      }

      a,
      span {
        display: block;
        text-decoration: none;
        color: $black;
      }

      .logout-btn {
        background: none;
        border: none;
        padding: 0;
        width: 100%;
        text-align: left;
        cursor: pointer;
        color: $black;
      }
    }

    .profile-item {
      display: flex;
      align-items: center;
      padding: 10px 15px;
    }

    .profile-picture {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      margin-right: 15px;
    }

    .profile-info {
      text-align: left;
      flex-grow: 1;
      display: flex;
      flex-direction: column;
    }

    .role {
      font-weight: bold;
      color: $primary-color-role;
      text-transform: capitalize;
      font-size: 14px;
    }

    .vip-level-container {
      margin-top: 5px;
    }

    .vip-level {
      font-size: 14px;
      color: $black;
    }
  }

  svg {
    transition: transform 0.49s ease;
  }

  &.open svg {
    transform: rotate(180deg);
  }
}

@media (max-width: 768px) {
  .user-dropdown {
    .dropdown-menu {
      position: static;
      width: 100%;
      border: none;
      box-shadow: none;
    }
  }
}

.logout-span {
  font-family: $font-primary;
  font-size: $font-size-md;
}
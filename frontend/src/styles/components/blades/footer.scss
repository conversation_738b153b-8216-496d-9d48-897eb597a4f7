// frontend/src/styles/components/blades/footer.scss
@use "../../variables.scss" as *;
//~ Include Font Awesome for social icons (add this to your project if not already present)
@import url("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css");


.footer {
  background: $primary-blue;
  color: $white;
  padding: $spacing-xl 0;
  font-family: $font-secondary;
  position: relative;

  .footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 $spacing-md;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: $spacing-lg;

    .footer-section {
      flex: 1;
      min-width: 200px;
      text-align: left;

      h3 {
        font-size: $font-size-lg;
        font-weight: $font-weight-bolder;
        margin-bottom: $spacing-md;
        color: $accent-yellow;
      }

      p, a {
        font-size: $font-size-md;
        margin-bottom: $spacing-sm;
        color: $white;
        text-decoration: none;
        transition: color $transition-default;
      }

      a:hover {
        color: $accent-yellow;
        text-decoration: underline;
      }

      .social-links {
        display: flex;
        gap: $spacing-md;
        flex-wrap: wrap;

        a {
          font-size: $font-size-lg;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          background: $button-accent-bg;
          color: $button-accent-text;
          border-radius: $border-radius-md;
          transition: background $transition-default, transform $transition-default;

          &:hover {
            background: $button-accent-hover;
            transform: scale(1.1);
            color: $button-accent-text;
            text-decoration: none;
          }

          i {
            font-size: $font-size-lg; // Ensure icon size matches container
          }
        }
      }
    }
  }

  .footer-bottom {
    text-align: center;
    padding: $spacing-sm 0; // Reduced from $spacing-md (20px) to $spacing-sm (10px)
    border-top: 1px solid $transparent-white;
    font-size: 12px; // Fixed smaller size (was $font-size-sm = 14px)
    color: $light-gray;
    line-height: 1.2; // Tighter line height for compactness
  }
}

/* Tablet (768px and below) */
@media (max-width: 768px) {
  .footer {
    .footer-container {
      flex-direction: column;
      padding: 0 $spacing-md;
      gap: $spacing-md;

      .footer-section {
        text-align: center;
        min-width: unset;

        .social-links {
          justify-content: center;
        }
      }
    }
  }
}

/* Mobile (480px and below) */
@media (max-width: 480px) {
  .footer {
    padding: $spacing-lg 0;

    .footer-container {
      padding: 0 $spacing-sm;
      gap: $spacing-sm;

      .footer-section {
        h3 {
          font-size: $font-size-md;
        }

        p, a {
          font-size: $font-size-sm;
        }

        .social-links a {
          width: 35px;
          height: 35px;
          font-size: $font-size-md;

          i {
            font-size: $font-size-md; // Match smaller container
          }
        }
      }
    }

    .footer-bottom {
      padding: $spacing-sm 0;
      font-size: 10px; // Even smaller for mobile
    }
  }
}
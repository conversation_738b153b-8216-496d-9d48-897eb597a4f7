@use "../../variables.scss" as *;

/* Reset for Debugging */
* {
  box-sizing: border-box;
}

/* Navbar */
.navbar {
  background-color: $primary-blue;
  padding: $spacing-md $spacing-lg;
  color: $white;
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 1000;
  box-shadow: $box-shadow-sm;
}

.navbar-container {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  flex-wrap: nowrap; /* Default for desktop */
}

/* Logo */
.logo img {
  height: $spacing-xl;
  width: auto;
}

/* Nav Links */
.nav-links {
  display: flex;
  justify-content: space-between;
  flex-grow: 1;
  margin-left: $spacing-md;
  max-width: 1200px;
}

.nav-left,
.nav-right {
  display: flex;
  align-items: center;
}

.main-links,
.utility-links {
  list-style: none;
  display: flex;
  margin: 0;
  padding: 0;
  gap: $spacing-md + 5px;
  align-items: center;
}

.nav-links a {
  color: $white;
  text-decoration: none;
  font-size: $font-size-md;
  font-weight: $font-weight-bold;
  transition: $transition-default;
}

.nav-links a:hover {
  color: $accent-yellow;
}

/* Dropdown */
.dropdown {
  position: relative;
}

.dropdown svg {
  transition: transform 0.49s ease; /* Apply transition in base state */
}

.dropdown.open svg {
  transform: rotate(180deg); /* Rotate when open */
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background-color: $primary-blue;
  min-width: 200px;
  border-radius: $border-radius-sm;
  box-shadow: $box-shadow-md;
  padding: $spacing-sm 0;
  list-style: none;
  margin: 0;
}

.dropdown-menu li {
  padding: 8px $spacing-md;
}

.dropdown-menu a {
  color: $white;
  text-decoration: none;
  font-size: $font-size-md;
  font-weight: $font-weight-bold;
  display: block;
  transition: $transition-default;
}

.dropdown-menu a:hover {
  color: $accent-yellow;
}

/* Language Switch */
.lang-switch {
  background: none;
  border: 1px solid $transparent-white;
  color: $white;
  padding: 5px $spacing-sm;
  border-radius: $border-radius-sm;
  cursor: pointer;
  font-size: $font-size-sm;
  transition: $transition-default;
}

.lang-switch:hover {
  border-color: $accent-yellow;
  color: $accent-yellow;
}

/* Hamburger */
.hamburger {
  display: none; /* Hidden by default (desktop) */
  background: none;
  border: none;
  color: $accent-yellow; /* Match design */
  font-size: 30px;
  cursor: pointer;
  padding: $spacing-sm;
  z-index: 1200;
  line-height: 1;
}

/* Mobile Auth Links */
.mobile-auth-links {
  display: none; /* Hidden by default (desktop) */
}

/* Mobile Styles */
@media only screen and (max-width: 768px) {
  .navbar-container {
    flex-wrap: wrap; /* Allow wrapping */
    justify-content: space-between;
    padding: $spacing-md;
  }

  .hamburger {
    display: block; /* Show on mobile */
    background-color: $primary-blue; /* Match navbar */
    color: $accent-yellow; /* Consistent with design */
    order: 3;
  }

  .mobile-auth-links {
    display: flex;
    gap: $spacing-md;
    align-items: center;
    order: 2;
  }

  .mobile-auth-links a {
    color: $white; /* Match nav-links */
    text-decoration: none; /* No underline */
    font-size: $font-size-md;
    font-weight: $font-weight-bold;
    transition: $transition-default;
  }

  .mobile-auth-links a:hover {
    color: $accent-yellow; /* Match hover effect */
  }

  .logo {
    order: 1;
  }

  .nav-links {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background-color: $primary-blue;
    flex-direction: column;
    padding: $spacing-md;
    z-index: 1000;
  }

  .nav-links.active {
    display: flex;
  }

  .nav-left,
  .nav-right {
    width: 100%;
    justify-content: center;
  }

  .main-links,
  .utility-links {
    flex-direction: column;
    width: 100%;
    gap: $spacing-md; /* Space between items within each list */
    text-align: center;
  }

  /* Space Between Main Links and Utility Links */
  .nav-left {
    margin-bottom: $spacing-md; /* Space between main-links and utility-links */
  }

  .dropdown-menu {
    position: static;
    box-shadow: none;
    background-color: $primary-blue-darkest;
    width: 100%;
    padding: $spacing-sm 0;
  }
}

/* Desktop Styles */
@media only screen and (min-width: 769px) {
  .mobile-auth-links {
    display: none;
  }

  .hamburger {
    display: none;
  }
}
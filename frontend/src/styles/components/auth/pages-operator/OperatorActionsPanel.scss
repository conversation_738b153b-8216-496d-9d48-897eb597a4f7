// OperatorActionsPanel.scss
@use "../../../variables" as *;

.operator-actions-panel {
  position: fixed;
  top: 0;
  right: 0;
  width: 400px;
  height: 100vh;
  background: $white;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  animation: slideInRight 0.3s ease-out;

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: $spacing-lg $spacing-xl;
    border-bottom: 1px solid $gray-border;
    background: linear-gradient(135deg, $primary-blue 0%, $primary-blue-dark 100%);
    color: $white;

    .panel-title {
      display: flex;
      align-items: center;
      gap: $spacing-md;

      h3 {
        margin: 0;
        font-size: 1.3rem;
        font-weight: $font-weight-bold;
      }

      .status-indicator {
        padding: $spacing-xs $spacing-sm;
        border-radius: $border-radius-sm;
        font-size: 0.8rem;
        font-weight: $font-weight-bold;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        color: $white;
      }
    }

    .panel-actions {
      display: flex;
      gap: $spacing-xs;

      .btn {
        background: rgba($white, 0.1);
        color: $white;
        border: 1px solid rgba($white, 0.2);
        padding: $spacing-sm;
        border-radius: $border-radius-sm;

        &:hover {
          background: rgba($white, 0.2);
        }
      }
    }
  }

  .panel-tabs {
    display: flex;
    border-bottom: 1px solid $gray-border;
    background: $light-gray;

    .tab {
      flex: 1;
      padding: $spacing-md $spacing-sm;
      border: none;
      background: transparent;
      color: $gray-dark;
      font-weight: $font-weight-medium;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: $spacing-xs;
      font-size: 0.9rem;

      &:hover {
        background: rgba($primary-blue, 0.05);
        color: $primary-blue;
      }

      &.active {
        background: $white;
        color: $primary-blue;
        border-bottom: 2px solid $primary-blue;
        font-weight: $font-weight-bold;
      }
    }
  }

  .panel-content {
    flex: 1;
    overflow-y: auto;
    padding: $spacing-lg;

    .info-section {
      margin-bottom: $spacing-xl;

      h4 {
        margin: 0 0 $spacing-md 0;
        color: $primary-blue;
        font-size: 1rem;
        font-weight: $font-weight-bold;
        border-bottom: 1px solid $gray-border;
        padding-bottom: $spacing-xs;
      }

      .info-item {
        display: flex;
        align-items: flex-start;
        gap: $spacing-md;
        margin-bottom: $spacing-md;
        padding: $spacing-sm;
        border-radius: $border-radius-sm;
        transition: background-color 0.2s ease;

        &:hover {
          background: rgba($primary-blue, 0.02);
        }

        .icon {
          width: 20px;
          height: 20px;
          flex-shrink: 0;
          margin-top: 2px;

          &.pickup {
            color: $primary-success-code-color;
          }

          &.dropoff {
            color: $primary-blue;
          }
        }

        .info-text {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: $spacing-xs;

          .label {
            font-size: 0.8rem;
            color: $gray-medium;
            font-weight: $font-weight-medium;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }

          .value {
            font-size: 0.95rem;
            color: $black;
            font-weight: $font-weight-medium;
            line-height: 1.4;
          }
        }
      }
    }

    .maps-tab {
      .map-actions {
        display: flex;
        flex-direction: column;
        gap: $spacing-md;
        margin-bottom: $spacing-xl;

        .map-button {
          display: flex;
          align-items: center;
          gap: $spacing-md;
          padding: $spacing-lg;
          border: 2px solid $gray-border;
          border-radius: $border-radius-md;
          text-decoration: none;
          color: $black;
          transition: all 0.2s ease;
          background: $white;

          &:hover {
            border-color: $primary-blue;
            box-shadow: 0 4px 12px rgba($primary-blue, 0.1);
            transform: translateY(-1px);
            text-decoration: none;
            color: $primary-blue;
          }

          &.pickup {
            &:hover {
              border-color: $primary-success-code-color;
              color: $primary-success-code-color;
            }
          }

          &.dropoff {
            &:hover {
              border-color: $primary-blue;
              color: $primary-blue;
            }
          }

          &.directions {
            &:hover {
              border-color: #f59e0b;
              color: #f59e0b;
            }
          }

          svg {
            width: 24px;
            height: 24px;
            flex-shrink: 0;
          }

          .button-text {
            display: flex;
            flex-direction: column;
            gap: $spacing-xs;

            .title {
              font-weight: $font-weight-bold;
              font-size: 1rem;
            }

            .subtitle {
              font-size: 0.8rem;
              color: $gray-medium;
            }
          }
        }
      }

      .map-info {
        background: rgba($primary-blue, 0.05);
        padding: $spacing-lg;
        border-radius: $border-radius-md;
        border-left: 4px solid $primary-blue;

        p {
          margin: 0 0 $spacing-sm 0;
          font-size: 0.9rem;
          color: $gray-dark;
        }

        ul {
          margin: 0;
          padding-left: $spacing-lg;

          li {
            font-size: 0.85rem;
            color: $gray-medium;
            margin-bottom: $spacing-xs;
          }
        }
      }
    }

    .contact-tab {
      .contact-section {
        margin-bottom: $spacing-xl;

        h4 {
          margin: 0 0 $spacing-md 0;
          color: $primary-blue;
          font-size: 1rem;
          font-weight: $font-weight-bold;
        }

        .contact-card {
          display: flex;
          align-items: center;
          gap: $spacing-md;
          padding: $spacing-lg;
          border: 1px solid $gray-border;
          border-radius: $border-radius-md;
          background: $white;

          .contact-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, $primary-blue 0%, $primary-blue-dark 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: $white;
            font-size: 1.2rem;
            flex-shrink: 0;
          }

          .contact-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: $spacing-xs;

            .name {
              font-weight: $font-weight-bold;
              font-size: 1rem;
              color: $black;
            }

            .phone {
              display: flex;
              align-items: center;
              gap: $spacing-xs;
              color: $primary-blue;
              text-decoration: none;
              font-weight: $font-weight-medium;

              &:hover {
                text-decoration: underline;
              }
            }

            .vehicle {
              font-size: 0.9rem;
              color: $gray-medium;
            }
          }
        }
      }

      .quick-actions {
        h4 {
          margin: 0 0 $spacing-md 0;
          color: $primary-blue;
          font-size: 1rem;
          font-weight: $font-weight-bold;
        }

        .action-button {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: $spacing-sm;
          width: 100%;
          padding: $spacing-md;
          margin-bottom: $spacing-sm;
          border: 2px solid $primary-blue;
          border-radius: $border-radius-md;
          background: $white;
          color: $primary-blue;
          font-weight: $font-weight-bold;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background: $primary-blue;
            color: $white;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba($primary-blue, 0.3);
          }
        }
      }
    }
  }
}

// Animation
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

// Responsive design
@media (max-width: 768px) {
  .operator-actions-panel {
    width: 100%;
    right: 0;
  }
}

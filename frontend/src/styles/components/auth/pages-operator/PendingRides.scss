@use "../../../variables" as *;

.pending-rides {
  padding: $spacing-lg;
  max-width: 1200px;
  margin: 0 auto;

  .loading-container,
  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 300px;
    text-align: center;

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid rgba($primary-blue, 0.1);
      border-radius: 50%;
      border-top-color: $primary-blue;
      animation: spin 1s ease-in-out infinite;
      margin-bottom: $spacing-md;
    }

    .error-message {
      color: $primary-error-code-color;
      margin-bottom: $spacing-md;
    }
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-xl;
    padding-bottom: $spacing-md;
    border-bottom: 1px solid $border-color;

    h1 {
      margin: 0;
      color: $primary-blue;
      font-size: 1.8rem;
      font-weight: bold;
    }

    .page-actions {
      .btn {
        display: inline-flex;
        align-items: center;
        gap: $spacing-xs;
        padding: $spacing-sm $spacing-md;
        border-radius: $border-radius-sm;
        font-weight: $font-weight-bold;
        cursor: pointer;
        transition: all 0.2s ease;
        text-align: center;
        font-size: 0.9rem;
        border: 1px solid transparent;


        &:disabled {
          opacity: 0.7;
          cursor: not-allowed;
        }

        .spinning {
          animation: spin 1s linear infinite;
        }

        &.btn-primary {
          background-color: $primary-blue;
          color: $white;
          border-color: $primary-blue;

          &:hover:not(:disabled) {
            background-color: $primary-blue-dark;
            border-color: $primary-blue-dark;
          }
        }

        &.btn-secondary {
          background-color: $white;
          color: $primary-blue;
          border-color: $primary-blue;

          &:hover:not(:disabled) {
            background-color: $light-gray;
          }
        }

        &.btn-outline {
          background-color: transparent;
          color: $black-light-40;
          border-color: $gray-border;

          &:hover:not(:disabled) {
            background-color: $light-gray;
            color: $black;
          }
        }
      }
    }
  }


  .no-rides {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 300px;
    text-align: center;
    color: $primary-error-code-color;
    padding: $spacing-xl;
    background: $white;
    border-radius: $border-radius-lg;
    box-shadow: $box-shadow-sm;

    .no-rides-icon {
      font-size: 3rem;
      color: $gray-light;
      margin-bottom: $spacing-md;
    }

    p {
      font-size: 1.1rem;
      margin: 0;
    }
  }

  .ride-requests {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: $spacing-lg;
    margin-top: $spacing-lg;
  }

  .ride-card {
    background: $white;
    border-radius: $border-radius-lg;
    overflow: hidden;
    box-shadow: $box-shadow-sm;
    transition: transform 0.2s ease, box-shadow 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: $box-shadow-md;
    }

    .ride-info {
      padding: $spacing-lg;
    }

    .info-row {
      display: flex;
      align-items: flex-start;
      margin-bottom: $spacing-md;

      &:last-child {
        margin-bottom: 0;
      }

      .icon {
        color: $primary-blue;
        margin-right: $spacing-sm;
        margin-top: 2px;
        flex-shrink: 0;

        &.pickup {
          color: $primary-success-code-color;
        }

        &.dropoff {
          color: $primary-error-code-color;
        }
      }

      .location, .time, .rider-info {
        flex: 1;
      }

      .label {
        font-size: 0.75rem;
        color: $black-light-40;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        margin-bottom: 2px;
      }

      .value {
        font-weight: 500;
        color: $black;
        word-break: break-word;
      }
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: $spacing-sm;
      margin: $spacing-md 0;
      padding: $spacing-sm 0;
      border-top: 1px solid $border-color;
      border-bottom: 1px solid $border-color;

      .info-item {
        display: flex;
        align-items: center;
        font-size: 0.9rem;
        color: $black-light-40;

        .icon {
          margin-right: $spacing-xs;
          color: $primary-blue;
          font-size: 0.9em;
        }
      }
    }

    .ride-actions {
      display: flex;
      gap: $spacing-sm;
      padding: $spacing-md $spacing-lg;
      background: $white;
      border-top: 1px solid $border-color;

      .btn {
        display: inline-flex;
        align-items: center;
        gap: $spacing-xs;
        padding: $spacing-sm $spacing-md;
        border-radius: $border-radius-sm;
        font-weight: $font-weight-bold;
        cursor: pointer;
        transition: all 0.2s ease;
        text-align: center;
        font-size: 0.9rem;
        border: 1px solid transparent;

        &:disabled {
          opacity: 0.7;
          cursor: not-allowed;
        }

        .spinning {
          animation: spin 1s linear infinite;
        }

        &.btn-primary {
          background-color: $primary-blue;
          color: $white;
          border-color: $primary-blue;

          &:hover:not(:disabled) {
            background-color: $primary-blue-dark;
            border-color: $primary-blue-dark;
          }
        }

        &.btn-secondary {
          background-color: $white;
          color: $primary-blue;
          border-color: $primary-blue;

          &:hover:not(:disabled) {
            background-color: $light-gray;
          }
        }

        &.btn-outline {
          background-color: transparent;
          color: $black-light-40;
          border-color: $gray-border;

          &:hover:not(:disabled) {
            background-color: $light-gray;
            color: $black;
          }
        }
      }
    }
  }





  @keyframes spin {
    to { transform: rotate(360deg); }
  }

  @media (max-width: 768px) {
    padding: $spacing-md;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: $spacing-md;

      .page-actions {
        width: 100%;
        .btn {
          width: 100%;
          justify-content: center;
        }
      }
    }

    .ride-requests {
      grid-template-columns: 1fr;
    }

    .ride-card {
      .ride-actions {
        flex-direction: column;

        .btn {
          width: 100%;
        }
      }
    }
  }

  @media (max-width: 480px) {
    padding: $spacing-sm;

    .ride-card {
      .info-grid {
        grid-template-columns: 1fr;
      }
    }
  }
}

  .ride-requests {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: $spacing-md;
  }

  .ride-card {
    background: $white;
    border-radius: $border-radius-md;
    box-shadow: $box-shadow-sm;
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: $box-shadow-md;
    }

    .ride-info {
      padding: $spacing-md;
      border-bottom: 1px solid $gray-border;
    }

    .info-row {
      display: flex;
      align-items: flex-start;
      margin-bottom: $spacing-sm;

      &:last-child {
        margin-bottom: 0;
      }

      .icon {
        margin-right: $spacing-sm;
        margin-top: 4px;
        color: $primary-blue;
        min-width: 16px;

        &.pickup {
          color: $primary-success-code-color;
        }

        &.dropoff {
          color: $primary-error-code-color;
        }
      }

      .location, .detail, .time {
        flex: 1;
      }

      .label {
        font-size: 0.75rem;
        color: $black-light-40;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 2px;
      }

      .value {
        font-weight: $font-weight-bold;
        color: $black;
      }

      .detail {
        margin-right: $spacing-lg;
      }
    }

    .ride-actions {
      display: flex;
      padding: $spacing-md;
      gap: $spacing-sm;

      .btn {
        display: inline-flex;
        align-items: center;
        gap: $spacing-xs;
        padding: $spacing-sm $spacing-md;
        border-radius: $border-radius-sm;
        font-weight: $font-weight-bold;
        cursor: pointer;
        transition: all 0.2s ease;
        text-align: center;
        font-size: 0.9rem;
        border: 1px solid transparent;

        &:disabled {
          opacity: 0.7;
          cursor: not-allowed;
        }

        .spinning {
          animation: spin 1s linear infinite;
        }

        &.btn-primary {
          background-color: $primary-blue;
          color: $white;
          border-color: $primary-blue;

          &:hover:not(:disabled) {
            background-color: $primary-blue-dark;
            border-color: $primary-blue-dark;
          }
        }

        &.btn-secondary {
          background-color: $white;
          color: $primary-blue;
          border-color: $primary-blue;

          &:hover:not(:disabled) {
            background-color: $light-gray;
          }
        }

        &.btn-outline {
          background-color: transparent;
          color: $black-light-40;
          border-color: $gray-border;

          &:hover:not(:disabled) {
            background-color: $light-gray;
            color: $black;
          }
        }
      }
    }
  }




// Responsive adjustments
@media (max-width: 768px) {
  .pending-rides {
    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: $spacing-md;
    }

    .ride-requests {
      grid-template-columns: 1fr;
    }
  }
}


.operator-list-view {
  padding: $spacing-lg;
  margin: 0 auto;
  width: 100%;

  h2 {
    color: $primary-blue;
    margin-bottom: $spacing-lg;
    font-size: 1.5rem;
    font-weight: $font-weight-bold;
  }

  .rides-list {
    background: $white;
    border-radius: $border-radius-md;
    box-shadow: $box-shadow-sm;
    overflow-x: auto;
    width: 100%;
  }

  .rides-table {
    width: 100%;
    min-width: 800px;
    border-collapse: collapse;

    th, td {
      padding: $spacing-md;
      text-align: left;
      border-bottom: 1px solid $gray-light;
    }

    th {
      background-color: $light-gray;
      color: $gray-dark;
      font-weight: $font-weight-bold;
      text-transform: uppercase;
      font-size: 0.75rem;
      letter-spacing: 0.5px;
      white-space: nowrap;
    }

    tr:hover {
      background-color: rgba($primary-blue, 0.02);
    }

    .btn {
      display: inline-flex;
      align-items: center;
      gap: $spacing-xs;
      padding: $spacing-sm $spacing-md;
      border-radius: $border-radius-sm;
      font-weight: $font-weight-bold;
      cursor: pointer;
      transition: all 0.2s ease;
      text-align: center;
      font-size: 0.9rem;
      border: 1px solid transparent;

      &:disabled {
        opacity: 0.7;
        cursor: not-allowed;
      }

      .spinning {
        animation: spin 1s linear infinite;
      }

      &.btn-primary {
        background-color: $primary-blue;
        color: $white;
        border-color: $primary-blue;

        &:hover:not(:disabled) {
          background-color: $primary-blue-dark;
          border-color: $primary-blue-dark;
        }
      }

      &.btn-secondary {
        background-color: $white;
        color: $primary-blue;
        border-color: $primary-blue;

        &:hover:not(:disabled) {
          background-color: $light-gray;
        }
      }

      &.btn-outline {
        background-color: transparent;
        color: $black-light-40;
        border-color: $gray-border;

        &:hover:not(:disabled) {
          background-color: $light-gray;
          color: $black;
        }
      }
    }
  }

  .no-rides {
    padding: $spacing-xxl $spacing-md;
    text-align: center;
    color: $gray-medium;
    
    p {
      margin: 0;
      font-size: 1.1rem;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .operator-list-view {
    padding: $spacing-md;
    
    .rides-table {
      display: block;
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;
      
      th, td {
        white-space: nowrap;
      }
    }
  }
}

.operator-sidebar {
  display: block !important;
  transform: translateX(0) !important;
  visibility: visible !important;
  z-index: 100 !important;

  .sidebar-header {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    
    h2 {
      color: $white !important;
      margin: 0;
      font-size: 1.2rem;
      font-weight: $font-weight-bold;
    }
  }
}

.custom-refresh-btn {
  background-color: $primary-blue;
  color: $white;
  border-color: $primary-blue;
  padding: $spacing-sm $spacing-md;
  border-radius: $border-radius-md;
  font-weight: $font-weight-bold;
  cursor: pointer;
  transition: background-color 0.3s ease, border-color 0.3s ease;

  &:hover:not(:disabled) {
    background-color: $primary-blue-dark;
    border-color: $primary-blue-dark;
  }
}

.ride-requests {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: $spacing-md;
}

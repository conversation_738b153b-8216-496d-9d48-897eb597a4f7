// AssignDriverModal.scss
@use "../../../variables" as *;

.assign-driver-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease-out;

  .modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(2px);
  }

  .modal-content {
    position: relative;
    background: $white;
    border-radius: $border-radius-lg;
    width: 90%;
    max-width: 700px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: slideUp 0.3s ease-out;
    display: flex;
    flex-direction: column;

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding: $spacing-xl $spacing-xl $spacing-lg;
      border-bottom: 1px solid $gray-border;
      background: linear-gradient(135deg, $primary-blue 0%, $primary-blue-dark 100%);
      color: $white;

      .modal-title {
        flex: 1;

        h3 {
          margin: 0 0 $spacing-xs 0;
          font-size: 1.5rem;
          font-weight: $font-weight-bold;
          color: $white;
        }

        .modal-subtitle {
          margin: 0;
          font-size: 0.9rem;
          opacity: 0.9;
          color: rgba($white, 0.8);
        }
      }

      .modal-header-actions {
        display: flex;
        gap: $spacing-sm;
        align-items: center;

        .btn {
          &.btn-secondary {
            background: rgba($white, 0.1);
            color: $white;
            border: 1px solid rgba($white, 0.2);

            &:hover {
              background: rgba($white, 0.2);
            }
          }

          &.btn-ghost {
            background: transparent;
            color: $white;
            border: none;
            padding: $spacing-sm;
            border-radius: $border-radius-sm;

            &:hover {
              background: rgba($white, 0.1);
            }
          }
        }
      }
    }

    .modal-body {
      flex: 1;
      overflow-y: auto;
      padding: $spacing-lg $spacing-xl;

      .drivers-section {
        .section-header {
          margin-bottom: $spacing-lg;
          display: flex;
          flex-direction: column;
          gap: $spacing-md;
          align-items: flex-start;

          h4 {
            margin: 0;
            color: $primary-blue;
            font-size: 1.1rem;
            font-weight: $font-weight-bold;
          }

          .search-bar {
            display: flex;
            align-items: center;
            width: 100%;
            background: $light-gray;
            border-radius: $border-radius-md;
            padding: $spacing-sm $spacing-md;
            border: 1px solid $gray-border;

            .search-icon {
              margin-right: $spacing-sm;
              color: $gray-medium;
            }

            input {
              flex: 1;
              border: none;
              background: transparent;
              outline: none;
              font-size: 0.9rem;
              padding: $spacing-xs 0;

              &::placeholder {
                color: $gray-medium;
                opacity: 0.7;
              }
            }
          }
        }

        .drivers-list {
          display: flex;
          flex-direction: column;
          gap: $spacing-md;
          max-height: 500px;
          overflow-y: auto;
          padding-right: $spacing-xs;

          &::-webkit-scrollbar {
            width: 6px;
          }

          &::-webkit-scrollbar-track {
            background: $light-gray;
            border-radius: 3px;
          }

          &::-webkit-scrollbar-thumb {
            background: $gray-medium;
            border-radius: 3px;

            &:hover {
              background: $gray-dark;
            }
          }
        }

        .driver-card {
          display: flex;
          align-items: center;
          padding: $spacing-sm;
          border: 2px solid $gray-border;
          border-radius: $border-radius-md;
          cursor: pointer;
          transition: all 0.2s ease;
          position: relative;
          background: $white;
          min-height: auto; // Allow card to expand with content
          height: auto; // Ensure height adjusts to content
          overflow: visible; // Ensure content is not clipped

          &:hover {
            border-color: $primary-blue;
            box-shadow: 0 4px 12px rgba($primary-blue, 0.1);
            transform: translateY(-1px);
          }

          &.selected {
            border-color: $primary-blue;
            background: linear-gradient(135deg, rgba($primary-blue, 0.05) 0%, rgba($primary-blue, 0.1) 100%);
            box-shadow: 0 4px 16px rgba($primary-blue, 0.2);
          }

          .driver-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, $primary-blue 0%, $primary-blue-dark 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: $white;
            font-size: 1.2rem;
            margin-right: $spacing-lg;
            flex-shrink: 0;
          }

          .driver-info {
            flex: 1;

            .driver-name {
              display: flex;
              align-items: center;
              gap: $spacing-sm;
              margin-bottom: $spacing-sm;

              h5 {
                margin: 0;
                font-size: 1.1rem;
                font-weight: $font-weight-bold;
                color: $black;
              }

              .driver-id {
                font-size: 0.8rem;
                color: $gray-medium;
                background: $light-gray;
                padding: 2px 6px;
                border-radius: $border-radius-sm;
              }
            }

            .driver-details {
              display: flex;
              flex-direction: column;
              gap: $spacing-xs;

              .detail-item {
                display: flex;
                align-items: center;
                gap: $spacing-xs;
                font-size: 0.9rem;
                color: $gray-dark;

                .detail-icon {
                  color: $primary-blue;
                  width: 14px;
                  flex-shrink: 0;
                }
              }
            }
          }

          .driver-status {
            margin-right: $spacing-md;

            .status-badge {
              padding: $spacing-xs $spacing-sm;
              border-radius: $border-radius-sm;
              font-size: 0.8rem;
              font-weight: $font-weight-bold;
              text-transform: uppercase;
              letter-spacing: 0.5px;

              &.available {
                background: rgba($primary-success-code-color, 0.1);
                color: $primary-success-code-color;
                border: 1px solid rgba($primary-success-code-color, 0.2);
              }
              
              &.busy, &.on_ride {
                background: rgba($button-accent-bg, 0.1);
                color: $button-accent-bg;
                border: 1px solid rgba($button-accent-bg, 0.2);
              }
              
              &.offline {
                background: rgba($gray-border, 0.1);
                color: $gray-border;
                border: 1px solid rgba($gray-border, 0.2);
              }
            }
          }

          .selected-indicator {
            position: absolute;
            top: $spacing-sm;
            right: $spacing-sm;

            .checkmark {
              width: 24px;
              height: 24px;
              border-radius: 50%;
              background: $primary-success-code-color;
              color: $white;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 0.8rem;
              font-weight: bold;
              animation: checkmarkPop 0.3s ease-out;
            }
          }
        }

        .empty-state {
          text-align: center;
          padding: $spacing-xxl $spacing-lg;
          color: $gray-medium;

          .empty-icon {
            font-size: 3rem;
            margin-bottom: $spacing-lg;
          }

          h4 {
            margin: 0 0 $spacing-sm 0;
            color: $gray-dark;
            font-size: 1.2rem;
          }

          p {
            margin: 0 0 $spacing-lg 0;
            font-size: 0.9rem;
            line-height: 1.5;
          }
        }
      }
    }

    .modal-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: $spacing-lg $spacing-xl;
      border-top: 1px solid $gray-border;
      background: rgba($light-gray, 0.3);

      .footer-info {
        .selection-info {
          font-size: 0.9rem;
          color: $gray-dark;
          font-weight: $font-weight-medium;
        }
      }

      .footer-actions {
        display: flex;
        gap: $spacing-sm;

        .btn {
          display: inline-flex;
          align-items: center;
          gap: $spacing-xs;
          padding: $spacing-sm $spacing-lg;
          border-radius: $border-radius-sm;
          font-weight: $font-weight-bold;
          cursor: pointer;
          transition: all 0.2s ease;
          text-align: center;
          font-size: 0.9rem;
          border: 1px solid transparent;
          min-width: 120px;
          justify-content: center;

          &:disabled {
            opacity: 0.7;
            cursor: not-allowed;
          }

          .spinner {
            width: 16px;
            height: 16px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
          }

          &.btn-primary {
            background-color: $primary-blue;
            color: $white;
            border-color: $primary-blue;

            &:hover:not(:disabled) {
              background-color: $primary-blue-dark;
              border-color: $primary-blue-dark;
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba($primary-blue, 0.3);
            }
          }

          &.btn-secondary {
            background-color: $white;
            color: $gray-dark;
            border-color: $gray-border;

            &:hover:not(:disabled) {
              background-color: $light-gray;
              border-color: $gray-medium;
            }
          }

          &.btn-outline {
            background-color: transparent;
            color: $primary-blue;
            border-color: $primary-blue;

            &:hover:not(:disabled) {
              background-color: rgba($primary-blue, 0.05);
            }
          }
        }
      }
    }
  }
}

// Animations
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes checkmarkPop {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// Responsive design
@media (max-width: 768px) {
  .assign-driver-modal {
    .modal-content {
      width: 95%;
      max-height: 95vh;

      .modal-header {
        padding: $spacing-lg;
        flex-direction: column;
        align-items: flex-start;
        gap: $spacing-md;

        .modal-header-actions {
          align-self: flex-end;
        }
      }

      .modal-body {
        padding: $spacing-md;

        .drivers-section {
          .section-header {
            .search-bar {
              padding: $spacing-xs $spacing-sm;
            }
          }
          
          .driver-card {
            flex-direction: column;
            text-align: center;
            gap: $spacing-xs;
            min-height: 160px;

            .driver-avatar {
              margin-right: 0;
            }

            .driver-info .driver-details {
              align-items: center;
            }
          }
        }
      }

      .modal-footer {
        padding: $spacing-md;
        flex-direction: column;
        gap: $spacing-md;

        .footer-actions {
          width: 100%;

          .btn {
            flex: 1;
          }
        }
      }
    }
  }
}

@use "../../../variables" as *;

.operator-history-view {
  padding: $spacing-lg;
  background-color: $light-gray;
  min-height: 100vh;

  // Header Section
  .history-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: $spacing-lg;
    background: $white;
    padding: $spacing-lg;
    border-radius: $border-radius-lg;
    box-shadow: $box-shadow-sm;

    .header-content {
      h1 {
        display: flex;
        align-items: center;
        gap: $spacing-sm;
        color: $primary-blue;
        font-size: $font-size-xxl;
        font-weight: $font-weight-bolder;
        margin: 0 0 $spacing-xs 0;

        .header-icon {
          color: $accent-yellow;
        }
      }

      .header-subtitle {
        color: $black-light-40;
        margin: 0;
        font-size: $font-size-md;
      }
    }

    .header-actions {
      display: flex;
      gap: $spacing-sm;

      .btn {
        display: flex;
        align-items: center;
        gap: $spacing-xs;
        padding: $spacing-sm $spacing-md;
        border-radius: $border-radius-md;
        font-weight: $font-weight-medium;
        transition: $transition-default;

        &.btn-primary {
          background-color: $button-primary-bg;
          color: $button-primary-text;
          border: none;

          &:hover:not(:disabled) {
            background-color: $button-primary-hover;
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
        }

        &.btn-secondary {
          background-color: $button-secondary-bg;
          color: $button-secondary-text;
          border: 1px solid $gray-border;

          &:hover {
            background-color: $button-secondary-hover;
          }
        }

        .spinning {
          animation: spin 1s linear infinite;
          -webkit-animation: spin 1s linear infinite;
}
      }
    }
  }

  // Define spin keyframes for animation
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Statistics Grid
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: $spacing-md;
    margin-bottom: $spacing-lg;

    .stat-card {
      background: $white;
      padding: $spacing-lg;
      border-radius: $border-radius-lg;
      box-shadow: $box-shadow-sm;
      display: flex;
      align-items: center;
      gap: $spacing-md;
      transition: $transition-default;

      &:hover {
        box-shadow: $box-shadow-md;
        transform: translateY(-2px);
      }

      .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: $primary-blue-light-50;
        color: $primary-blue;
        font-size: 24px;

        &.revenue {
          background-color: $primary-success-light-45;
          color: $primary-success-code-color;
        }
      }

      .stat-content {
        .stat-value {
          font-size: $font-size-xl;
          font-weight: $font-weight-bolder;
          color: $primary-blue;
          margin-bottom: $spacing-xxs;
        }

        .stat-label {
          color: $black-light-40;
          font-size: $font-size-sm;
          font-weight: $font-weight-medium;
        }
      }
    }
  }

  // Filters Section
  .filters-section {
    background: $white;
    padding: $spacing-lg;
    border-radius: $border-radius-lg;
    box-shadow: $box-shadow-sm;
    margin-bottom: $spacing-lg;

    .filters-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: $spacing-md;
      margin-bottom: $spacing-md;

      .filter-group {
        label {
          display: block;
          margin-bottom: $spacing-xs;
          font-weight: $font-weight-medium;
          color: $primary-blue;
        }

        .search-input {
          position: relative;

          .search-icon {
            position: absolute;
            left: $spacing-sm;
            top: 50%;
            transform: translateY(-50%);
            color: $black-light-40;
          }

          input {
            padding-left: $spacing-xl;
          }
        }

        input, select {
          width: 100%;
          padding: $spacing-sm;
          border: 1px solid $gray-border;
          border-radius: $border-radius-md;
          font-size: $font-size-md;
          transition: $transition-default;

          &:focus {
            outline: none;
            border-color: $primary-blue;
            box-shadow: 0 0 0 2px rgba(0, 43, 127, 0.1);
          }
        }
      }
    }

    .filter-actions {
      display: flex;
      justify-content: flex-end;

      .btn {
        padding: $spacing-sm $spacing-md;
        border-radius: $border-radius-md;
        font-weight: $font-weight-medium;
        transition: $transition-default;
        background-color: $button-secondary-bg;
        color: $button-secondary-text;
        border: 1px solid $gray-border;

        &:hover {
          background-color: $button-secondary-hover;
        }
      }
    }
  }

  // Content Section
  .content-section {
    background: $white;
    border-radius: $border-radius-lg;
    box-shadow: $box-shadow-sm;
    overflow: hidden;

    .loading-container, .error-container, .empty-state {
      padding: $spacing-xxxl;
      text-align: center;

      .spinner {
        margin: 0 auto $spacing-md;
      }

      .error-icon, .empty-icon {
        font-size: 48px;
        color: $black-light-40;
        margin-bottom: $spacing-md;
      }

      h3 {
        color: $primary-blue;
        margin-bottom: $spacing-sm;
      }

      p {
        color: $black-light-40;
        margin-bottom: $spacing-lg;
      }

      .btn {
        padding: $spacing-sm $spacing-lg;
        border-radius: $border-radius-md;
        font-weight: $font-weight-medium;
        background-color: $button-primary-bg;
        color: $button-primary-text;
        border: none;
        transition: $transition-default;

        &:hover {
          background-color: $button-primary-hover;
        }
      }
    }

    .results-summary {
      padding: $spacing-md $spacing-lg;
      border-bottom: 1px solid $secondary-color;
      background-color: $secondary-color-light-40;

      p {
        margin: 0;
        color: $black-light-40;
        font-size: $font-size-sm;
        font-weight: $font-weight-medium;
      }
    }

    // Rides Table
    .rides-table-container {
      overflow-x: auto;

      .rides-table {
        width: 100%;
        border-collapse: collapse;

        thead {
          background-color: $primary-blue-light-50;

          th {
            padding: $spacing-md;
            text-align: left;
            font-weight: $font-weight-bolder;
            color: $primary-blue;
            border-bottom: 2px solid $primary-blue;
            white-space: nowrap;
          }
        }

        tbody {
          tr {
            border-bottom: 1px solid $secondary-color;
            transition: $transition-default;

            &:hover {
              background-color: $secondary-color-light-40;
            }

            td {
              padding: $spacing-md;
              vertical-align: top;

              .rider-info, .driver-info {
                .rider-name, .driver-name {
                  font-weight: $font-weight-medium;
                  color: $primary-blue;
                  margin-bottom: $spacing-xxs;
                }

                .rider-phone {
                  color: $black-light-40;
                  font-size: $font-size-sm;
                }
              }

              .route-info {
                .pickup, .dropoff {
                  display: flex;
                  align-items: center;
                  gap: $spacing-xs;
                  margin-bottom: $spacing-xs;
                  font-size: $font-size-sm;

                  .location-icon {
                    font-size: 12px;

                    &.pickup {
                      color: $primary-success-code-color;
                    }

                    &.dropoff {
                      color: $primary-error-code-color;
                    }
                  }
                }
              }

              .datetime-info {
                .date {
                  font-weight: $font-weight-medium;
                  color: $primary-blue;
                }
              }

              .distance {
                font-weight: $font-weight-medium;
                color: $primary-blue;
              }

              .fare {
                font-weight: $font-weight-bolder;
                color: $primary-success-code-color;
                font-size: $font-size-lg;
              }

              .actions {
                display: flex;
                justify-content: center;
                align-items: center;

                .details-btn {
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  gap: $spacing-xs;
                  padding: $spacing-xs $spacing-sm;
                  border: none;
                  border-radius: $border-radius-md;
                  background: linear-gradient(135deg, $primary-blue, $primary-blue-dark);
                  color: $white;
                  font-size: $font-size-sm;
                  font-weight: $font-weight-medium;
                  cursor: pointer;
                  transition: all 0.3s ease;
                  box-shadow: 0 2px 4px rgba(0, 43, 127, 0.2);
                  position: relative;
                  overflow: hidden;
                  min-height: 32px;

                  &::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: -100%;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                    transition: left 0.5s ease;
                  }

                  &:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 12px rgba(0, 43, 127, 0.3);
                    background: linear-gradient(135deg, $primary-blue-dark, $primary-blue-darker);

                    &::before {
                      left: 100%;
                    }

                    .eye-icon {
                      transform: scale(1.1);
                    }
                  }

                  &:active {
                    transform: translateY(0);
                    box-shadow: 0 2px 6px rgba(0, 43, 127, 0.25);
                  }

                  .eye-icon {
                    font-size: 14px;
                    transition: transform 0.3s ease;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                  }

                  .btn-text {
                    font-weight: $font-weight-medium;
                    letter-spacing: 0.5px;
                  }

                  @media (max-width: 768px) {
                    padding: $spacing-xs;
                    width: 32px;
                    height: 32px;

                    .btn-text {
                      display: none;
                    }

                    .eye-icon {
                      font-size: 16px;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    // Pagination
    .pagination {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: $spacing-sm;
      padding: $spacing-lg;
      border-top: 1px solid $secondary-color;

      .btn {
        padding: $spacing-xs $spacing-sm;
        border-radius: $border-radius-md;
        font-weight: $font-weight-medium;
        transition: $transition-default;
        border: 1px solid $gray-border;

        &.btn-primary {
          background-color: $button-primary-bg;
          color: $button-primary-text;
          border-color: $button-primary-bg;
        }

        &.btn-secondary {
          background-color: $button-secondary-bg;
          color: $button-secondary-text;

          &:hover:not(:disabled) {
            background-color: $button-secondary-hover;
          }

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
        }
      }

      .page-numbers {
        display: flex;
        gap: $spacing-xxs;
      }
    }
  }

  // Modal styles
  .modal-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    background: radial-gradient(circle at center, rgba(0, 43, 127, 0.15), rgba(0, 0, 0, 0.6));
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    z-index: 1000;
    padding: $spacing-lg;
    backdrop-filter: blur(8px);
    animation: fadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-sizing: border-box !important;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      backdrop-filter: blur(0px);
    }
    to {
      opacity: 1;
      backdrop-filter: blur(8px);
    }
  }
  
  .modal {
    background-color: $white;
    border-radius: $border-radius-lg;
    width: 100%;
    max-width: 800px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-shadow: 0 25px 50px rgba(0, 43, 127, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.05);
    animation: slideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    border: 1px solid rgba(0, 43, 127, 0.1);
    margin: 0 auto;
    position: relative;

    @keyframes slideIn {
      from {
        transform: translateY(50px) scale(0.95);
        opacity: 0;
      }
      to {
        transform: translateY(0) scale(1);
        opacity: 1;
      }
    }

    @media (max-width: 768px) {
      max-width: 95vw;
      margin: $spacing-md auto;
    }
    
    &.ride-details-modal {
      .modal-header {
        background: linear-gradient(135deg, $primary-blue, $primary-blue-dark);
        color: $white;
        padding: $spacing-lg;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: none;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
          opacity: 0.3;
        }

        .header-content {
          display: flex;
          align-items: center;
          gap: $spacing-md;
          position: relative;
          z-index: 1;

          .ride-id-badge {
            background: rgba(255, 255, 255, 0.2);
            color: $white;
            padding: $spacing-xs $spacing-sm;
            border-radius: $border-radius-pill;
            font-size: $font-size-sm;
            font-weight: $font-weight-bolder;
            letter-spacing: 0.5px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
          }

          h3 {
            margin: 0;
            font-size: $font-size-xl;
            font-weight: $font-weight-bolder;
            letter-spacing: 0.5px;
          }

          .status-indicator {
            display: flex;
            align-items: center;
            gap: $spacing-xs;
            background: rgba(255, 255, 255, 0.15);
            padding: $spacing-xs $spacing-sm;
            border-radius: $border-radius-pill;
            font-size: $font-size-sm;
            font-weight: $font-weight-medium;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
          }
        }

        .close-btn {
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          color: $white;
          cursor: pointer;
          padding: $spacing-sm;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          width: 40px;
          height: 40px;
          transition: all 0.3s ease;
          backdrop-filter: blur(10px);
          position: relative;
          z-index: 1;

          &:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            transform: rotate(90deg);
          }

          svg {
            width: 18px;
            height: 18px;
            stroke-width: 2.5;
          }
        }
      }
      
      .modal-content {
        padding: $spacing-xl;
        overflow-y: auto;
        max-height: 65vh;
        background: linear-gradient(180deg, $white 0%, rgba(248, 249, 250, 0.5) 100%);

        .modal-sections-container {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
          gap: $spacing-xl;

          @media (max-width: 768px) {
            grid-template-columns: 1fr;
            gap: $spacing-lg;
          }
        }
        
        .detail-section {
          background: $white;
          border-radius: $border-radius-lg;
          padding: $spacing-lg;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
          border: 1px solid rgba(0, 43, 127, 0.08);
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, $primary-blue, $accent-yellow);
          }

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 43, 127, 0.12);
          }

          h4 {
            color: $primary-blue;
            margin-top: 0;
            margin-bottom: $spacing-md;
            font-weight: $font-weight-bolder;
            font-size: $font-size-lg;
            display: flex;
            align-items: center;
            gap: $spacing-sm;

            &::before {
              content: '';
              width: 6px;
              height: 6px;
              background: $accent-yellow;
              border-radius: 50%;
              box-shadow: 0 0 0 3px rgba(255, 204, 0, 0.3);
            }
          }
          
          .detail-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: $spacing-md;
            gap: $spacing-md;
            padding: $spacing-sm 0;
            border-bottom: 1px solid rgba(0, 43, 127, 0.05);
            transition: all 0.2s ease;

            &:last-child {
              border-bottom: none;
              margin-bottom: 0;
            }

            &:hover {
              background: rgba(0, 43, 127, 0.02);
              margin: 0 (-$spacing-sm) $spacing-md (-$spacing-sm);
              padding: $spacing-sm;
              border-radius: $border-radius-sm;
              border-bottom: 1px solid rgba(0, 43, 127, 0.05);

              &:last-child {
                margin-bottom: 0;
              }
            }

            .label {
              flex: 0 0 110px;
              font-weight: $font-weight-bolder;
              color: $black-light-40;
              font-size: $font-size-sm;
              text-transform: uppercase;
              letter-spacing: 0.5px;
              line-height: 1.4;
            }

            .value {
              flex: 1;
              font-weight: $font-weight-medium;
              color: $black;
              line-height: 1.4;
              
              &.status-badge {
                display: inline-flex;
                align-items: center;
                gap: $spacing-xs;
                font-size: $font-size-sm;
                font-weight: $font-weight-bolder;

                &.completed {
                  color: $primary-success-code-color;
                  font-weight: $font-weight-bolder;
                }
              }

              &.fare-value {
                color: $primary-success-code-color;
                font-weight: $font-weight-bolder;
                font-size: $font-size-xl;
                background: linear-gradient(135deg, $primary-success-code-color, #2ecc71);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
              }

              &.location-value {
                color: $primary-blue;
                font-weight: $font-weight-medium;
                line-height: 1.5;
              }

              .phone-link {
                color: $primary-blue;
                text-decoration: none;
                font-weight: $font-weight-medium;
                transition: all 0.2s ease;
                display: inline-block;

                &:hover {
                  color: $primary-blue-dark;
                  text-decoration: underline;
                }
              }
            }
          }
        }
      }
    }
  }

  // Responsive Design
  @media (max-width: 768px) {
    padding: $spacing-md;

    .history-header {
      flex-direction: column;
      gap: $spacing-md;

      .header-actions {
        width: 100%;
        justify-content: stretch;

        .btn {
          flex: 1;
        }
      }
    }

    .stats-grid {
      grid-template-columns: 1fr;
    }

    .filters-section .filters-grid {
      grid-template-columns: 1fr;
    }

    .rides-table-container {
      .rides-table {
        font-size: $font-size-sm;

        th, td {
          padding: $spacing-sm;
        }
      }
    }

    .pagination {
      flex-wrap: wrap;
      gap: $spacing-xs;

      .page-numbers {
        order: 3;
        width: 100%;
        justify-content: center;
      }
    }
  }
}

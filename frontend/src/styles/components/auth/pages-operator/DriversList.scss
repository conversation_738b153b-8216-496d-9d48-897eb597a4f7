@use "../../../variables" as *;

.drivers-list {
  // Loading and error states
  .loading-container,
  .error-container,
  .no-drivers {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
    background-color: $light-gray;
    border-radius: $border-radius-md;
    margin: 1rem 0;
    min-height: 150px;
  }
  
  .loading-container .spinner {
    animation: spin 1s linear infinite;
    font-size: 2rem;
    margin-bottom: 1rem;
    color: $primary-blue;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  .error-message {
    color: #dc3545;
    margin-bottom: 1rem;
  }
  
  .no-drivers {
    color: $black-light-40;
  }
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-xl;
    flex-wrap: wrap;
    gap: $spacing-md;
    padding: $spacing-lg;
    background: linear-gradient(135deg, $white, rgba(0, 43, 127, 0.02));
    border-radius: $border-radius-lg;
    box-shadow: 0 2px 8px rgba(0, 43, 127, 0.06);
    border: 1px solid rgba(0, 43, 127, 0.08);

    h1 {
      margin: 0;
      color: $primary-blue;
      font-size: 2rem;
      font-weight: $font-weight-bolder;
      display: flex;
      align-items: center;
      gap: $spacing-sm;

      &::before {
        content: '👥';
        font-size: 1.5rem;
      }
    }

    .page-actions {
      display: flex;
      gap: $spacing-sm;
      flex-wrap: wrap;
      
      .toggle-inactive {
        display: flex;
        align-items: center;
        gap: $spacing-xs;
        background: $white;
        border: 1px solid rgba(0, 43, 127, 0.2);
        border-radius: $border-radius-md;
        padding: $spacing-sm $spacing-md;
        color: $black-light-40;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: $font-size-sm;
        font-weight: $font-weight-medium;
        box-shadow: 0 2px 4px rgba(0, 43, 127, 0.1);

        &:hover {
          background: linear-gradient(135deg, rgba(0, 43, 127, 0.05), rgba(0, 43, 127, 0.1));
          color: $primary-blue;
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(0, 43, 127, 0.15);
        }

        &.active {
          background: linear-gradient(135deg, $primary-success-light-45, rgba(39, 174, 96, 0.2));
          color: $primary-success-code-color;
          border-color: rgba(39, 174, 96, 0.3);

          svg {
            color: $primary-success-code-color;
          }
        }

        svg {
          font-size: 1.2rem;
          transition: transform 0.3s ease;
        }

        &:hover svg {
          transform: scale(1.1);
        }
      }


      .btn {
        display: inline-flex;
        align-items: center;
        gap: $spacing-xs;
        padding: $spacing-sm $spacing-lg;
        border-radius: $border-radius-md;
        font-weight: $font-weight-bolder;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: $font-size-sm;
        letter-spacing: 0.5px;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
          transition: left 0.5s ease;
        }

        &.btn-primary {
          background: linear-gradient(135deg, $primary-blue, $primary-blue-dark);
          color: $white;
          border: none;
          box-shadow: 0 2px 8px rgba(0, 43, 127, 0.2);

          &:hover {
            background: linear-gradient(135deg, $primary-blue-dark, $primary-blue-darker);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 43, 127, 0.3);

            &::before {
              left: 100%;
            }
          }

          &:active {
            transform: translateY(0);
            box-shadow: 0 2px 8px rgba(0, 43, 127, 0.25);
          }
        }
      }
    }
  }

  .drivers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: $spacing-lg;
    margin-top: $spacing-lg;
  }

  .driver-card {
    background: $white;
    border-radius: $border-radius-lg;
    box-shadow: 0 4px 12px rgba(0, 43, 127, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(0, 43, 127, 0.06);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, $primary-blue, $accent-yellow);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 12px 32px rgba(0, 43, 127, 0.15);
      border-color: rgba(0, 43, 127, 0.12);

      &::before {
        opacity: 1;
      }
    }

    .driver-header {
      padding: $spacing-lg;
      display: flex;
      align-items: flex-start;
      position: relative;
      border-bottom: 1px solid rgba(0, 43, 127, 0.08);
      background: linear-gradient(135deg, $white, rgba(0, 43, 127, 0.01));

      .driver-avatar {
        width: 56px;
        height: 56px;
        border-radius: 50%;
        background: linear-gradient(135deg, $primary-blue-light-50, rgba(0, 43, 127, 0.1));
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: $spacing-md;
        color: $primary-blue;
        font-size: 1.6rem;
        flex-shrink: 0;
        box-shadow: 0 4px 12px rgba(0, 43, 127, 0.15);
        border: 2px solid rgba(255, 255, 255, 0.8);
        transition: all 0.3s ease;

        .driver-card:hover & {
          transform: scale(1.05);
          box-shadow: 0 6px 16px rgba(0, 43, 127, 0.2);
        }
      }

      .driver-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: $spacing-xs;

        .driver-name {
          font-weight: $font-weight-bolder;
          font-size: 1.2rem;
          color: $primary-blue;
          display: flex;
          flex-direction: column;
          gap: $spacing-xs;
          line-height: 1.3;

          .driver-rating {
            font-size: $font-size-sm;
            font-weight: $font-weight-medium;
            color: $black-light-40;
            display: flex;
            align-items: center;
            gap: $spacing-xs;
            background: rgba(255, 193, 7, 0.1);
            padding: 2px $spacing-xs;
            border-radius: $border-radius-pill;
            width: fit-content;

            &::before {
              content: '⭐';
              font-size: 0.9rem;
              margin-right: 2px;
            }
          }
        }
      }

      .status-badge {
        padding: $spacing-xs $spacing-sm;
        border-radius: $border-radius-pill;
        font-size: $font-size-sm;
        font-weight: $font-weight-bolder;
        text-transform: uppercase;
        letter-spacing: 0.8px;
        margin-left: auto;
        white-space: nowrap;
        display: flex;
        align-items: center;
        gap: 4px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        border: 1px solid transparent;

        &::before {
          content: '';
          width: 6px;
          height: 6px;
          border-radius: 50%;
          display: block;
        }

        &.available {
          background: linear-gradient(135deg, #e8f5e9, rgba(46, 125, 50, 0.2));
          color: #2e7d32;
          border-color: rgba(46, 125, 50, 0.3);

          &::before {
            background: #2e7d32;
            box-shadow: 0 0 0 2px rgba(46, 125, 50, 0.3);
          }
        }

        &.on-ride {
          background: linear-gradient(135deg, #fff3e0, rgba(245, 124, 0, 0.2));
          color: #f57c00;
          border-color: rgba(245, 124, 0, 0.3);

          &::before {
            background: #f57c00;
            box-shadow: 0 0 0 2px rgba(245, 124, 0, 0.3);
          }
        }

        &.offline {
          background: linear-gradient(135deg, #f5f5f5, rgba(117, 117, 117, 0.2));
          color: #757575;
          border-color: rgba(117, 117, 117, 0.3);

          &::before {
            background: #757575;
            box-shadow: 0 0 0 2px rgba(117, 117, 117, 0.3);
          }
        }

        &.inactive {
          background: linear-gradient(135deg, #ffebee, rgba(198, 40, 40, 0.2));
          color: #c62828;
          border-color: rgba(198, 40, 40, 0.3);

          &::before {
            background: #c62828;
            box-shadow: 0 0 0 2px rgba(198, 40, 40, 0.3);
          }
        }
      }

      .menu-button {
        background: none;
        border: none;
        color: $black-light-40;
        cursor: pointer;
        padding: $spacing-xs;
        margin-left: $spacing-sm;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          background-color: $light-gray;
          color: $primary-blue;
        }
      }

      .dropdown-menu {
        position: absolute;
        top: 60px;
        right: 15px;
        background: $white;
        border-radius: $border-radius-sm;
        box-shadow: $box-shadow-lg;
        z-index: 10;
        min-width: 180px;
        overflow: hidden;

        button {
          display: block;
          width: 100%;
          text-align: left;
          padding: $spacing-sm $spacing-md;
          background: none;
          border: none;
          color: $black;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background-color: $light-gray;
            color: $primary-blue;
          }

          &.danger {
            color: #d32f2f;

            &:hover {
              background-color: #ffebee;
            }
          }
        }
      }
    }

    .driver-details {
      padding: 0 $spacing-md $spacing-md;
      border-bottom: 1px solid $gray-border;

      .detail-row {
        display: flex;
        align-items: center;
        margin-bottom: $spacing-sm;

        &:last-child {
          margin-bottom: 0;
        }

        .icon {
          margin-right: $spacing-sm;
          color: $primary-blue;
          width: 16px;
          text-align: center;
        }

        .detail-value {
          flex: 1;
          font-size: 0.9rem;
          color: $black;

          a {
            color: $primary-blue;
            text-decoration: none;

            &:hover {
              text-decoration: underline;
            }
          }


          .last-active {
            font-size: 0.8rem;
            color: $black-light-40;
            margin-left: $spacing-xs;
          }
        }
      }
    }

    .driver-actions {
      padding: $spacing-md;
      display: flex;
      gap: $spacing-sm;

      .status-toggle {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: $spacing-xs;
        padding: $spacing-sm;
        background: none;
        border: 1px solid $gray-border;
        border-radius: $border-radius-sm;
        color: $black-light-40;
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: 0.9rem;

        &:hover {
          background-color: $light-gray;
        }

        &.inactive {
          color: #757575;
        }

        svg {
          font-size: 1.2rem;
        }
      }

      .btn {
        flex: 1;
        padding: $spacing-sm;
        border-radius: $border-radius-sm;
        font-weight: $font-weight-bold;
        cursor: pointer;
        transition: all 0.2s ease;
        text-align: center;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 6px;

        &.btn-primary {
          background-color: $primary-blue;
          color: $white;
          border: 1px solid $primary-blue;

          &:hover {
            background-color: $primary-blue-dark;
          }
        }
      }
    }
  }

  // Modal styles (reusing from OperatorHistoryView)
  .modal-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    background: radial-gradient(circle at center, rgba(0, 43, 127, 0.15), rgba(0, 0, 0, 0.6));
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    z-index: 1000;
    padding: $spacing-lg;
    backdrop-filter: blur(8px);
    animation: fadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-sizing: border-box !important;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      backdrop-filter: blur(0px);
    }
    to {
      opacity: 1;
      backdrop-filter: blur(8px);
    }
  }

  .modal {
    background-color: $white;
    border-radius: $border-radius-lg;
    width: 100%;
    max-width: 800px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-shadow: 0 25px 50px rgba(0, 43, 127, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.05);
    animation: slideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    border: 1px solid rgba(0, 43, 127, 0.1);
    margin: 0 auto;
    position: relative;

    @keyframes slideIn {
      from {
        transform: translateY(50px) scale(0.95);
        opacity: 0;
      }
      to {
        transform: translateY(0) scale(1);
        opacity: 1;
      }
    }

    @media (max-width: 768px) {
      max-width: 95vw;
      margin: $spacing-md auto;
    }

    &.driver-details-modal {
      .modal-header {
        background: linear-gradient(135deg, $primary-blue, $primary-blue-dark);
        color: $white;
        padding: $spacing-lg;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: none;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
          opacity: 0.3;
        }

        .header-content {
          display: flex;
          align-items: center;
          gap: $spacing-md;
          position: relative;
          z-index: 1;

          .driver-id-badge {
            background: rgba(255, 255, 255, 0.2);
            color: $white;
            padding: $spacing-xs $spacing-sm;
            border-radius: $border-radius-pill;
            font-size: $font-size-sm;
            font-weight: $font-weight-bolder;
            letter-spacing: 0.5px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
          }

          h3 {
            margin: 0;
            font-size: $font-size-xl;
            font-weight: $font-weight-bolder;
            letter-spacing: 0.5px;
          }

          .status-indicator {
            display: flex;
            align-items: center;
            gap: $spacing-xs;
            background: rgba(255, 255, 255, 0.15);
            padding: $spacing-xs $spacing-sm;
            border-radius: $border-radius-pill;
            font-size: $font-size-sm;
            font-weight: $font-weight-medium;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
          }
        }

        .close-btn {
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          color: $white;
          cursor: pointer;
          padding: $spacing-sm;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          width: 40px;
          height: 40px;
          transition: all 0.3s ease;
          backdrop-filter: blur(10px);
          position: relative;
          z-index: 1;

          &:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            transform: rotate(90deg);
          }

          svg {
            width: 18px;
            height: 18px;
            stroke-width: 2.5;
          }
        }
      }

      .modal-content {
        padding: $spacing-xl;
        overflow-y: auto;
        max-height: 65vh;
        background: linear-gradient(180deg, $white 0%, rgba(248, 249, 250, 0.5) 100%);

        .modal-sections-container {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
          gap: $spacing-xl;

          @media (max-width: 768px) {
            grid-template-columns: 1fr;
            gap: $spacing-lg;
          }
        }

        .detail-section {
          background: $white;
          border-radius: $border-radius-lg;
          padding: $spacing-lg;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
          border: 1px solid rgba(0, 43, 127, 0.08);
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, $primary-blue, $accent-yellow);
          }

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 43, 127, 0.12);
          }

          h4 {
            color: $primary-blue;
            margin-top: 0;
            margin-bottom: $spacing-md;
            font-weight: $font-weight-bolder;
            font-size: $font-size-lg;
            display: flex;
            align-items: center;
            gap: $spacing-sm;

            &::before {
              content: '';
              width: 6px;
              height: 6px;
              background: $accent-yellow;
              border-radius: 50%;
              box-shadow: 0 0 0 3px rgba(255, 204, 0, 0.3);
            }
          }

          .detail-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: $spacing-md;
            gap: $spacing-md;
            padding: $spacing-sm 0;
            border-bottom: 1px solid rgba(0, 43, 127, 0.05);
            transition: all 0.2s ease;

            &:last-child {
              border-bottom: none;
              margin-bottom: 0;
            }

            &:hover {
              background: rgba(0, 43, 127, 0.02);
              margin: 0 (-$spacing-sm) $spacing-md (-$spacing-sm);
              padding: $spacing-sm;
              border-radius: $border-radius-sm;
              border-bottom: 1px solid rgba(0, 43, 127, 0.05);

              &:last-child {
                margin-bottom: 0;
              }
            }

            .label {
              flex: 0 0 110px;
              font-weight: $font-weight-bolder;
              color: $black-light-40;
              font-size: $font-size-sm;
              text-transform: uppercase;
              letter-spacing: 0.5px;
              line-height: 1.4;
            }

            .value {
              flex: 1;
              font-weight: $font-weight-medium;
              color: $black;
              line-height: 1.4;

              &.rating-value {
                color: $accent-yellow;
                font-weight: $font-weight-bolder;
                font-size: $font-size-lg;
              }

              &.location-value {
                color: $primary-blue;
                font-weight: $font-weight-medium;
                line-height: 1.5;
              }

              &.rides-value {
                color: $primary-success-code-color;
                font-weight: $font-weight-bolder;
                font-size: $font-size-lg;
              }

              .phone-link {
                color: $primary-blue;
                text-decoration: none;
                font-weight: $font-weight-medium;
                transition: all 0.2s ease;
                display: inline-block;

                &:hover {
                  color: $primary-blue-dark;
                  text-decoration: underline;
                }
              }

              .btn {
                &.btn-sm {
                  padding: $spacing-xs $spacing-sm;
                  font-size: $font-size-xs;
                  border-radius: $border-radius-sm;

                  &.btn-primary {
                    background: linear-gradient(135deg, $primary-blue, $primary-blue-dark);
                    color: $white;
                    border: none;
                    box-shadow: 0 2px 4px rgba(0, 43, 127, 0.2);
                    transition: all 0.3s ease;

                    &:hover {
                      background: linear-gradient(135deg, $primary-blue-dark, $primary-blue-darker);
                      transform: translateY(-1px);
                      box-shadow: 0 4px 8px rgba(0, 43, 127, 0.3);
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 992px) {
  .drivers-list {
    .page-header {
      flex-direction: column;
      align-items: flex-start;
      
      .page-actions {
        width: 100%;
        justify-content: space-between;
      }
    }

    .drivers-grid {
      grid-template-columns: 1fr;
    }
  }
}

@media (max-width: 576px) {
  .drivers-list {
    .driver-actions {
      flex-direction: column;
      
      .status-toggle,
      .btn {
        width: 100%;
      }
    }
  }
}

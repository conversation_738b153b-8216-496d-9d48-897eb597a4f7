@use "../../../variables" as *;

.operator-dashboard {
  display: flex;
  min-height: 100vh;
  background-color: $light-gray;
  box-sizing: border-box;
  color: $black;
  font-family: $font-primary;

  .operator-main {
    flex: 1;
    margin-left: 250px;
    padding: $spacing-lg;
    box-sizing: border-box;
    transition: all 0.3s ease;
  }

  .dashboard-content {
    background: $white;
    border-radius: $border-radius-lg;
    padding: $spacing-xl;
    box-shadow: $box-shadow-sm;
    min-height: calc(100vh - #{$navbar-height} - #{$spacing-xxl});
    position: relative;
    overflow: hidden;
  }

  // Header Styles
  .dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-xxl;
    padding-bottom: $spacing-lg;
    border-bottom: 1px solid $border-color;
  }


  .user-profile {
    display: flex;
    align-items: center;
    gap: $spacing-md;

    .user-avatar {
      font-size: 3.5rem;
      color: $primary-blue;
      background: rgba($primary-blue, 0.1);
      border-radius: 50%;
      padding: $spacing-xs;
    }

    .user-info {
      h2 {
        margin: 0;
        font-size: 1.5rem;
        font-weight: bold;
        color: $black;
      }

      .user-role {
        display: flex;
        align-items: center;
        gap: $spacing-md;
        margin-top: $spacing-xs;
      }

      .role-badge {
        background: $primary-blue;
        color: $white;
        padding: $spacing-xxs $spacing-sm;
        border-radius: $border-radius-pill;
        font-size: 0.75rem;
        font-weight: bold;
        text-transform: uppercase;
        letter-spacing: 0.05em;

        &.admin { background: $accent-red; }
        &.operator { background: $primary-blue; }
        &.driver { background: $primary-success-code-color; }
      }


      .current-time {
        color: $gray-medium;
        font-size: 0.875rem;
      }
    }
  }

  
  .notifications {
    position: relative;
    cursor: pointer;
    padding: $spacing-sm;
    border-radius: 50%;
    transition: all 0.2s ease;

    &:hover {
      background: rgba($primary-blue, 0.1);
    }

    .notification-bell {
      font-size: 1.25rem;
      color: $gray-medium;
    }

    .notification-badge {
      position: absolute;
      top: 0;
      right: 0;
      background: $accent-red;
      color: $white;
      border-radius: 50%;
      width: 18px;
      height: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.625rem;
      font-weight: bold;
    }
  }

  // Stats Grid
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: $spacing-lg;
    margin-bottom: $spacing-xxl;
  }

  .stat-card {
    background: $white;
    border-radius: $border-radius-lg;
    padding: $spacing-lg;
    display: flex;
    align-items: center;
    gap: $spacing-md;
    box-shadow: $box-shadow-xs;
    transition: transform 0.2s ease, box-shadow 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: $box-shadow-md;
    }

    .stat-icon-container {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      background: rgba($primary-blue, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      color: $primary-blue;
      font-size: 1.25rem;
    }

    .stat-details {
      .stat-value {
        font-size: 1.5rem;
        font-weight: bold;
        color: $black;
        line-height: 1.2;
      }

      .stat-label {
        font-size: 0.875rem;
        color: $gray-medium;
        margin-top: $spacing-xxs;
      }
    }
  }

  // Dashboard Main Content Area
  .dashboard-main {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: $spacing-xxl;
  }

  .content-section {
    background: $white;
    border-radius: $border-radius-lg;
    padding: $spacing-xl;
    box-shadow: $box-shadow-sm;
  }

  // Update Log
  .update-log {
    h3 {
      margin: 0 0 $spacing-lg 0;
      font-size: 1.25rem;
      color: $black;
      padding-bottom: $spacing-sm;
      border-bottom: 1px solid $border-color;
    }
  }

  .update-list {
    display: flex;
    flex-direction: column;
    gap: $spacing-md;
  }

  .update-item {
    background: $white;
    border-radius: $border-radius-md;
    padding: $spacing-md;
    border-left: 3px solid $primary-blue;
    transition: all 0.2s ease;
    box-shadow: $box-shadow-xs;

    &.improvement { border-left-color: $primary-success-code-color; }
    &.fix { border-left-color: $accent-red; }
    &.feature { border-left-color: $accent-yellow; }

    &:hover {
      transform: translateX(4px);
      box-shadow: $box-shadow-sm;
    }

    .update-date {
      font-size: 0.75rem;
      color: $gray-medium;
      margin-bottom: $spacing-xxs;
    }

    .update-text {
      font-size: 0.9rem;
      color: $black;
    }
  }
}

// Responsive adjustments
@media (max-width: 1200px) {
  .dashboard-main {
    grid-template-columns: 1fr !important;
  }
}

@media (max-width: 992px) {
  .operator-dashboard {
    .operator-main {
      margin-left: 0;
      padding: $spacing-md;
      padding-top: calc(#{$navbar-height} + #{$spacing-md});
    }


    .dashboard-content {
      padding: $spacing-lg !important;
      min-height: calc(100vh - #{$navbar-height} - #{$spacing-lg} * 2) !important;
    }
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr 1fr !important;
  }
}

@media (max-width: 576px) {
  .stats-grid {
    grid-template-columns: 1fr !important;
  }
  
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-md;
  }
  
  .notifications {
    align-self: flex-end;
  }
}

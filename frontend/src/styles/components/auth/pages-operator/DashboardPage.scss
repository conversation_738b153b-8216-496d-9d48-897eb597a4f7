@use "../../../variables.scss" as *;

.dashboard-page {
  padding: $spacing-lg;
  max-width: 1400px;
  margin: 0 auto;
  color: $black;
  font-family: $font-primary;
  background: linear-gradient(to bottom, lighten($gray-light, 10%), $white);
  min-height: 100vh;

  .dashboard-content {
    background: transparent;
    border-radius: $border-radius-lg;
    padding: $spacing-xl 0;
    box-shadow: none;
  }

  // Header Styles
  .dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-lg;
    padding: $spacing-lg $spacing-xl;
    border-bottom: none;
    background: linear-gradient(135deg, lighten($primary-blue, 10%) 0%, $primary-blue 100%);
    border-radius: $border-radius-lg;
    color: $white;
    box-shadow: 0 4px 12px rgba($primary-blue, 0.3);
  }

  .user-profile {
    display: flex;
    align-items: center;
    gap: $spacing-md;

    .user-avatar {
      font-size: 3.2rem;
      color: $white;
      opacity: 0.9;
    }

    .user-info {
      h2 {
        margin: 0;
        font-size: 1.7rem;
        font-weight: $font-weight-bold;
        color: $white;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
      }

      .user-role {
        display: flex;
        align-items: center;
        gap: $spacing-md;
        margin-top: $spacing-xxs;
      }

      .role-badge {
        background: $white;
        color: $primary-blue;
        padding: $spacing-xs $spacing-md;
        border-radius: $border-radius-pill;
        font-size: 0.8rem;
        font-weight: $font-weight-bold;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        
        &.operator { 
          background: $white;
          color: $primary-blue;
        }
      }

      .current-time {
        color: rgba($white, 0.9);
        font-size: 0.9rem;
        font-style: italic;
      }
    }
  }
  
  .notifications {
    position: relative;
    cursor: pointer;
    padding: $spacing-sm;
    transition: transform $transition-default;

    &:hover {
      transform: scale(1.1);
    }

    .notification-link {
      color: inherit;
      text-decoration: none;
      display: flex;
      align-items: center;
      position: relative;
    }

    .notification-bell {
      font-size: 1.8rem;
      color: $white;
    }

    .notification-badge {
      position: absolute;
      top: -5px;
      right: -5px;
      background: $accent-yellow;
      color: $black;
      border-radius: 50%;
      width: 22px;
      height: 22px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.8rem;
      font-weight: $font-weight-bold;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    }
  }

  // Loading and Error States
  .loading-container, .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $spacing-xl;
    text-align: center;
    
    .spinner {
      margin-bottom: $spacing-md;
    }
    
    .error-icon {
      font-size: 3rem;
      color: $accent-red;
      margin-bottom: $spacing-md;
    }
    
    p {
      margin-bottom: $spacing-lg;
      color: $gray-medium;
    }
  }

  // Stats Grid
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: $spacing-md;
    margin-bottom: $spacing-xl;
  }

  .stat-card {
    background: $white;
    border-radius: $border-radius-md;
    padding: $spacing-md;
    display: flex;
    align-items: center;
    gap: $spacing-md;
    box-shadow: $box-shadow-sm;
    position: relative;
    border: 1px solid $gray-light;

    .stat-icon-container {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.25rem;
      
      &.pending {
        background: rgba($accent-yellow, 0.2);
        color: darken($accent-yellow, 20%);
      }
      
      &.assigned {
        background: rgba($primary-blue, 0.2);
        color: $primary-blue;
      }
      
      &.available {
        background: rgba($primary-success-code-color, 0.2);
        color: $primary-success-code-color;
      }
      
      &.completed {
        background: rgba($button-accent-bg, 0.2);
        color: darken($button-accent-bg, 20%);
      }
    }

    .stat-details {
      flex: 1;
      
      .stat-value {
        font-size: 1.5rem;
        font-weight: $font-weight-bold;
        color: $black;
        line-height: 1.2;
      }

      .stat-label {
        font-size: 0.875rem;
        color: $gray-medium;
      }
    }
    
    .stat-action {
      font-size: 0.75rem;
      color: $primary-blue;
      text-decoration: none;
      font-weight: $font-weight-bold;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }

  // Dashboard Grid Layout
  .dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    gap: $spacing-lg;
    grid-template-areas:
      "pending-rides available-drivers"
      "active-rides active-rides";
  }

  .dashboard-section {
    background: $white;
    border-radius: $border-radius-md;
    padding: $spacing-md;
    box-shadow: $box-shadow-sm;
    border: 1px solid $gray-light;
    
    &.pending-rides {
      grid-area: pending-rides;
    }
    
    &.available-drivers {
      grid-area: available-drivers;
    }
    
    &.active-rides {
      grid-area: active-rides;
    }
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: $spacing-md;
      
      h3 {
        margin: 0;
        font-size: 1.1rem;
        font-weight: $font-weight-bold;
        color: $black;
      }
      
      .view-all {
        font-size: 0.875rem;
        color: $primary-blue;
        text-decoration: none;
        
        &:hover {
          text-decoration: underline;
        }
      }
    }
    
    .empty-state {
      padding: $spacing-lg;
      text-align: center;
      color: $gray-medium;
      font-style: italic;
    }
  }
  
  // Ride Cards
  .rides-list {
    display: flex;
    flex-direction: column;
    gap: $spacing-md;
  }
  
  .ride-card {
    background: $white;
    border-radius: $border-radius-md;
    padding: $spacing-md;
    box-shadow: $box-shadow-xs;
    border: 1px solid $gray-light;
    
    &.pending {
      border-left: 3px solid $accent-yellow;
    }
    
    &.active {
      border-left: 3px solid $primary-blue;
    }
    
    .ride-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: $spacing-sm;
      
      .ride-time, .ride-date, .ride-id {
        font-size: 0.875rem;
        color: $gray-medium;
      }
      
      .ride-status {
        .status-badge {
          padding: $spacing-xxs $spacing-sm;
          border-radius: $border-radius-sm;
          font-size: 0.75rem;
          font-weight: $font-weight-bold;
          
          &.assigned {
            background: $accent-yellow;
            color: $black;
          }
          
          &.pickedup, &.picked_up {
            background: $primary-blue;
            color: $white;
          }
          
          &.inprogress, &.in_progress {
            background: $primary-success-code-color;
            color: $white;
          }
        }
      }
    }
    
    .ride-details {
      .locations {
        margin-bottom: $spacing-sm;
        
        .pickup, .dropoff {
          display: flex;
          align-items: center;
          gap: $spacing-sm;
          margin-bottom: $spacing-xs;
          font-size: 0.9rem;
          
          .pickup-icon {
            color: $primary-success-code-color;
          }
          
          .dropoff-icon {
            color: $primary-error-code-color;
          }
          
          .location-text {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
      
      .passenger-info {
        margin-top: $spacing-sm;
        
        .name {
          font-weight: $font-weight-bold;
          font-size: 0.9rem;
        }
        
        .passengers {
          display: flex;
          gap: $spacing-md;
          font-size: 0.8rem;
          color: $gray-medium;
          margin-top: $spacing-xxs;
        }
      }
    }
    
    .ride-route {
      display: flex;
      align-items: center;
      gap: $spacing-sm;
      margin-bottom: $spacing-md;
      
      .origin, .destination {
        flex: 1;
        font-size: 0.9rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      .route-arrow {
        color: $gray-medium;
        font-size: 0.8rem;
      }
    }
    
    .driver-info {
      display: flex;
      justify-content: space-between;
      font-size: 0.875rem;
      margin-bottom: $spacing-md;
      
      .driver-name, .assigned-time {
        display: flex;
        align-items: center;
        gap: $spacing-xs;
      }
    }
    
    .ride-actions {
      display: flex;
      justify-content: flex-end;
      margin-top: $spacing-sm;
    }
  }
  
  // Driver Cards
  .drivers-list {
    display: flex;
    flex-direction: column;
    gap: $spacing-md;
  }
  
  .driver-card {
    display: flex;
    align-items: center;
    background: $white;
    border-radius: $border-radius-md;
    padding: $spacing-md;
    box-shadow: $box-shadow-xs;
    border: 1px solid $gray-light;
    
    .driver-info {
      display: flex;
      align-items: center;
      gap: $spacing-sm;
      flex: 1;
      
      .driver-avatar {
        font-size: 1.5rem;
        color: $primary-blue;
      }
      
      .driver-details {
        .driver-name {
          font-weight: $font-weight-bold;
          font-size: 0.9rem;
        }
        
        .driver-vehicle {
          font-size: 0.8rem;
          color: $gray-medium;
        }
      }
    }
    
    .driver-status {
      margin: 0 $spacing-md;
      
      .status-badge {
        padding: $spacing-xxs $spacing-sm;
        border-radius: $border-radius-sm;
        font-size: 0.75rem;
        font-weight: $font-weight-bold;
        
        &.available {
          background: $primary-success-code-color;
          color: $white;
        }
      }
    }
  }
  
  .btn {
    padding: $spacing-xs $spacing-md;
    border-radius: $border-radius-sm;
    font-weight: $font-weight-bold;
    border: none;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    font-size: 0.875rem;
    transition: all $transition-default;
    
    &.btn-sm {
      padding: $spacing-xxs $spacing-sm;
      font-size: 0.75rem;
    }
    
    &.btn-primary {
      background: $button-primary-bg;
      color: $button-primary-text;
      
      &:hover {
        background: $button-primary-hover;
      }
    }
    
    &.btn-secondary {
      background: $button-secondary-bg;
      color: $button-secondary-text;
      
      &:hover {
        background: $button-secondary-hover;
      }
    }
    
    &.btn-accent {
      background: $button-accent-bg;
      color: $button-accent-text;
      
      &:hover {
        background: $button-accent-hover;
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 1200px) {
  .dashboard-page {
    padding: $spacing-md;
    
    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: $spacing-md;
    }
  }
}

@media (max-width: 768px) {
  .dashboard-page {
    padding: $spacing-sm;
    
    .dashboard-content {
      padding: $spacing-md;
    }
    
    .dashboard-grid {
      grid-template-columns: 1fr;
      grid-template-areas:
        "pending-rides"
        "available-drivers"
        "active-rides";
    }
    
    .stats-grid {
      grid-template-columns: 1fr;
      gap: $spacing-md;
    }
  }
}
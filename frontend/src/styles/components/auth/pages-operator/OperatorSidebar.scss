@use "../../../variables.scss" as *;

.operator-sidebar {
  width: 250px;
  background: $primary-blue;
  color: $white;
  height: 100%;//!calc(100vh-30px):;
  position: fixed;
  left: 0;
  top: 0px;
  z-index: 100;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
  overflow-y: auto;

  .sidebar-header {
    padding: $spacing-lg;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    
    h2 {
      margin: 0;
      font-size: 1.2rem;
      font-weight: $font-weight-bold;
    }
  }

  .sidebar-nav {
    padding: $spacing-md 0;

    ul {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .nav-link {
      display: flex;
      align-items: center;
      padding: $spacing-md $spacing-lg;
      color: rgba(255, 255, 255, 0.8);
      text-decoration: none;
      transition: all 0.2s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        color: $white;
      }

      &.active {
        background: rgba(255, 255, 255, 0.2);
        color: $white;
        border-left: 4px solid $accent-yellow;
      }

      .nav-icon {
        margin-right: $spacing-md;
        font-size: 1.1rem;
      }

      .nav-label {
        font-size: 0.9rem;
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 992px) {
  .operator-sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    
    &.open {
      transform: translateX(0);
    }
  }
}

// Mobile menu toggle button
.mobile-menu-toggle {
  display: none;
  position: fixed;
  top: $spacing-md;
  left: $spacing-md;
  z-index: 1100;
  background: $primary-blue;
  color: $white;
  border: none;
  border-radius: 4px;
  padding: $spacing-sm $spacing-md;
  cursor: pointer;
  font-size: 1.2rem;

  @media (max-width: 992px) {
    display: block;
  }
}

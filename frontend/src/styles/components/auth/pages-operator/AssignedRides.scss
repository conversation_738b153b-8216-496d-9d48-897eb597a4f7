@use "../../../variables" as *;

.assigned-rides {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-lg;

    h1 {
      margin: 0;
      color: $primary-blue;
      font-size: 1.8rem;
      font-weight: $font-weight-bold;
    }

    .btn {
      display: inline-flex;
      align-items: center;
      gap: $spacing-xs;
      padding: $spacing-sm $spacing-md;
      border-radius: $border-radius-sm;
      font-weight: $font-weight-bold;
      cursor: pointer;
      transition: all 0.2s ease;

      &.btn-primary {
        background-color: $primary-blue;
        color: $white;
        border: 1px solid $primary-blue;

        &:hover {
          background-color: $primary-blue-dark;
        }
      }
    }
  }

  .rides-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: $spacing-md;
  }

  .ride-card {
    background: $white;
    border-radius: $border-radius-md;
    box-shadow: $box-shadow-sm;
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    display: flex;
    flex-direction: column;

    &:hover {
      transform: translateY(-2px);
      box-shadow: $box-shadow-md;
    }

    .ride-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: $spacing-sm $spacing-md;
      background-color: $primary-blue-light-50;
      border-bottom: 1px solid $gray-border;

      .ride-id-section {
        display: flex;
        align-items: center;
        gap: $spacing-sm;

        .ride-id {
          font-weight: $font-weight-bold;
          color: $primary-blue-dark-10;
        }

        .btn-icon-details {
          background: rgba($primary-blue, 0.1);
          border: 1px solid rgba($primary-blue, 0.2);
          border-radius: 50%;
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.2s ease;
          color: $primary-blue;

          &:hover {
            background: $primary-blue;
            color: $white;
            transform: scale(1.1);
            box-shadow: 0 2px 8px rgba($primary-blue, 0.3);
          }

          svg {
            font-size: 0.9rem;
          }
        }
      }

      .status-badge {
        padding: 0.25rem 0.5rem;
        border-radius: 1rem;
        font-size: 0.75rem;
        font-weight: $font-weight-bold;
        text-transform: uppercase;
        letter-spacing: 0.5px;

        &.status-assigned {
          background-color: #e3f2fd;
          color: #1976d2;
        }

        &.status-picked-up {
          background-color: #e8f5e9;
          color: #388e3c;
        }

        &.status-in-progress {
          background-color: #fff3e0;
          color: #f57c00;
        }

        &.status-completed {
          background-color: #e8f5e9;
          color: #2e7d32;
        }

        &.status-unknown {
          background-color: #f5f5f5;
          color: #616161;
        }
      }
    }

    .ride-details {
      padding: $spacing-md;
      flex: 1;

      .detail-row {
        display: flex;
        align-items: flex-start;
        margin-bottom: $spacing-md;

        .icon {
          margin-right: $spacing-sm;
          margin-top: 4px;
          color: $primary-blue;

          &.pickup {
            color: $primary-success-code-color;
          }
        }

        .detail {
          flex: 1;
        }

        .label {
          font-size: 0.75rem;
          color: $black-light-40;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          margin-bottom: 2px;
        }

        .value {
          font-weight: $font-weight-bold;
          color: $black;
        }
      }

      .driver-info {
        display: flex;
        align-items: center;
        padding: $spacing-md;
        background-color: $light-gray;
        border-radius: $border-radius-sm;
        margin: $spacing-md 0;

        .driver-avatar {
          width: 50px;
          height: 50px;
          border-radius: 50%;
          background-color: $primary-blue-light-50;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: $spacing-md;
          color: $primary-blue;
          font-size: 1.5rem;
        }

        .driver-details {
          flex: 1;

          .driver-name {
            font-weight: $font-weight-bold;
            margin-bottom: 2px;
          }

          .driver-vehicle {
            font-size: 0.9rem;
            color: $black-light-40;
            margin-bottom: 4px;
          }

          .driver-phone {
            display: flex;
            align-items: center;
            gap: 4px;
            color: $primary-blue;
            text-decoration: none;
            font-size: 0.9rem;

            &:hover {
              text-decoration: underline;
            }
          }
        }
      }


      .ride-timing {
        display: flex;
        justify-content: space-between;
        background-color: $light-gray;
        padding: $spacing-sm $spacing-md;
        border-radius: $border-radius-sm;

        .timing {
          text-align: center;

          .label {
            font-size: 0.7rem;
            color: $black-light-40;
            text-transform: uppercase;
            margin-bottom: 2px;
          }

          .value {
            font-weight: $font-weight-bold;
            font-size: 1.1rem;
          }
        }
      }
    }

    .ride-actions {
      display: flex;
      flex-direction: column;
      padding: $spacing-md;
      gap: $spacing-sm;
      border-top: 1px solid $gray-border;
      position: relative;

      .primary-actions {
        display: flex;
        gap: $spacing-sm;
        flex-wrap: wrap;
      }

      .secondary-actions {
        display: flex;
        justify-content: flex-end;
      }

      .btn {
        flex: 1;
        min-width: 0;
        padding: $spacing-sm $spacing-md;
        border-radius: $border-radius-sm;
        font-weight: $font-weight-bold;
        cursor: pointer;
        transition: all 0.2s ease;
        text-align: center;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: $spacing-xs;
        white-space: nowrap;
        border: 1px solid transparent;

        svg {
          font-size: 20px; // Define the base size for the icon
          width: 1em;      // Width will be 1 * 20px = 20px
          height: 1em;     // Height will be 1 * 20px = 20px
          display: inline-block; // Ensure proper rendering
          vertical-align: middle; // Align with text if any
          flex-shrink: 0; // Prevent shrinking in flex container
        }

        &.btn-primary {
          background-color: $primary-blue;
          color: $white;
          border: 1px solid $primary-blue;

          &:hover {
            background-color: $primary-blue-dark;
          }
        }

        &.btn-secondary {
          background-color: $white;
          color: $primary-blue;
          border: 1px solid $primary-blue;

          &:hover {
            background-color: $light-gray;
          }
        }

        &.btn-outline {
          background-color: transparent;
          color: $black-light-40;
          border: 1px solid $gray-border;

          &:hover {
            background-color: $light-gray;
            color: $black;
          }
        }

        &.btn-success {
          background-color: $primary-success-code-color;
          color: $white;
          border: 1px solid $primary-success-code-color;

          &:hover {
            background-color: darken($primary-success-code-color, 10%);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba($primary-success-code-color, 0.3);
          }
        }

        &.btn-cancel {
          background-color: #dc3545;
          color: $white;
          border: 1px solid #dc3545;

          &:hover {
            background-color: darken(#dc3545, 10%);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(#dc3545, 0.3);
          }
        }

        &.btn-special-operator {
          background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
          color: $white;
          border: none;
          font-weight: $font-weight-bold;
          position: relative;
          overflow: hidden;
          box-shadow: 0 2px 8px rgba(238, 90, 36, 0.2);

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
          }

          &:hover {
            background: linear-gradient(135deg, #ee5a24 0%, #ff6b6b 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(238, 90, 36, 0.4);

            &::before {
              left: 100%;
            }
          }

          &:active {
            transform: translateY(0);
          }
        }
      }
    }
  }

  .reassign-modal, .ride-details-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    overflow-y: auto;
    padding: $spacing-md;

    .modal-content {
      background: $white;
      border-radius: $border-radius-md;
      padding: $spacing-lg;
      width: 100%;
      max-width: 500px;
      box-shadow: $box-shadow-lg;
      margin: auto;

      h3 {
        margin-top: 0;
        color: $primary-blue;
        margin-bottom: $spacing-sm;
        font-size: 1.5rem;
        border-bottom: 2px solid $primary-blue-light-50;
        padding-bottom: $spacing-sm;
      }

      p {
        margin-bottom: $spacing-lg;
        color: $black-light-40;
      }

      .modal-actions {
        display: flex;
        justify-content: flex-end;
        gap: $spacing-sm;
        margin-top: $spacing-lg;
      }

      &.details-content {
        max-width: 800px;

        h4 {
          color: $primary-blue;
          margin-top: 0;
          margin-bottom: $spacing-md;
          font-size: 1.1rem;
          border-bottom: 1px solid $primary-blue-light-50;
          padding-bottom: $spacing-xs;
        }

        .ride-details-grid {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: $spacing-lg;

          .detail-section {
            margin-bottom: $spacing-md;

            .detail-item {
              display: flex;
              margin-bottom: $spacing-sm;
              align-items: baseline;

              .label {
                font-weight: $font-weight-bold;
                color: $black-light-40;
                flex: 0 0 120px;
                font-size: 0.9rem;
              }

              .value {
                flex: 1;
                color: $black;
                
                a {
                  color: $primary-blue;
                  text-decoration: none;
                  
                  &:hover {
                    text-decoration: underline;
                  }
                }
                
                &.status .status-badge {
                  display: inline-block;
                }
              }
            }
          }
        }
      }
    }
  }
  
  .loading-container, .error-container, .no-rides {
    text-align: center;
    padding: $spacing-lg;
    background: $white;
    border-radius: $border-radius-md;
    box-shadow: $box-shadow-sm;
    margin-bottom: $spacing-lg;
    
    .spinner {
      animation: spin 1s linear infinite;
      font-size: 1.5rem;
      color: $primary-blue;
      margin-bottom: $spacing-sm;
    }
    
    .error-message {
      color: $primary-error-code-color;
      margin-bottom: $spacing-md;
    }
  }
  
  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
}

// Action groups for better organization
  .action-group {
    position: relative;
    display: inline-block;
    
    .action-dropdown {
      position: absolute;
      top: 100%;
      right: 0;
      background: $white;
      border-radius: $border-radius-sm;
      box-shadow: $box-shadow-md;
      min-width: 180px;
      z-index: 100; // Higher z-index to ensure it appears above other elements
      overflow: visible; // Changed from hidden to ensure content isn't cut off
      display: none;
      flex-direction: column;
      margin-top: $spacing-xs;
      border: 1px solid $gray-border;
      max-height: none; // Ensure no height restriction
      padding: $spacing-xxs 0; // Added subtle padding top/bottom
    }

    .action-dropdown.show {
      display: block;
    }

    .dropdown-btn {
      display: flex;
      align-items: center;
      gap: $spacing-sm;
      width: 100%;
      padding: $spacing-sm $spacing-md; // use spacing variables for consistency
      border: none;
      background: transparent;
      text-align: left;
      cursor: pointer;
      font-size: $font-size-sm;
      color: $black;
      transition: background-color 0.2s ease, color 0.2s ease;

      &:hover {
        background-color: $primary-blue-light-50;
        color: $primary-blue-dark-10;
      }

      &:first-child {
        border-top-left-radius: $border-radius-sm;
        border-top-right-radius: $border-radius-sm;
      }

      &:last-child {
        border-bottom-left-radius: $border-radius-sm;
        border-bottom-right-radius: $border-radius-sm;
      }

      svg {
        margin-right: $spacing-xs;
        font-size: 1rem;
        min-width: 16px;
      }
    }

    .dropdown-btn + .dropdown-btn {
      border-top: 1px solid $gray-light;
    }
  }

// Global dropdown styles to support portal rendering
.action-dropdown {
  position: absolute;
  background: $white;
  border: 1px solid $gray-border;
  border-radius: $border-radius-sm;
  box-shadow: $box-shadow-md;
  min-width: 180px;
  display: none;
  flex-direction: column;
  padding: $spacing-xxs 0;
  z-index: 100; // ensure on top

  &.show {
    display: block;
  }

  .dropdown-btn {
    display: flex;
    align-items: center;
    gap: $spacing-sm;
    width: 100%;
    padding: $spacing-sm $spacing-md;
    border: none;
    background: transparent;
    text-align: left;
    cursor: pointer;
    font-size: $font-size-sm;
    color: $black;
    transition: background-color 0.2s ease, color 0.2s ease;

    &:hover {
      background-color: $primary-blue-light-50;
      color: $primary-blue-dark-10;
    }

    &:first-child {
      border-top-left-radius: $border-radius-sm;
      border-top-right-radius: $border-radius-sm;
    }

    &:last-child {
      border-bottom-left-radius: $border-radius-sm;
      border-bottom-right-radius: $border-radius-sm;
    }

    svg {
      margin-right: $spacing-xs;
      font-size: 1rem;
      min-width: 16px;
    }
  }

  .dropdown-btn + .dropdown-btn {
    border-top: 1px solid $gray-light;
  }
}

// Add specific styling for the dropdown portal container
#dropdown-portal-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 0; 
  pointer-events: none; 
  
  .action-dropdown {
    pointer-events: auto; 
  }
}

// Responsive adjustments
@media (max-width: 1200px) {
  .assigned-rides {
    .rides-grid {
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    }
    
    .ride-card {
      .ride-actions {
        .primary-actions {
          flex-direction: column;
          gap: $spacing-xs;
        }

        .btn {
          padding: $spacing-sm;
          font-size: 0.9rem;

          span {
            display: none;
          }

          svg {
            margin: 0;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .assigned-rides {
    .rides-grid {
      grid-template-columns: 1fr;
    }

    .ride-actions {
      padding: $spacing-sm;
      gap: $spacing-xs;

      .primary-actions {
        flex-direction: column;
        gap: $spacing-xs;
      }

      .secondary-actions {
        margin-top: $spacing-xs;
      }

      &::-webkit-scrollbar {
        height: 4px;
      }
      
      &::-webkit-scrollbar-thumb {
        background-color: $gray-border;
        border-radius: 4px;
      }
      
      .btn {
        flex: 0 0 auto;
        padding: $spacing-xs $spacing-sm;
        font-size: 0.8rem;
      }
    }
    
    .modal-content.details-content {
      .ride-details-grid {
        grid-template-columns: 1fr;
      }
    }
  }
}

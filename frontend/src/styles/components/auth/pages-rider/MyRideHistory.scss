@use "../../../variables" as *;

.rider-history-container {
    padding: $spacing-md;
    max-width: 1200px;
    margin: 0 auto;
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-md;

    h1 {
        color: $primary-blue;
        font-size: $font-size-xxl;
        margin: 0;
    }

    .refresh-button {
        background: $button-primary-bg;
        color: $button-primary-text;
        border: none;
        padding: $spacing-sm;
        border-radius: $border-radius-md;
        cursor: pointer;
        transition: $transition-default;

        &:hover {
            background: $button-primary-hover;
        }

        &:active {
            background: $button-primary-active;
        }
    }
}

.search-container {
    position: relative;
    margin-bottom: $spacing-md;

    input {
        width: 100%;
        padding: $spacing-sm $spacing-md;
        padding-right: $spacing-xl;
        border: 1px solid $gray-border;
        border-radius: $border-radius-md;
        font-size: $font-size-md;
    }

    .search-icon {
        position: absolute;
        right: $spacing-md;
        top: 50%;
        transform: translateY(-50%);
        color: $gray-medium;
    }

    .clear-search-button {
        position: absolute;
        right: $spacing-md + 25px;
        top: 50%;
        transform: translateY(-50%);
        background: $button-secondary-bg;
        border: none;
        padding: $spacing-xs $spacing-sm;
        border-radius: $border-radius-sm;
        cursor: pointer;

        &:hover {
            background: $button-secondary-hover;
        }
    }
}

.ride-list-container {
    background: $white;
    border-radius: $border-radius-lg;
    box-shadow: $box-shadow-md;
    overflow: hidden;
}

.ride-list-header {
    display: grid;
    grid-template-columns: 1.5fr 2fr 2fr 1.5fr 1fr 1fr;
    background: $primary-blue-light-50;
    padding: $spacing-sm;
    font-weight: $font-weight-bold;
    color: $primary-blue-dark;

    span {
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: $spacing-xs;

        &:hover {
            color: $primary-blue-dark-20;
        }
    }
}

.ride-history-list {
    max-height: 60vh;
    overflow-y: auto;
}

.ride-list-item {
    display: grid;
    grid-template-columns: 1.5fr 2fr 2fr 1.5fr 1fr 1fr;
    padding: $spacing-sm;
    border-bottom: 1px solid $gray-light;
    cursor: pointer;

    &:hover {
        background: $light-gray;
    }

    .ride-summary {
        display: contents;

        span {
            padding: 0 $spacing-sm;
        }
    }
}

.ride-details-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: $shadow-darker;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;

    .modal-content {
        background: $white;
        padding: $spacing-lg;
        border-radius: $border-radius-lg;
        max-width: 600px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
        position: relative;

        .close-button {
            position: absolute;
            top: $spacing-sm;
            right: $spacing-sm;
            background: none;
            border: none;
            font-size: $font-size-lg;
            cursor: pointer;
            color: $gray-dark;

            &:hover {
                color: $black;
            }
        }

        h2 {
            color: $primary-blue;
            margin-bottom: $spacing-md;
        }

        .section {
            margin-bottom: $spacing-md;

            h3 {
                color: $primary-blue-dark;
                margin-bottom: $spacing-sm;
            }

            .detail-row {
                display: flex;
                margin-bottom: $spacing-sm;

                .label {
                    font-weight: $font-weight-bold;
                    min-width: 120px;
                    color: $gray-dark;
                }

                .value {
                    flex: 1;
                    color: $black;
                }
            }
        }

        .close-modal-button {
            background: $button-primary-bg;
            color: $button-primary-text;
            border: none;
            padding: $spacing-sm $spacing-md;
            border-radius: $border-radius-md;
            cursor: pointer;
            width: 100%;
            transition: $transition-default;

            &:hover {
                background: $button-primary-hover;
            }

            &:active {
                background: $button-primary-active;
            }
        }
    }
}


.error-container {
    text-align: center;
    padding: $spacing-lg;

    .error-message {
        color: $primary-error-code-color;
        margin-bottom: $spacing-md;
    }

    .retry-button {
        background: $button-accent-bg;
        color: $button-accent-text;
        border: none;
        padding: $spacing-sm $spacing-md;
        border-radius: $border-radius-md;
        cursor: pointer;

        &:hover {
            background: $button-accent-hover;
        }
    }
}
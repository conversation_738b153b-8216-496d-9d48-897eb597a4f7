// frontend/src/styles/components/auth/LoginExtraEffects.scss
@use "../../variables" as *;

.login-container {
  transition: transform $transition-default, box-shadow $transition-default;

  &:hover {
    transform: translateY(-2px); // Slight lift on hover
    box-shadow: $box-shadow-lg; // Larger shadow for depth
  }

  .logo-link {
    .logo {
      transition: transform $transition-default, opacity $transition-default;

      &:hover {
        transform: scale(1.05); // Slight zoom on hover
        opacity: 0.9; // Subtle fade
      }
    }
  }

  .input-group {
    input {
      transition: border-color $transition-default, box-shadow $transition-default;

      &:focus {
        border-color: $primary-blue;
        box-shadow: 0 0 5px $shadow-dark; // Glow effect on focus
        outline: none;
      }
    }

    .password-wrapper {
      .toggle-password {
        transition: color $transition-default, transform $transition-default;

        &:hover {
          color: $primary-blue-dark;
          transform: translateY(-50%) scale(1.1); // Slight bounce on hover
        }
      }
    }
  }

  .options {
    .forgot-password {
      transition: color $transition-default;

      &:hover {
        color: $primary-blue-dark;
        text-decoration: underline;
      }
    }
  }

  .login-button {
    transition: background-color $transition-default, transform $transition-default;

    &:hover {
      background-color: $button-primary-hover; // Updated to button-specific hover color
      transform: scale(1.02); // Subtle grow effect
    }

    &:active {
      background-color: $button-primary-active; // Updated to button-specific active color
      transform: scale(0.98); // Slight press effect
    }
  }

  .signup-link {
    a {
      transition: color $transition-default;

      &:hover {
        color: $primary-blue-dark;
        text-decoration: underline;
      }
    }
  }

  .disclaimer {
    a {
      transition: color $transition-default;

      &:hover {
        color: $primary-blue-dark;
        text-decoration: underline;
      }
    }
  }
}
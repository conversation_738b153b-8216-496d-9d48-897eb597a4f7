@use "../../../variables" as *;

.driver-report-container {
  padding: $spacing-lg;
  max-width: 1400px;
  margin: 0 auto;
  background: linear-gradient(135deg, $light-gray, rgba(248, 249, 250, 0.8));
  min-height: 100vh;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-xl;
  padding: $spacing-lg;
  background: linear-gradient(135deg, $white, rgba(0, 43, 127, 0.02));
  border-radius: $border-radius-lg;
  box-shadow: 0 2px 8px rgba(0, 43, 127, 0.06);
  border: 1px solid rgba(0, 43, 127, 0.08);

  h1 {
    margin: 0;
    color: $primary-blue;
    font-size: 2rem;
    font-weight: $font-weight-bolder;
    display: flex;
    align-items: center;
    gap: $spacing-sm;

    &::before {
      content: '📊';
      font-size: 1.5rem;
    }
  }

  .header-actions {
    display: flex;
    gap: $spacing-sm;
    align-items: center;

    .period-selector {
      padding: $spacing-sm $spacing-md;
      border-radius: $border-radius-md;
      border: 1px solid rgba(0, 43, 127, 0.2);
      background: $white;
      color: $primary-blue;
      font-weight: $font-weight-medium;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        border-color: $primary-blue;
      }

      &:focus {
        outline: none;
        border-color: $primary-blue;
        box-shadow: 0 0 0 2px rgba(0, 43, 127, 0.1);
      }
    }

    .refresh-button {
      background: linear-gradient(135deg, $primary-blue, $primary-blue-dark);
      color: $white;
      border: none;
      border-radius: 50%;
      width: 48px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 2px 8px rgba(0, 43, 127, 0.2);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
        z-index: 1;
      }

      &:hover {
        background: linear-gradient(135deg, $primary-blue-dark, $primary-blue-darker);
        transform: translateY(-2px) rotate(180deg);
        box-shadow: 0 6px 16px rgba(0, 43, 127, 0.3);

        &::before {
          left: 100%;
        }
      }

      svg {
        font-size: 1.2rem;
        transition: transform 0.3s ease;
        color: $white !important;
        fill: currentColor;
        position: relative;
        z-index: 2;
        width: 20px;
        height: 20px;
      }
    }
  }
}

// Welcome section
.welcome-section {
  background: linear-gradient(135deg, $white, rgba(0, 43, 127, 0.02));
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  margin-bottom: $spacing-xl;
  box-shadow: 0 2px 8px rgba(0, 43, 127, 0.06);
  border: 1px solid rgba(0, 43, 127, 0.08);

  .welcome-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: $spacing-md;

    h2 {
      color: $primary-blue;
      font-size: 1.5rem;
      font-weight: $font-weight-bolder;
      margin: 0;
    }

    .driver-info {
      display: flex;
      gap: $spacing-md;
      align-items: center;

      .vehicle-type,
      .current-location {
        display: flex;
        align-items: center;
        gap: $spacing-xs;
        color: $black-light-40;
        font-size: $font-size-md;
        font-weight: $font-weight-medium;

        svg {
          color: $primary-blue;
        }
      }
    }
  }
}

// Dashboard content
.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: $spacing-xl;
}

// Modern dashboard cards
.dashboard-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: $spacing-lg;

  .metric-card {
    background: $white;
    border-radius: $border-radius-lg;
    box-shadow: 0 4px 12px rgba(0, 43, 127, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 43, 127, 0.06);
    position: relative;
    display: flex;
    align-items: center;
    padding: $spacing-lg;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      transition: opacity 0.3s ease;
    }

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 12px 32px rgba(0, 43, 127, 0.15);
      border-color: rgba(0, 43, 127, 0.12);

      &::before {
        opacity: 1;
      }
    }

    .card-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: $spacing-md;
      font-size: 1.5rem;
      color: $white;
      flex-shrink: 0;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .card-content {
      flex: 1;

      h3 {
        margin: 0 0 $spacing-xs 0;
        color: $black-light-40;
        font-size: $font-size-sm;
        font-weight: $font-weight-bolder;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .metric-value {
        margin: 0;
        font-size: 1.8rem;
        font-weight: $font-weight-bolder;
        color: $primary-blue;
        line-height: 1.2;
      }
    }

    // Card-specific colors
    &.earnings {
      &::before { background: linear-gradient(90deg, #2ecc71, #27ae60); }
      .card-icon { background: linear-gradient(135deg, #2ecc71, #27ae60); }
    }

    &.trips {
      &::before { background: linear-gradient(90deg, $primary-blue, $primary-blue-dark); }
      .card-icon { background: linear-gradient(135deg, $primary-blue, $primary-blue-dark); }
    }

    &.distance {
      &::before { background: linear-gradient(90deg, #f39c12, #e67e22); }
      .card-icon { background: linear-gradient(135deg, #f39c12, #e67e22); }
    }

    &.average {
      &::before { background: linear-gradient(90deg, #9b59b6, #8e44ad); }
      .card-icon { background: linear-gradient(135deg, #9b59b6, #8e44ad); }
    }

    &.completion {
      &::before { background: linear-gradient(90deg, #1abc9c, #16a085); }
      .card-icon { background: linear-gradient(135deg, #1abc9c, #16a085); }
    }

    &.acceptance {
      &::before { background: linear-gradient(90deg, #e74c3c, #c0392b); }
      .card-icon { background: linear-gradient(135deg, #e74c3c, #c0392b); }
    }
  }
}

// Chart section
.chart-section {
  background: $white;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  box-shadow: 0 4px 12px rgba(0, 43, 127, 0.08);
  border: 1px solid rgba(0, 43, 127, 0.06);

  h3 {
    color: $primary-blue;
    font-size: $font-size-lg;
    font-weight: $font-weight-bolder;
    margin: 0 0 $spacing-md 0;
    display: flex;
    align-items: center;
    gap: $spacing-sm;

    svg {
      color: $primary-blue;
    }
  }

  .chart-container {
    height: 300px;
    position: relative;
  }
}

// Ride types section
.ride-types-section {
  background: $white;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  box-shadow: 0 4px 12px rgba(0, 43, 127, 0.08);
  border: 1px solid rgba(0, 43, 127, 0.06);

  h3 {
    color: $primary-blue;
    font-size: $font-size-lg;
    font-weight: $font-weight-bolder;
    margin: 0 0 $spacing-md 0;
  }

  .ride-types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: $spacing-md;

    .ride-type-card {
      background: linear-gradient(135deg, $white, rgba(0, 43, 127, 0.02));
      border-radius: $border-radius-md;
      padding: $spacing-md;
      border: 1px solid rgba(0, 43, 127, 0.08);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 43, 127, 0.1);
      }

      h4 {
        margin: 0 0 $spacing-xs 0;
        color: $primary-blue;
        font-weight: $font-weight-bolder;
      }

      p {
        margin: 0;
        color: $black-light-40;
        font-size: $font-size-md;
        font-weight: $font-weight-medium;
      }

      &.economy { border-left: 4px solid #2ecc71; }
      &.comfort { border-left: 4px solid $primary-blue; }
      &.xl { border-left: 4px solid #f39c12; }
      &.cargo { border-left: 4px solid #9b59b6; }
    }
  }
}

// Tips section
.tips-section {
  background: linear-gradient(135deg, $white, rgba(0, 43, 127, 0.02));
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  box-shadow: 0 2px 8px rgba(0, 43, 127, 0.06);
  border: 1px solid rgba(0, 43, 127, 0.08);
  border-left: 4px solid $accent-yellow;

  h3 {
    color: $primary-blue;
    font-size: $font-size-lg;
    font-weight: $font-weight-bolder;
    margin: 0 0 $spacing-md 0;
    display: flex;
    align-items: center;
    gap: $spacing-sm;
  }

  ul {
    margin: 0;
    padding-left: $spacing-lg;
    list-style: none;

    li {
      color: $black-light-40;
      font-size: $font-size-md;
      font-weight: $font-weight-medium;
      margin-bottom: $spacing-sm;
      position: relative;
      line-height: 1.5;

      &::before {
        content: '✓';
        position: absolute;
        left: -$spacing-lg;
        color: $primary-success-code-color;
        font-weight: $font-weight-bolder;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .driver-report-container {
    padding: $spacing-md;
  }

  .report-header {
    padding: $spacing-md;
    flex-direction: column;
    gap: $spacing-md;
    text-align: center;

    h1 {
      font-size: 1.5rem;
    }

    .header-actions {
      width: 100%;
      justify-content: center;
    }
  }

  .welcome-section {
    padding: $spacing-md;

    .welcome-header {
      flex-direction: column;
      text-align: center;

      .driver-info {
        justify-content: center;
      }
    }
  }

  .dashboard-cards {
    grid-template-columns: 1fr;
    gap: $spacing-md;
  }

  .chart-section {
    padding: $spacing-md;

    .chart-container {
      height: 250px;
    }
  }

  .ride-types-section {
    padding: $spacing-md;

    .ride-types-grid {
      grid-template-columns: 1fr;
    }
  }

  .tips-section {
    padding: $spacing-md;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .dashboard-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1025px) {
  .dashboard-cards {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1400px) {
  .dashboard-cards {
    grid-template-columns: repeat(3, 1fr);
  }
}
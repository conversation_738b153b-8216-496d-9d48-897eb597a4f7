@use "../../../variables" as *;

.driver-nav {
  display: flex;
  justify-content: center;
  background-color: $primary-blue; /* Matches your Curaçao theme */
  padding: $spacing-sm; /* 10px */
  flex-wrap: wrap; /* Enables wrapping for multiple rows */

  a {
    color: $white;
    text-decoration: none;
    padding: $spacing-sm $spacing-md; /* 10px 20px */
    font-size: $font-size-lg; /* 18px */
    transition: $transition-default; /* 0.3s ease */
    flex: 1 0 45%; /* Takes ~half the width, forcing 2 per row */
    text-align: center; /* Centers text inside each link */
    margin: $spacing-xs; /* 5px margin for spacing */

    &:hover {
      background-color: $primary-blue-dark; /* Darker blue on hover */
    }

    &.active {
      background-color: $accent-yellow; /* Yellow for active link */
      color: $black;
    }
  }
}

/* Mobile adjustments (≤768px) */
@media (max-width: 768px) {
  .driver-nav {
    justify-content: space-around; /* Evenly distributes links */
    padding: $spacing-sm; /* 10px */
  }

  .driver-nav a {
    flex: 1 0 45%; /* Ensures two links per row */
    margin: $spacing-xs; /* 5px */
  }
}

/* Desktop adjustments (>768px) */
@media (min-width: 769px) {
  .driver-nav {
    flex-wrap: nowrap; /* Keeps links in one row */
  }

  .driver-nav a {
    flex: 0 1 auto; /* Links take their natural width */
    margin: 0 $spacing-md; /* 0 20px for horizontal spacing */
  }
}
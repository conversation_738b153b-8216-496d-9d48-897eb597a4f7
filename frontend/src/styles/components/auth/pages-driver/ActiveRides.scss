@import '../../../variables.scss';

.dashboard-container {
  padding: $spacing-lg;
  max-width: 1400px;
  margin: 0 auto;
  background: linear-gradient(135deg, $light-gray, rgba(248, 249, 250, 0.8));
  min-height: 100vh;

  .dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-xl;
    padding: $spacing-lg;
    background: linear-gradient(135deg, $white, rgba(0, 43, 127, 0.02));
    border-radius: $border-radius-lg;
    box-shadow: 0 2px 8px rgba(0, 43, 127, 0.06);
    border: 1px solid rgba(0, 43, 127, 0.08);

    h1 {
      margin: 0;
      color: $primary-blue;
      font-size: 2rem;
      font-weight: $font-weight-bolder;
      display: flex;
      align-items: center;
      gap: $spacing-sm;
      
      &::before {
        content: '🚗';
        font-size: 1.5rem;
      }
    }

    .refresh-button {
      background: linear-gradient(135deg, $primary-blue, $primary-blue-dark);
      color: $white;
      border: none;
      border-radius: 50%;
      width: 48px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 2px 8px rgba(0, 43, 127, 0.2);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
      }

      &:hover {
        background: linear-gradient(135deg, $primary-blue-dark, $primary-blue-darker);
        transform: translateY(-2px) rotate(180deg);
        box-shadow: 0 6px 16px rgba(0, 43, 127, 0.3);

        &::before {
          left: 100%;
        }
      }

      &:active {
        transform: translateY(0) rotate(180deg);
        box-shadow: 0 2px 8px rgba(0, 43, 127, 0.25);
      }

      svg {
        font-size: 1.2rem;
        transition: transform 0.3s ease;
      }
    }
  }

  .active-ride-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: $spacing-lg;
    margin-top: $spacing-lg;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: $spacing-md;
    }
  }

  .active-ride-card {
    background: $white;
    border-radius: $border-radius-lg;
    box-shadow: 0 4px 12px rgba(0, 43, 127, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 43, 127, 0.06);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, $primary-blue, $accent-yellow);
    }

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 12px 32px rgba(0, 43, 127, 0.15);
      border-color: rgba(0, 43, 127, 0.12);
    }

    .card-header {
      padding: $spacing-lg;
      background: linear-gradient(135deg, $white, rgba(0, 43, 127, 0.01));
      border-bottom: 1px solid rgba(0, 43, 127, 0.08);

      h2 {
        margin: 0 0 $spacing-md 0;
        color: $primary-blue;
        font-size: $font-size-xl;
        font-weight: $font-weight-bolder;
        display: flex;
        align-items: center;
        gap: $spacing-sm;

        &::before {
          content: '👤';
          font-size: 1.2rem;
        }
      }

      .phone-section {
        display: flex;
        align-items: center;
        gap: $spacing-sm;
        background: rgba(0, 43, 127, 0.05);
        padding: $spacing-sm $spacing-md;
        border-radius: $border-radius-md;

        .label {
          font-weight: $font-weight-bolder;
          color: $black-light-40;
          font-size: $font-size-sm;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .value {
          font-weight: $font-weight-medium;
          color: $primary-blue;
          font-size: $font-size-md;
        }

        .icon-button {
          background: $white;
          border: 1px solid rgba(0, 43, 127, 0.2);
          border-radius: 50%;
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.3s ease;
          color: $primary-blue;

          &:hover {
            background: $primary-blue;
            color: $white;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 43, 127, 0.2);
          }

          &.copy-button:hover {
            background: $primary-success-code-color;
          }

          &.call-button:hover {
            background: $accent-yellow;
            color: $black;
          }

          svg {
            font-size: 2.9rem;
          }
        }
      }
    }

    .card-body {
      padding: $spacing-lg;

      .card-row {
        display: flex;
        align-items: flex-start;
        margin-bottom: $spacing-md;
        gap: $spacing-md;
        padding: $spacing-sm 0;
        border-bottom: 1px solid rgba(0, 43, 127, 0.05);
        transition: all 0.2s ease;

        &:last-child {
          border-bottom: none;
          margin-bottom: 0;
        }

        &:hover {
          background: rgba(0, 43, 127, 0.02);
          margin: 0 (-$spacing-sm) $spacing-md (-$spacing-sm);
          padding: $spacing-sm;
          border-radius: $border-radius-sm;
          border-bottom: 1px solid rgba(0, 43, 127, 0.05);

          &:last-child {
            margin-bottom: 0;
          }
        }

        &.full-route-row {
          background: linear-gradient(135deg, rgba(39, 174, 96, 0.05), rgba(46, 204, 113, 0.02));
          border: 1px solid rgba(39, 174, 96, 0.1);
          border-radius: $border-radius-sm;
          padding: $spacing-sm;
          margin: $spacing-sm 0;

          .label {
            color: $primary-success-code-color;
            font-weight: $font-weight-bolder;
          }

          .value {
            color: $primary-success-code-color;
            font-style: italic;
          }

          &:hover {
            background: linear-gradient(135deg, rgba(39, 174, 96, 0.08), rgba(46, 204, 113, 0.04));
            border-color: rgba(39, 174, 96, 0.2);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(39, 174, 96, 0.15);
          }
        }

        .label {
          flex: 0 0 100px;
          font-weight: $font-weight-bolder;
          color: $black-light-40;
          font-size: $font-size-sm;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          line-height: 1.4;
        }

        .value {
          flex: 1;
          font-weight: $font-weight-medium;
          color: $black;
          line-height: 1.4;
        }

        .buttons {
          flex: 0 0 auto;
          display: flex;
          gap: $spacing-xs;
          flex-wrap: wrap;

          button {
            background: linear-gradient(135deg, $primary-blue, $primary-blue-dark);
            color: $white;
            border: none;
            border-radius: $border-radius-sm;
            padding: $spacing-xs $spacing-sm;
            font-size: $font-size-xs;
            font-weight: $font-weight-medium;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 43, 127, 0.2);

            &:hover {
              background: linear-gradient(135deg, $primary-blue-dark, $primary-blue-darker);
              transform: translateY(-1px);
              box-shadow: 0 4px 8px rgba(0, 43, 127, 0.3);
            }
          }

          // Style for the route button (link styled as button)
          .map-link-button.route-button {
            background: linear-gradient(135deg, $primary-success-code-color, #2ecc71);
            color: $white;
            border: none;
            border-radius: $border-radius-sm;
            padding: $spacing-xs $spacing-sm;
            font-size: $font-size-xs;
            font-weight: $font-weight-medium;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(39, 174, 96, 0.2);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 4px;

            &:hover {
              background: linear-gradient(135deg, #2ecc71, #27ae60);
              transform: translateY(-1px);
              box-shadow: 0 4px 8px rgba(39, 174, 96, 0.3);
              text-decoration: none;
              color: $white;
            }

            svg {
              font-size: 2.9rem;
            }
          }
        }
      }
    }

    .card-actions {
      padding: $spacing-lg;
      background: linear-gradient(135deg, rgba(248, 249, 250, 0.8), $white);
      border-top: 1px solid rgba(0, 43, 127, 0.08);
      display: flex;
      gap: $spacing-sm;
      flex-wrap: wrap;

      button {
        flex: 1;
        min-width: 120px;
        padding: $spacing-sm $spacing-md;
        border: none;
        border-radius: $border-radius-md;
        font-weight: $font-weight-bolder;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: $font-size-sm;
        letter-spacing: 0.5px;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
          transition: left 0.5s ease;
        }

        &:hover::before {
          left: 100%;
        }

        &.action-finish {
          background: linear-gradient(135deg, $primary-success-code-color, #2ecc71);
          color: $white;
          box-shadow: 0 2px 8px rgba(39, 174, 96, 0.2);

          &:hover {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(39, 174, 96, 0.3);
          }
        }

        &.action-cancel {
          background: linear-gradient(135deg, #f39c12, #e67e22);
          color: $white;
          box-shadow: 0 2px 8px rgba(243, 156, 18, 0.2);

          &:hover {
            background: linear-gradient(135deg, #e67e22, #d35400);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(243, 156, 18, 0.3);
          }
        }

        &.action-delete {
          background: linear-gradient(135deg, #e74c3c, #c0392b);
          color: $white;
          box-shadow: 0 2px 8px rgba(231, 76, 60, 0.2);

          &:hover {
            background: linear-gradient(135deg, #c0392b, #a93226);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(231, 76, 60, 0.3);
          }
        }

        &:active {
          transform: translateY(0);
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none !important;
          box-shadow: none !important;
        }
      }
    }
  }

  // Toast notification
  .copy-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, $primary-success-code-color, #2ecc71);
    color: $white;
    padding: $spacing-sm $spacing-lg;
    border-radius: $border-radius-md;
    box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
    z-index: 1000;
    animation: slideInRight 0.3s ease-out;
    font-weight: $font-weight-medium;

    @keyframes slideInRight {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }
  }

  // Loading and error states
  .spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    font-size: 2rem;
    color: $primary-blue;

    &::after {
      content: '⏳';
      animation: spin 2s linear infinite;
    }

    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }
  }

  .error-container {
    text-align: center;
    padding: $spacing-xl;
    background: $white;
    border-radius: $border-radius-lg;
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.1);
    border: 1px solid rgba(231, 76, 60, 0.2);

    .error-message {
      color: #e74c3c;
      font-size: $font-size-lg;
      font-weight: $font-weight-medium;
      margin-bottom: $spacing-md;
    }

    .retry-button {
      background: linear-gradient(135deg, $primary-blue, $primary-blue-dark);
      color: $white;
      border: none;
      border-radius: $border-radius-md;
      padding: $spacing-sm $spacing-lg;
      font-weight: $font-weight-bolder;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 2px 8px rgba(0, 43, 127, 0.2);

      &:hover {
        background: linear-gradient(135deg, $primary-blue-dark, $primary-blue-darker);
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(0, 43, 127, 0.3);
      }
    }
  }

  // Empty state
  p {
    text-align: center;
    color: $black-light-40;
    font-size: $font-size-lg;
    font-weight: $font-weight-medium;
    padding: $spacing-xl;
    background: $white;
    border-radius: $border-radius-lg;
    box-shadow: 0 2px 8px rgba(0, 43, 127, 0.06);
    border: 1px solid rgba(0, 43, 127, 0.08);

    &::before {
      content: '📭';
      display: block;
      font-size: 3rem;
      margin-bottom: $spacing-md;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .dashboard-container {
    padding: $spacing-md;

    .dashboard-header {
      padding: $spacing-md;
      flex-direction: column;
      gap: $spacing-md;
      text-align: center;

      h1 {
        font-size: 1.5rem;
      }
    }

    .active-ride-card {
      .card-header {
        padding: $spacing-md;

        .phone-section {
          flex-direction: column;
          align-items: flex-start;
          gap: $spacing-xs;

          .icon-button {
            align-self: flex-end;
          }
        }
      }

      .card-body {
        padding: $spacing-md;

        .card-row {
          flex-direction: column;
          gap: $spacing-xs;

          .label {
            flex: none;
          }

          .buttons {
            align-self: flex-start;
            flex-direction: column;
            width: 100%;

            .map-link-button.route-button {
              justify-content: center;
              margin-top: $spacing-xs;
            }
          }
        }
      }

      .card-actions {
        padding: $spacing-md;
        flex-direction: column;

        button {
          min-width: auto;
        }
      }
    }
  }
}

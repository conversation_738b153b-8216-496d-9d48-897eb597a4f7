@use "../../../variables" as *;

.dashboard-container {
  padding: $spacing-md;
  background-color: $light-gray;
  min-height: 100vh;
  margin: 0 auto;
}

.dashboard-header {
  // display: flex;
  // justify-content: space-between;
  // align-items: center;
  // margin-bottom: $spacing-lg;
  // padding: $spacing-sm $spacing-md;
  // background-color: $primary-blue;
  // color: $white;
  // border-radius: $border-radius-md;
  // box-shadow: $box-shadow-sm;

  h1 {
    font-size: $font-size-xl;
    font-family: $font-secondary;
    margin: 0;
  }
}

.refresh-button {
  background-color: $button-accent-bg;
  color: $button-accent-text;
  border: none;
  padding: $spacing-md;
  border-radius: $border-radius-lg;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: $font-size-lg;
  transition: $transition-default;

  &:hover {
    background-color: $button-accent-hover;
  }

  &:active {
    background-color: $button-accent-active;
  }
}

// Table styles for desktop
.dashboard-table {
  width: 100%;
  border-collapse: collapse;
  background-color: $white;
  box-shadow: $box-shadow-md;

  th,
  td {
    padding: $spacing-sm;
    text-align: left;
    border-bottom: 1px solid $gray-border;
  }

  th {
    background-color: $primary-blue;
    color: $white;
    font-weight: $font-weight-bold;
  }

  tr:hover {
    background-color: $light-gray;
  }
}

// Card layout for mobile and tablet
.ride-request-cards {
  display: none;
}

.ride-request-card {
  background-color: $white;
  border-radius: $border-radius-lg;
  box-shadow: $box-shadow-md;
  padding: $spacing-md;
  margin-bottom: $spacing-lg;
  border-left: 6px solid $primary-blue;

  .card-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-sm;
    padding: $spacing-xs 0;
    border-bottom: 1px solid $gray-border;

    &:last-child {
      border-bottom: none;
    }
  }

  .label {
    font-family: $font-secondary;
    font-weight: $font-weight-bold;
    font-size: $font-size-lg;
    color: $primary-blue-dark;
  }

  .value {
    font-family: $font-primary;
    font-size: $font-size-lg;
    color: $black;
    text-align: right;
    max-width: 60%;
    word-wrap: break-word;
  }
}

// Error message
.error-message {
  color: $primary-error-code-color;
  font-size: $font-size-xl;
  font-family: $font-secondary;
  text-align: center;
  padding: $spacing-md;
  background-color: rgba($primary-error-code-color, 0.1);
  border-radius: $border-radius-md;
  margin: $spacing-lg;
}

// Media queries for responsive design
@media (max-width: 1024px) {
  .dashboard-table {
    display: none;
  }

  .ride-request-cards {
    display: block;
  }
}

@media (min-width: 1025px) {
  .dashboard-table {
    display: table;
  }

  .ride-request-cards {
    display: none;
  }
}

// Tablet Fix: Adjust padding and center the container
@media (min-width: 768px) and (max-width: 1024px) {
  .dashboard-container {
    padding: $spacing-sm;
    max-width: 100%;
    margin: 0 auto;
  }

  .ride-request-card {
    padding: $spacing-lg;
    margin-bottom: $spacing-xl;
  }

  .label,
  .value {
    font-size: $font-size-xl;
  }
}

// Mobile Fix
@media (max-width: 768px) {
  .dashboard-container {
    padding: $spacing-sm;
    margin: 0 auto !important; //Force fix mobile
    max-width: 100%;
  }

  .card-row {
    .label {
      margin-right: $spacing-xs; // Adding small spacing between label and value
    }
  }
}

// Button styles for actions
.action-buttons {
  display: flex;
  gap: $spacing-sm;
  justify-content: flex-start;
}

.actions-cell {
  padding: $spacing-sm;
}

button.action-accept, button.action-reject {
  padding: $spacing-sm $spacing-md;
  font-size: $font-size-md;
  border: none;
  border-radius: $border-radius-md;
  cursor: pointer;
  transition: $transition-default;
  box-shadow: $box-shadow-sm;
}

button.action-accept {
  background-color: $primary-success-code-color;
  color: $white;

  &:hover {
    background-color: $primary-success-dark-10;
  }

  &:active {
    background-color: $primary-success-dark-20;
  }
}

button.action-reject {
  background-color: $primary-error-code-color;
  color: $white;

  &:hover {
    background-color: $primary-error-dark-10;
  }

  &:active {
    background-color: $primary-error-dark-20;
  }
}

// Tablet and Desktop enhancements
@media (min-width: 768px) {
  .action-buttons {
    gap: $spacing-md;
  }

  button.action-accept, button.action-reject {
    padding: $spacing-md $spacing-lg;
    font-size: $font-size-lg;
  }

  .actions-cell {
    padding: $spacing-md;
  }
}
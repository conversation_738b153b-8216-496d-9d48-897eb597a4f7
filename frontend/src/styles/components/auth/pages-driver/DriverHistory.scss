@use "../../../variables" as *;

.driver-history-container {
  padding: $spacing-sm;
  background-color: $light-gray;
  min-height: 100vh;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-lg;
  padding: $spacing-sm $spacing-md;
  background-color: $primary-blue;
  color: $white;
  border-radius: $border-radius-md;
  box-shadow: $box-shadow-sm;
  h1 {
    font-size: $font-size-md!important;
    font-family: $font-secondary;
    margin: 0;
    color:$white !important;
  }
}

.search-container {
  position: relative;
  margin-bottom: $spacing-md;
  input {
    width: 100%;
    padding: $spacing-sm $spacing-sm $spacing-sm $spacing-xl;
    font-size: $font-size-md;
    border: 1px solid $gray-border;
    border-radius: $border-radius-md;
    margin-left: $spacing-sm;
    &:focus {
      outline: none;
      border-color: $primary-blue;
    }
  }
  .search-icon {
    position: absolute;
    left: $spacing-sm+4px;
    top: 50%;
    transform: translateY(-50%);
    margin-right: $spacing-sm!important;
    color: $gray-border;
  }
  .clear-search-button {
    position: absolute !important;
    right: $spacing-sm !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    background-color: transparent !important;
    color: $gray-border !important;
    border: none !important;
    padding: $spacing-xs !important;
    border-radius: $border-radius-sm !important;
    cursor: pointer !important;
    font-size: $font-size-md !important;

    &:hover {
      background-color: rgba(0, 0, 0, 0.05) !important;
      color: $primary-blue !important;
    }
  }
}

.ride-history-item {
  background-color: $white;
  border-radius: $border-radius-lg;
  box-shadow: $box-shadow-md;
  padding: $spacing-md;
  margin-bottom: $spacing-lg;
  border-left: 6px solid $primary-blue;

  .ride-item-header {
    margin-bottom: $spacing-md;
    h2 {
      font-size: $font-size-xxl;
      margin: 0;
      color: $primary-blue-dark;
    }
    p {
      font-size: $font-size-lg;
      margin: 5px 0 0;
      color: $black;
    }
  }

  .ride-item-details {
    .detail-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: $spacing-sm;
      padding: $spacing-xs 0;
      border-bottom: 1px solid $gray-border;
      &:last-child {
        border-bottom: none;
      }
      .label {
        font-family: $font-secondary;
        font-weight: $font-weight-bold;
        font-size: $font-size-lg;
        color: $primary-blue-dark;
        flex: 0 0 30%;
      }
      .value {
        font-family: $font-primary;
        font-size: $font-size-lg;
        color: $black;
        text-align: right;
        flex: 1;
        word-wrap: break-word;
      }
    }
  }
}

.ride-list-container {
  margin-top: $spacing-md;

  .ride-list-table {
    width: 100%;
    border-collapse: collapse;
    background-color: $white;
    border-radius: $border-radius-md;
    overflow: hidden;
    box-shadow: $box-shadow-sm;

    .ride-list-header {
      background-color: $primary-blue;
      color: $white;
      font-weight: $font-weight-bold;
      border-bottom: 2px solid $primary-blue-dark;

      th {
        text-align: left;
        padding: $spacing-sm $spacing-md;
        cursor: pointer;

        &:hover {
          background-color: $primary-blue-light-50;
        }
      }

      .header-name {
        width: 20%;
      }
      .header-phone {
        width: 15%;
      }
      .header-date {
        width: 18%;
      }
      .header-completed {
        width: 18%;
      }
      .header-fare {
        width: 12%;
      }
      .header-status {
        width: 17%;
      }
    }

    .ride-history-list {
      .ride-list-item {
        border-bottom: 1px solid $gray-border;
        cursor: pointer;

        &:last-child {
          border-bottom: none;
        }

        &:hover {
          background-color: $light-gray;
        }

        td {
          padding: $spacing-sm $spacing-md;
          text-align: left;
        }

        .rider-name {
          font-weight: $font-weight-bold;
          color: $black;
        }
        .rider-phone {
          font-size: $font-size-md;
          color: $black;
        }
        .ride-date {
          font-size: $font-size-md;
          color: $black;
        }
        .ride-completed {
          font-size: $font-size-md;
          color: $black;
        }
        .ride-fare {
          font-size: $font-size-md;
          color: $primary-blue-dark;
          font-weight: $font-weight-bold;
        }
        .ride-status {
          font-size: $font-size-md;
          color: $black;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .ride-list-container {
    .ride-list-table {
      display: block;
      overflow-x: auto;

      .ride-list-header {
        th {
          padding: $spacing-xs $spacing-sm;
          font-size: $font-size-sm;
          min-width: 80px;
        }
        .header-name, .header-phone, .header-date, .header-completed, .header-fare, .header-status {
          display: table-cell;
        }
        .header-name {
          width: 20%;
        }
        .header-phone {
          width: 15%;
        }
        .header-date {
          width: 18%;
        }
        .header-completed {
          width: 18%;
        }
        .header-fare {
          width: 12%;
        }
        .header-status {
          width: 17%;
        }
      }
      .ride-history-list {
        .ride-list-item {
          td {
            padding: $spacing-xs $spacing-sm;
            font-size: $font-size-sm;
            min-width: 80px;
          }
          .rider-name, .rider-phone, .ride-date, .ride-completed, .ride-fare, .ride-status {
            display: table-cell;
          }
        }
      }
    }
  }
}

@media (min-width: 769px) {
  .driver-history-container {
    padding: $spacing-md;
  }
  .ride-history-item {
    max-width: 600px;
    margin: 0 auto $spacing-lg auto;
  }
  .ride-list-container {
    max-width: 600px;
    margin: 0 auto;
  }
}

.ride-details-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;

  .modal-content {
    background-color: $white;
    padding: $spacing-lg;
    border-radius: $border-radius-lg;
    box-shadow: $box-shadow-lg;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;

    h2 {
      color: $primary-blue-dark;
      margin-bottom: $spacing-md;
      font-size: $font-size-xxl;
    }

    .section {
      margin-bottom: $spacing-md;
      padding: $spacing-sm;
      border-radius: $border-radius-sm;

      h3 {
        font-size: $font-size-lg;
        color: $primary-blue;
        margin-bottom: $spacing-sm;
        padding-bottom: $spacing-xs;
        border-bottom: 1px solid $gray-border;
      }

      .detail-row {
        display: grid;
        grid-template-columns: 120px 1fr;
        gap: 10px;
        margin-bottom: $spacing-sm;

        .label {
          font-weight: $font-weight-bold;
          color: $primary-blue-dark;
          text-align: right;
          padding-right: 10px;
        }

        .value {
          color: $black;
          text-align: left;
        }
      }
    }
  }

  .close-button {
    position: absolute;
    top: $spacing-sm;
    right: $spacing-sm;
    background: none;
    border: none;
    padding: $spacing-sm;
    cursor: pointer;
    color: $primary-blue-dark;
    transition: color $transition-default, transform $transition-default;

    svg {
      font-size: 24px;
    }

    &:hover {
      color: $primary-blue;
      transform: scale(1.1);
    }
  }
}

.sticky-header {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: $light-gray;
  padding: $spacing-sm 0;
}
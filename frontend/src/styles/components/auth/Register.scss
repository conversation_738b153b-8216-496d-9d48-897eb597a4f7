// frontend/src/styles/components/auth/Register.scss
@use "../../variables" as *;

.register-container {
  max-width: 420px;
  margin: $spacing-xl auto;
  padding: $spacing-lg;
  background: $white;
  border-radius: $border-radius-lg;
  box-shadow: $box-shadow-md;
  text-align: center; // Center the content

  .logo-link {
    display: inline-block;
    margin-bottom: $spacing-md;

    .logo {
      width: 150px;
      // Add transition for hover effect
      transition: transform $transition-default, opacity $transition-default;

      &:hover {
        transform: scale(1.05); // Slight zoom on hover
        opacity: 0.9; // Subtle fade
      }
    }
  }

  h2 {
    font-family: $font-primary;
    font-size: $font-size-xxl;
    color: $primary-blue-dark;
    margin-bottom: $spacing-sm;
    font-weight: $font-weight-bolder;
  }

  .subtitle {
    font-family: $font-secondary;
    font-size: $font-size-md;
    color: $gray-border;
    margin-bottom: $spacing-md;
    padding: 0 $spacing-sm;
    line-height: 1.4;
  }

  form {
    display: flex;
    flex-direction: column;
    gap: $spacing-md;
    text-align: left; // Move labels to left edge

    .input-group {
      display: flex;
      flex-direction: column;
      gap: $spacing-sm;

      label {
        font-family: $font-secondary;
        font-size: $font-size-md;
        color: $black;
        font-weight: $font-weight-bold;
      }

      input {
        padding: $spacing-sm;
        border: 1px solid $gray-border;
        border-radius: $border-radius-md;
        font-size: $font-size-md;
        font-family: $font-secondary;
        transition: border-color $transition-default;

        &:focus {
          outline: none;
          border-color: $primary-blue;
          box-shadow: 0 0 5px $transparent-white;
        }
      }

      .password-wrapper {
        position: relative;
        display: flex;
        align-items: center;

        input {
          width: 100%;
          padding-right: 70px; // Space for the toggle button
        }

        button {
          position: absolute;
          right: 5px;
          padding: calc($spacing-sm / 2) $spacing-sm; // Replaced $spacing-sm / 2 with calc() for division
          background: $button-primary-bg; // Updated to primary button color
          color: $button-primary-text; // Updated to button text color
          border: none;
          border-radius: $border-radius-sm;
          font-size: $font-size-sm;
          font-family: $font-secondary;
          cursor: pointer;
          transition: background $transition-default;

          &:hover {
            background: $button-primary-hover; // Updated to button hover color
          }
        }
      }
    }

    .error {
      color: $primary-error-code-color;
      font-size: $font-size-sm;
      font-family: $font-secondary;
      text-align: center;
      margin: $spacing-sm 0;
    }

    .register-button {
      padding: $spacing-sm $spacing-md;
      background: $button-primary-bg; // Updated to primary button color
      color: $button-primary-text; // Updated to button text color
      border: none;
      border-radius: $border-radius-md;
      font-size: $font-size-lg;
      font-family: $font-primary;
      font-weight: $font-weight-bold;
      cursor: pointer;
      transition: background $transition-default;

      &:hover {
        background: $button-primary-hover; // Updated to button hover color
      }

      &:disabled {
        background: $gray-border;
        cursor: not-allowed;
      }
    }
  }

  .switch-form {
    margin-top: $spacing-md;
    font-family: $font-secondary;
    font-size: $font-size-sm;

    .login-link {
      color: $primary-blue;
      text-decoration: none;
      &:hover {
        text-decoration: underline;
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 480px) {
  .register-container {
    margin: $spacing-md;
    padding: $spacing-md;

    h2 {
      font-size: $font-size-xl;
    }

    .subtitle {
      font-size: $font-size-sm;
      padding: 0;
    }

    form {
      gap: $spacing-sm;

      .input-group {
        input {
          font-size: $font-size-sm;
        }

        .password-wrapper {
          button {
            font-size: $font-size-sm;
            padding: calc($spacing-sm / 2); // Replaced $spacing-sm / 2 with calc() for division
          }
        }
      }

      .register-button {
        font-size: $font-size-md;
      }
    }
  }
}
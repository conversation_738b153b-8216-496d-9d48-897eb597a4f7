@use "../../variables" as *; // Base variables
@use "./LoginExtraEffects" as *; // Import extra effects (no .scss extension needed)

* {
  font-family: $font-primary;
}

.login-container {
  max-width: 400px;
  margin: $spacing-xxl auto;
  padding: $spacing-md;
  text-align: center;
  background-color: $white;
  box-shadow: $box-shadow-md;
  border-radius: $border-radius-md;

  .logo-link {
    display: inline-block;
    text-decoration: none;

    .logo {
      width: 150px;
      margin-bottom: $spacing-lg;
      display: initial;
    }
  }

  h2 {
    margin-bottom: $spacing-md;
    font-size: $font-size-xl;
    color: $black;
  }

  .input-group {
    margin-bottom: $spacing-md;
    text-align: left;

    label {
      display: block;
      font-size: $font-size-sm;
      color: $black;
      margin-bottom: calc($spacing-sm / 2); // Replaced $spacing-sm / 2 with calc() for division
    }

    input {
      display: block;
      width: 100%;
      padding: $spacing-sm;
      margin: 0;
      border: 1px solid $gray-border;
      border-radius: $border-radius-sm;
      font-size: $font-size-md;
    }

    .password-wrapper {
      position: relative;

      .toggle-password {
        position: absolute;
        right: $spacing-sm;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: $primary-blue;
        font-size: $font-size-sm;
        cursor: pointer;
        padding: 0;
      }
    }
  }

  .options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-md;

    .remember-me {
      display: flex;
      align-items: center;
      font-size: $font-size-sm;
      color: $black;

      input {
        margin-right: calc($spacing-sm / 2); // Replaced $spacing-sm / 2 with calc() for division
      }
    }

    .forgot-password {
      font-size: $font-size-sm;
      color: $primary-blue;
      text-decoration: none;
    }
  }

  .login-button {
    width: 100%;
    padding: $spacing-sm $spacing-md;
    background-color: $button-primary-bg; // Updated to new primary button color
    color: $button-primary-text; // Updated to button-specific text color
    border: none;
    border-radius: $border-radius-sm;
    font-size: $font-size-md;
    cursor: pointer;
    transition: $transition-default; // Added for smooth hover effect
  }

  .login-button:hover {
    background-color: $button-primary-hover; // Added hover state
  }

  .error {
    color: $primary-error-code-color;
    margin-top: $spacing-sm;
    font-size: $font-size-sm;
  }

  .signup-link {
    margin-top: $spacing-md;
    font-size: $font-size-sm;
    color: $black;

    a {
      color: $primary-blue;
      text-decoration: none;
    }
  }

  .disclaimer {
    margin-top: $spacing-md;
    font-size: $font-size-sm;
    color: $gray-border;

    a {
      color: $primary-blue;
      text-decoration: none;
    }
  }
}
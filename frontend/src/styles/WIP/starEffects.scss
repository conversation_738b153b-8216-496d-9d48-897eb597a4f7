// frontend/src/styles/WIP/starEffects.scss
@use "variables.scss" as *;

.wip-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  z-index: 1; /* Ensure container is above other elements */
}

/* Parallax layers */
.background-stars {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
  top: 0;
  left: 0;
  animation: scrollStars 60s linear infinite; /* Slow background */
  z-index: 2;
}

.midground-stars {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
  top: 0;
  left: 0;
  animation: scrollStars 40s linear infinite; /* Medium midground */
  z-index: 3;
}

.foreground-stars {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
  top: 0;
  left: 0;
  animation: scrollStars 20s linear infinite; /* Fast foreground */
  transition: transform 0.1s ease-out; /* Smooth cursor movement */
  z-index: 4;
}

/* Stars */
.star {
  position: absolute;
  border-radius: 50%;
  animation: twinkle 2s infinite; /* Twinkling effect */
}

/* Shooting star with trail */
.shooting-star {
  position: absolute;
  width: 4px; /* Larger for visibility */
  height: 4px;
  background: #ffffff;
  border-radius: 50%;
  box-shadow: 0 0 10px 2px #ffffff; /* Glow effect */
  animation: shoot 1s linear forwards;
  z-index: 5; /* Ensure shooting stars are on top */
}

.shooting-star::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100px; /* Trail length */
  height: 2px;
  background: linear-gradient(to right, #ffffff, transparent);
  transform: translateX(-100%); /* Position trail behind the star */
}

/* Keyframes */
@keyframes scrollStars {
  0% { transform: translateY(0); }
  100% { transform: translateY(-100vh); }
}

@keyframes twinkle {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes shoot {
  0% { transform: translateX(0) translateY(0); opacity: 1; }
  100% { transform: translateX(200px) translateY(200px); opacity: 0; }
}
// frontend/src/styles/WIP/spinner.scss
@use "variables.scss" as *;

// Spinner Styles
.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    border-top-color: $brand-primary;  // Use brand primary color for spinner
    animation: spin 1s infinite linear;
    margin: 20px auto;
    z-index: 1;
}

@keyframes spin {
    to {
      transform: rotate(360deg);
    }
}

// Progress Bar for Countdown
.progress-container {
    width: 100%;
    background-color: $brand-gray;  // Use brand gray color
    margin-top: 20px;
    height: 10px;
}

#progress-bar {
    width: 0%;
    height: 100%;
    background-color: $brand-secondary;  // Use brand secondary color
    transition: width 1s ease-in-out;  // Smooth transition
}
// temp/scss/hinaCustom.scss
@use "variables.scss" as *;

// Logo Styling with Pulsing Border
.logo-hina {
    display: flex;
    justify-content: center;
    align-items: center;

    .logo {
        max-height: 180px;
        width: 180px;  // Ensure the width matches the height for a perfect circle
        position: relative;
        top: 0;
        left: 0;
        border-radius: 50%;  // Make the logo a perfect circle
        overflow: hidden;  // Ensure content stays inside the circle
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);  // Subtle shadow for depth
        border: 3px solid $brand-white;  // Border using brand white color
        padding: 10px;
        background-color: rgba($brand-white, 0.1);  // Translucent background using brand white
        object-fit: cover;  // Ensure the image fits perfectly inside the circular frame

        /* Pulse animation using the brand accent color */
        animation: pulse-border 2s infinite;
    }
}

// Define the pulsing border animation
@keyframes pulse-border {
    0% {
        border-color: $brand-white;  // Start with brand white
        box-shadow: 0 0 10px rgba($brand-white, 0.5);  // Small glow
    }
    50% {
        border-color: $brand-secondary;  // Pulse to brand yellow
        box-shadow: 0 0 20px rgba($brand-secondary, 0.9);  // Stronger yellow glow at the pulse peak
    }
    100% {
        border-color: $brand-white;  // Return to brand white
        box-shadow: 0 0 10px rgba($brand-white, 0.5);  // Return to original glow
    }
}

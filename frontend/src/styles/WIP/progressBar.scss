// frontend/src/components/WIP/ProgressBar.js
@use "../variables.scss" as *;

.progress-container {
    width: 100%;
    background-color: $white;
    border-radius: 5px;
    margin: 20px 0;
    overflow: hidden; // Ensure nothing overflows the container
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    height: 20px; // Fix the container height
  }
  
  #progress-bar {
    width: 0%;
    height: 100%; // Make sure it matches the container height
    background: linear-gradient(90deg, $brand-secondary-light-10, $brand-secondary);
    transition: width 1s ease-in-out;
  }
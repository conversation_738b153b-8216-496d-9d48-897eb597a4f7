// frontend/src/styles/WIP/default.scss
@use "variables.scss" as *;

.wip-container {
    background-color: $brand-primary !important;
    background: url('https://github.com/alejandroatacho/alejandroatacho/raw/main/.github/img/Bottom.svg') no-repeat center center fixed;
    background-size: cover;
    color: $brand-white;
    font-family: $font-primary, Menlo, Monaco, fixed-width;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    margin: 0;
    flex-direction: column;
    text-align: center;
    overflow: hidden;  // Prevent scrollbars from showing

    // Headings
    h1, h2, h3, h4, h5, h6 {
        font-family: $font-secondary;
        color: $brand-secondary;
        margin-bottom: 20px;
    }

    h1 {
        font-size: 48px;
        font-weight: 700;
    }

    // Paragraphs
    p {
        color: $brand-white;
        font-family: $font-primary;
        font-size: 20px;
    }

    // Countdown Timer
    #countdown {
        font-size: 40px;
        color: $brand-secondary;
        font-weight: bold;
        margin-bottom: 20px;
    }

    // Links
    a {
        color: $brand-accent;
        text-decoration: none;
        font-weight: bold;
        transition: color 0.3s ease;
    }

    a:hover {
        text-decoration: underline;
        color: $brand-primary;  // Hover effect changes back to primary color
    }
}
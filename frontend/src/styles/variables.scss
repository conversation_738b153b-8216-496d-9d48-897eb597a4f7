// src/variables.scss
//! Goal of this variables.scss is to be a curacao-themed color palette and typography for the frontend, until further notice from Sinusta.
$primary-blue: #002B7F; // Updated to match Curaçao flag blue
$primary-blue-dark: #003580;
$primary-blue-darker: #003b8a;
$primary-blue-darkest: #003d8f;
$primary-error-code-color: #ff0000;
$primary-success-code-color: #27ae60;
$accent-yellow: #ffcc00;
$white: #ffffff;
$light-gray: #f8f9fa;
$gray-border: #5B5B5B;
$black: #000000;
$shadow-dark: rgba(0, 0, 0, 0.1);
$shadow-darker: rgba(0, 0, 0, 0.2);
$transparent-white: rgba(255, 255, 255, 0.2);
$border-radius-pill: 50%;
$accent-red: #FF0000;
$accent-yellow: #FFCC00;
// Pre-calculated color variations to replace lighten() and darken()
$primary-blue-light: #002B7F;
$primary-blue-light-50: #8095bf; // Approx 50% lighter than primary-blue
$primary-blue-light-45: #7389b3; // Approx 45% lighter than primary-blue
$primary-blue-dark-10: #00194c; // Approx 10% darker than primary-blue
$primary-blue-dark-20: #000c26; // Approx 20% darker than primary-blue
$primary-success-light-45: #b7e4cb; // Approx 45% lighter than primary-success-code-color
$primary-success-dark-10: #229954; // Approx 10% darker than primary-success-code-color
$primary-success-dark-20: #1c8348; // Approx 20% darker than primary-success-code-color
$primary-error-light-55: #ffcccc; // Approx 55% lighter than primary-error-code-color
$primary-error-dark-10: #cc0000; // Approx 10% darker than primary-error-code-color
$primary-error-dark-20: #990000; // Approx 20% darker than primary-error-code-color
$gray-border-dark-10: #424242; // Approx 10% darker than gray-border
$black-light-40: #666666; // Approx 40% lighter than black
$black-light-70: #b3b3b3; // Approx 70% lighter than black
$secondary-color: #E6E6E6; // Defined secondary color (same as button-secondary-bg)
$secondary-color-dark-10: #cfcfcf; // Approx 10% darker than secondary-color
$secondary-color-dark-15: #c2c2c2; // Approx 15% darker than secondary-color
$secondary-color-light-20: #f0f0f0; // Approx 20% lighter than secondary-color
$secondary-color-light-40: #fafafa; // Approx 40% lighter than secondary-color
$brand-secondary: #00ADEF; // Defined brand secondary color
$brand-secondary-light-10: #33bfff; // Approx 10% lighter than brand-secondary
$border-color: #5B5B5B;
//Primary Role Colors
$primary-color-role: #FFB107; // Taxi yellow
// Button Colors
$button-primary-bg: #005EB8; // A brighter, distinct blue for primary buttons
$button-primary-hover: #004A94; // Darker shade for hover
$button-primary-active: #003580; // Even darker for active state
$button-primary-text: $white; // White text for contrast

$button-secondary-bg: #E6E6E6; // Light gray for a neutral secondary button
$button-secondary-hover: #D1D1D1; // Slightly darker gray for hover
$button-secondary-active: #B8B8B8; // Darker still for active
$button-secondary-text: $black; // Black text for readability

$button-accent-bg: #FFB107; // A slightly deeper yellow than $accent-yellow
$button-accent-hover: #FFA000; // Darker yellow for hover
$button-accent-active: #FF8C00; // More orange-toned for active
$button-accent-text: $black; // Black text for contrast on yellow

$box-shadow-xs: 0 2px 4px $shadow-dark;
$box-shadow-sm: 0 4px 6px $shadow-dark;
$box-shadow-md: 0 4px 8px $shadow-dark;
$box-shadow-lg: 0 4px 12px $shadow-dark;
$box-shadow-xl: 0 8px 16px $shadow-dark;
$box-shadow-xxl: 0 12px 24px $shadow-dark;
$spacing-xs: 5px;
$spacing-xxs: 2px;
$spacing-sm: 10px;
$spacing-md: 20px;
$spacing-lg: 30px;
$spacing-xl: 40px;
$spacing-xxl: 60px;
$spacing-xxxl:80px;

$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-md: 16px;
$font-size-lg: 18px;
$font-size-xl: 1.2em;
$font-size-xxl: 2.5em;
$font-weight-bold:500;  //~ majority of the website is using this default bold for text color

$font-weight-medium: 600;
$font-weight-bolder: 700; //^ True Bold
$border-radius-sm: 4px;
$border-radius-md: 5px;
$border-radius-lg: 10px;
$border-radius-xl: 16px;

$transition-default: 0.3s ease;

$box-shadow-sm: 0 2px 4px $shadow-dark;
$box-shadow-md: 0 4px 6px $shadow-dark;
$box-shadow-lg: 0 4px 8px $shadow-dark;

// Typography
@import url('https://fonts.googleapis.com/css2?family=Comfortaa:wght@400;700&family=Visby+CF:wght@500&display=swap');

$font-logo: 'Dunkin Regular', sans-serif;  // Assuming Dunkin Regular is available
$font-primary: 'Visby CF', sans-serif;
$font-secondary: 'Comfortaa', sans-serif;
//! Responsive Breakpoints to focus on
// $tablet-width: 1280px;
// $mobile-width: 768px;
// $desktop-width: 1440px;
// Calculation
$navbar-height: $spacing-md * 2 + $spacing-xl; // 20px * 2 + 40px = 80px
$navbar-height-mobile: $navbar-height *1.5; //~ Fix navbar height for mobile being overlapped.
* {
    font-family: $font-primary;
    box-sizing: border-box;

}
body {
    background-color: $light-gray;
    margin: 0;
    padding: 0;
}
.spinner {
    border: 6px solid $light-gray;
    border-top: 6px solid $accent-yellow;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
    margin: $spacing-xl auto;
    -webkit-animation: spin 1s linear infinite;
}
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
// Lighter shades of primary blue for table headers
$primary-blue-light-50: lighten($primary-blue, 40%); // Lightest shade
$primary-blue-light-45: lighten($primary-blue, 35%);
$primary-blue-light-40: lighten($primary-blue, 30%);

// Grayscale variables for borders, text, and dividers
$gray-light: #cccccc;    // Light gray for borders
$gray-medium: #888888;   // Medium gray for text
$gray-dark: #333333;     // Dark gray for dividers

// Darker shades of primary blue dark for button states
$primary-blue-dark-10: darken($primary-blue-dark, 5%);
$primary-blue-dark-20: darken($primary-blue-dark, 10%);
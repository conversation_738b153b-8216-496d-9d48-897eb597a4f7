// src/pages/WIP.tsx
import { useEffect, useRef, useState } from "react";
import "../styles/WIP/WIPIndex.scss";
import { startTypingEffect } from "../components/WIP/TypingEffect";
import { startCountdown } from "../components/WIP/CountDownRedirect";
import { createStarField } from "../components/WIP/StarField";

// Access the environment variable based on your setup
const returnUrl = import.meta.env.VITE_FRONTEND_API_URL;
const WIP = () => {
  const [countdown, setCountdown] = useState(5);

  // Refs for star layers
  const backgroundRef = useRef<HTMLDivElement>(null);
  const midgroundRef = useRef<HTMLDivElement>(null);
  const foregroundRef = useRef<HTMLDivElement>(null);
  const progressBarRef = useRef<HTMLDivElement>(null);
  const typingRef = useRef<HTMLHeadingElement>(null);

  // Typing effect
  useEffect(() => {
    let cleanupTyping: (() => void) | undefined;

    if (typingRef.current) {
      cleanupTyping = startTypingEffect(typingRef.current, "Currently Under Construction!", 100);
    }

    return () => {
      if (cleanupTyping) cleanupTyping();
    };
  }, []);

  // Star field effect with parallax layers
  useEffect(() => {
    if (backgroundRef.current && midgroundRef.current && foregroundRef.current) {
      const cleanup = createStarField(backgroundRef.current, midgroundRef.current, foregroundRef.current, 50);
      return cleanup;
    }
  }, []);

  // Countdown and redirect
  useEffect(() => {
    if (progressBarRef.current) {
      const cleanup = startCountdown(
        10,
        setCountdown,
        progressBarRef.current,
        returnUrl // Use the constant defined above
      );
      return cleanup;
    }
  }, []);

  return (
    <div className="wip-container">
      {/* Parallax star layers */}
      <div className="background-stars" ref={backgroundRef}></div>
      <div className="midground-stars" ref={midgroundRef}></div>
      <div className="foreground-stars" ref={foregroundRef}></div>

      {/* Logo Section */}
      <div className="logo-hina">
        <img
          className="logo"
          src="/logo-no-background.png"
          alt="Sinusta Taxi Logo"
        />
      </div>

      <h1 id="jsRetype" ref={typingRef}></h1>
      <p>
        This page will redirect to{" "}
        <a href={returnUrl}>{returnUrl}</a> in{" "}
        <span id="countdown">{countdown}</span>{" "}
        seconds.
      </p>

      {/* Progress Bar */}
      <div className="progress-container">
        <div id="progress-bar" ref={progressBarRef}></div>
      </div>

      {/* Loading Spinner */}
      <div className="spinner"></div>

      {/* Visitor Counter */}
      <div className="visitor-counter" style={{ visibility: "hidden" }}>
        <img
          src="http://s05.flagcounter.com/count/QBT/bg_000000/txt_22B7DB/border_CCCCCC/columns_9/maxflags_300/viewers_0/labels_0/pageviews_1/flags_1/"
          alt="Visitor Counter"
        />
      </div>
    </div>
  );
};

export default WIP;
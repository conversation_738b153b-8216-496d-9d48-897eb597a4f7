// frontend/src/pages/BookTaxi.tsx
import React from "react";
import GoogleMap from "../components/GoogleMap";
import "../styles/pages/bookTaxi.scss";

const BookTaxi: React.FC = () => {
  const handleLocationUpdate = (location: { lat: number; lng: number }) => {
    console.log("User location:", location);
  };

  return (
    <div className="book-taxi-container">
      <h1>Book a Taxi</h1>
      <GoogleMap onLocationUpdate={handleLocationUpdate} />
      <p>Booking form coming soon!</p>
    </div>
  );
};

export default BookTaxi;
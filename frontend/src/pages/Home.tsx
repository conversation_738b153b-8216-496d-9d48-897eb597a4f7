import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import Navbar from '../components/blades/navbar';
import Footer from '../components/blades/footer';
import InstantRideForm from '../components/InstantRideForm';
import '../styles/pages/home.scss';

function Home() {
  const devMode = import.meta.env.VITE_FRONTEND_DEV_MODE === 'true';
  const apiUrl = import.meta.env.VITE_BACKEND_API_URL as string;

  const [data, setData] = useState<string | null>(null);

  const debugLog = (...args: unknown[]) => {
    if (devMode) console.log(...args);
  };

  useEffect(() => {
    debugLog('Development mode enabled:', devMode);
    debugLog('Fetching from:', `${apiUrl}/test/`);

    fetch(`${apiUrl}/test/`)
      .then((response) => {
        debugLog('Response status:', response.status);
        return response.text();
      })
      .then((text) => {
        debugLog('Raw response:', text);
        const parsed = JSON.parse(text);
        setData(parsed.message);
      })
      .catch((error) => {
        if (import.meta.env.VITE_FRONTEND_DEV_MODE === 'true') {
          console.error('Error fetching data:', error);
        }
        setData('Error connecting to server. Please try again later.');
      });
  }, [apiUrl, devMode]); // Add devMode if it might change; otherwise, just apiUrl

  return (
    <div>
      <Navbar />
      <header className="hero">
        <video autoPlay loop muted playsInline className="hero-video">
          <source src="/heroVid.mp4" type="video/mp4" />
          Your browser does not support the video tag.
        </video>
        <div className="hero-content">
          <h1>Sinusta Taxi: Curaçao’s Trusted Ride</h1>
          <p>Experience Safe, Timely, and Dependable Service 24/7—Airport Transfers, Hotel Travel, and Private Island Tours</p>
          <div className="cta-buttons">
            <Link to="/wip" className="cta-button">
              Book a Ride
            </Link>
            <Link to="/wip" className="cta-button">
              Apply To Become a Driver
            </Link>
            <a href="#" className="cta-button" target="_blank" rel="noopener noreferrer">
              Download Our App
            </a>
          </div>
        </div>
      </header>

      <section className="instant-ride-form">
        <h2 className="instant-ride-title">Book Your Ride Now</h2>
        <InstantRideForm />
      </section>

      <section className="signup-benefits">
        <h2>Why Create an Account?</h2>
        <div className="benefits-cards">
          <div className="benefit-card">
            <i className="fas fa-bolt benefit-icon"></i>
            <h3>Faster Bookings</h3>
            <p>Save addresses and payment methods for quick reservations.</p>
          </div>
          <div className="benefit-card">
            <i className="fas fa-history benefit-icon"></i>
            <h3>Ride History</h3>
            <p>Easily track past rides and access receipts anytime.</p>
          </div>
          <div className="benefit-card">
            <i className="fas fa-tag benefit-icon"></i>
            <h3>Exclusive Deals</h3>
            <p>Access member-only discounts and priority booking.</p>
          </div>
          <div className="benefit-card">
            <i className="fas fa-map benefit-icon"></i>
            <h3>Personalized Tours</h3>
            <p>Customize and save your tour preferences for future trips.</p>
          </div>
        </div>
        <Link to="/register" className="cta-button signup-cta">Register Now</Link>
      </section>

      <section className="services">
        <h2 className="services-title">Our Services</h2>
        <div className="service-cards">
          <div className="service-card">
            <i className="fas fa-plane-arrival service-icon"></i>
            <h3>Airport Transfers</h3>
            <p>Fixed-rate transfers from Curaçao International Airport to hotels.</p>
          </div>
          <div className="service-card">
            <i className="fas fa-map-marked-alt service-icon"></i>
            <h3>Private Tours</h3>
            <p>Explore Curaçao with our custom tour packages.</p>
          </div>
          <div className="service-card">
            <i className="fas fa-car-side service-icon"></i>
            <h3>VIP Transport</h3>
            <p>Luxury, Cargo, Comfort, XL, and armored vehicle options available.</p>
          </div>
        </div>
      </section>

      {devMode && (
        <section className="api-test">
          <h2>🚀 React + Django API Test</h2>
          <p>{data ? `API Response: ${data}` : 'Fetching API response...'}</p>
        </section>
      )}

      <Footer />
    </div>
  );
}

export default Home;
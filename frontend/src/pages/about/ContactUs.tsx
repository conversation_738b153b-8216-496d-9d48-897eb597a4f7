// frontend/src/pages/ContactUs.tsx
import Navbar from "../../components/blades/navbar";
import Footer from "../../components/blades/footer";
import "../../styles/pages/about/contactUs.scss";
import { useState } from 'react';

const ContactUs = () => {
  const [formData, setFormData] = useState({ name: '', email: '', message: '' });
  const [formStatus, setFormStatus] = useState<'idle' | 'sending' | 'sent' | 'error'>('idle');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target;
    setFormData(prev => ({ ...prev, [id]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setFormStatus('sending');
    // Simulate form submission (replace with actual API call when backend is ready)
    setTimeout(() => {
      setFormStatus('sent');
      setFormData({ name: '', email: '', message: '' });
      setTimeout(() => setFormStatus('idle'), 2000);
    }, 1000);
  };

  return (
    <>
      <Navbar />
      <div className="page-content">
        <div className="contact-page container mx-auto p-6">
          <h1 className="text-3xl font-bold mb-6">Contact Us</h1>
          <p className="text-lg mb-8">Reliable Rides Across Curaçao – Get in Touch!</p>

          {/* Business Card Style Contact Info - Redesigned as Taxi Permit */}
          <div className="contact-info-card mb-10 bg-white rounded-lg shadow-lg p-6 flex flex-col md:flex-row items-center justify-between">
            <div className="contact-card-left w-full md:w-2/3 mb-6 md:mb-0">
              <div className="permit-header flex items-center justify-between mb-4">
                <h2 className="text-2xl font-bold text-primary-blue">Sinusta Taxi Bond</h2>
                <div className="permit-badge bg-accent-yellow text-black px-3 py-1 rounded-full text-xs font-bold">OFFICIAL TAXI SERVICE</div>
              </div>
              <div className="permit-id text-sm text-gray-border mb-3">Permit No. STB-2023-CUR</div>
              <div className="permit-details-grid grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="permit-detail-item">
                  <p className="flex items-center">
                    <span className="icon-circle bg-primary-blue text-white rounded-full w-8 h-8 flex items-center justify-center mr-3">📍</span>
                    <span>Buenavista, Willemstad, Curaçao</span>
                  </p>
                </div>
                <div className="permit-detail-item">
                  <p className="flex items-center">
                    <span className="icon-circle bg-primary-blue text-white rounded-full w-8 h-8 flex items-center justify-center mr-3">📞</span>
                    <span>
                      <a href="tel:+59996733025" className="text-primary-blue hover:text-primary-blue-darkest">+5999 673 3025</a>
                      <span className="text-sm text-gray-border ml-2">(24/7 Support)</span>
                    </span>
                  </p>
                </div>
                <div className="permit-detail-item">
                  <p className="flex items-center">
                    <span className="icon-circle bg-primary-blue text-white rounded-full w-8 h-8 flex items-center justify-center mr-3">📧</span>
                    <span>
                      <a href="mailto:<EMAIL>" className="text-primary-blue hover:text-primary-blue-darkest"><EMAIL></a>
                    </span>
                  </p>
                </div>
                <div className="permit-detail-item">
                  <p className="flex items-center">
                    <span className="icon-circle bg-primary-blue text-white rounded-full w-8 h-8 flex items-center justify-center mr-3">💬</span>
                    <span>
                      <a href="https://wa.me/59996733025" target="_blank" rel="noopener noreferrer" className="text-primary-blue hover:text-primary-blue-darkest">Chat with Us on WhatsApp</a>
                    </span>
                  </p>
                </div>
              </div>
            </div>
            <div className="contact-card-right w-full md:w-1/3 flex flex-col items-center justify-center">
              <div className="permit-stamp">
                <span className="stamp-valid">VALID</span><br />
                <span className="stamp-location">CURAÇAO</span><br />
                <span className="stamp-year">2025</span>
              </div>
              <div className="social-links flex justify-center space-x-4 mt-4">
                <a href="https://www.instagram.com/curacaovacationdeal" target="_blank" rel="noopener noreferrer" className="social-icon bg-pink-600 text-white rounded-full w-10 h-10 flex items-center justify-center hover:bg-pink-700 transition">
                  <i className="fab fa-instagram text-lg"></i>
                </a>
                <a href="https://www.facebook.com/Sinusta/" target="_blank" rel="noopener noreferrer" className="social-icon bg-blue-600 text-white rounded-full w-10 h-10 flex items-center justify-center hover:bg-blue-800 transition">
                  <i className="fab fa-facebook-f text-lg"></i>
                </a>
              </div>
            </div>
          </div>

          {/* Enhanced Contact Form */}
          <div className="contact-form-section bg-light-gray p-6 rounded-lg mb-10">
            <h2 className="text-2xl font-semibold mb-4">Send Us a Message</h2>
            <form className="space-y-5" onSubmit={handleSubmit}>
              <div className="form-group">
                <label htmlFor="name" className="block text-sm font-medium mb-2">Name</label>
                <input
                  type="text"
                  id="name"
                  className="w-full p-3 border rounded-lg focus:border-primary-blue focus:ring-2 focus:ring-primary-blue focus:outline-none transition"
                  placeholder="Your Name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="email" className="block text-sm font-medium mb-2">Email</label>
                <input
                  type="email"
                  id="email"
                  className="w-full p-3 border rounded-lg focus:border-primary-blue focus:ring-2 focus:ring-primary-blue focus:outline-none transition"
                  placeholder="Your Email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="message" className="block text-sm font-medium mb-2">Message</label>
                <textarea
                  id="message"
                  className="w-full p-3 border rounded-lg focus:border-primary-blue focus:ring-2 focus:ring-primary-blue focus:outline-none transition"
                  rows={4}
                  placeholder="How can we assist you?"
                  value={formData.message}
                  onChange={handleInputChange}
                  required
                ></textarea>
              </div>
              <button
                type="submit"
                className="bg-button-primary-bg text-button-primary-text px-6 py-3 rounded-lg hover:bg-button-primary-hover active:bg-button-primary-active transition font-bold"
                disabled={formStatus === 'sending' || formStatus === 'sent'}
              >
                {formStatus === 'sending' ? 'Sending...' : formStatus === 'sent' ? 'Message Sent!' : 'Send Message'}
              </button>
              {formStatus === 'sent' && <p className="text-primary-success-code-color text-sm mt-2">Thank you for contacting us! We'll get back to you soon.</p>}
            </form>
          </div>

          {/* Modern Map Placeholder */}
          <div className="map-section mt-10">
            <h2 className="text-2xl font-semibold mb-4">Where We Operate</h2>
            <p className="mb-6">Serving all of Curaçao – from Willemstad to the beaches!</p>
            <div className="map-placeholder bg-gray-border rounded-lg h-80 flex flex-col items-center justify-center relative overflow-hidden">
              <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-primary-blue to-primary-blue-dark opacity-20"></div>
              <p className="text-white text-lg font-bold z-10">Curaçao Map Coming Soon</p>
              <p className="text-white text-sm z-10 mt-2">Interactive map of our service areas will be available shortly.</p>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    </>
  );
};

export default ContactUs;
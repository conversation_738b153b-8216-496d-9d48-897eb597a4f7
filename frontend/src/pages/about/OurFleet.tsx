// frontend/src/pages/about/OurFleet.tsx
import Navbar from "../../components/blades/navbar";
import Footer from "../../components/blades/footer";
import "../../styles/pages/about/OurFleet.scss"; // Main styles
import "../../styles/pages/about/animationsOurFleet.scss"; // Animation styles

const fleetData = [
  {
    type: "Economy",
    name: "Eco Cruiser",
    seats: 4,
    luggage: "2 bags",
    description: "Affordable rides for quick trips around Curaçao.",
    image: "/logo.jpg",
  },
  {
    type: "Comfort",
    name: "Comfy Rider",
    seats: 4,
    luggage: "3 bags",
    description: "Extra legroom for a relaxed island journey.",
    image: "/logo.jpg",
  },
  {
    type: "XL",
    name: "Island Hauler",
    seats: 7,
    luggage: "5 bags",
    description: "Perfect for groups exploring the island.",
    image: "/logo.jpg",
  },
  {
    type: "Cargo",
    name: "Load Master",
    seats: 2,
    luggage: "Large cargo space",
    description: "Ideal for moving gear or big shopping hauls.",
    image: "/logo.jpg",
  },
];

const OurFleet = () => {
  return (
    <>
      <Navbar />
      <div className="page-content">
        <div className="fleet-page container mx-auto p-6">
          <h1 className="text-3xl font-bold mb-6">Our Fleet</h1>
          <p className="text-lg mb-8">
            Meet the Sinusta Taxi fleet—crafted to suit every ride across Curaçao!
          </p>

          <div className="fleet-gallery">
            {fleetData.map((vehicle, index) => (
              <div className="vehicle-card" key={index}>
                <div className="vehicle-image">
                  <img src={vehicle.image} alt={`${vehicle.name} vehicle`} />
                </div>
                <div className="vehicle-details">
                  <h2 className="vehicle-name">{vehicle.name}</h2>
                  <div className="vehicle-info">
                    <div className="info-item">
                      <span className="label">Type:</span>
                      <span className="value">{vehicle.type}</span>
                    </div>
                    <div className="info-item">
                      <span className="label">Seats:</span>
                      <span className="value">{vehicle.seats}</span>
                    </div>
                    <div className="info-item">
                      <span className="label">Luggage:</span>
                      <span className="value">{vehicle.luggage}</span>
                    </div>
                  </div>
                  <p className="vehicle-description">{vehicle.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
};

export default OurFleet;
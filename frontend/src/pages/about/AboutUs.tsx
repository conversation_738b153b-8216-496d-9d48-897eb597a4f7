// frontend/src/pages/AboutUs.tsx
import Navbar from "../../components/blades/navbar";
import Footer from "../../components/blades/footer";
import "../../styles/pages/about/AboutUs.scss"; // Specific styles
import "../../styles/pages/about/animationsAboutUs.scss"; // Animation styles
import { useEffect } from "react";

const teamData = [
  {
    name: "<PERSON>",
    title: "CF<PERSON>",
    image: "/hinamizawa/johnDoe.webp",
    bio: "<PERSON> keeps our finances rolling smoother than a taxi on a fresh road.",
    favoriteSpot: "<PERSON>a Kenepa",
  },
  {
    name: "<PERSON><PERSON> Wizard",
    title: "CEO",
    image: "/hinamizawa/ozz.webp",
    bio: "<PERSON><PERSON>’s vision drives Sinusta Taxi to new horizons with a sprinkle of magic.",
    favoriteSpot: "Willemstad Waterfront",
  },
  {
    name: "<PERSON>",
    title: "Import Manager",
    image: "/hinamizawa/mary<PERSON>ane.webp",
    bio: "Mary ensures our fleet is always ready to roll, no matter the challenge.",
    favoriteSpot: "Cas Abao Beach",
  },
];

const AboutUs = () => {
  useEffect(() => {
    // Intersection Observer for scroll animations
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("in-view");
            observer.unobserve(entry.target); // Stop observing once triggered
          }
        });
      },
      { threshold: 0.2, rootMargin: "0px 0px -50px 0px" }
    );

    // Observe sections for animation
    const sections = document.querySelectorAll(".about-section, .team-section");
    sections.forEach((section) => {
      observer.observe(section);
    });

    return () => {
      sections.forEach((section) => {
        observer.unobserve(section);
      });
    };
  }, []);

  return (
    <>
      <Navbar />
      <div className="page-content">
        <div className="about-page container mx-auto p-6">
          <h1 className="text-3xl font-bold mb-6 animate-fade-in">About Sinusta Taxi</h1>

          <section className="about-section flex flex-wrap items-center mb-10">
            <img
              src="/logo.jpg"
              alt="Sinusta Taxi History"
              className="section-image animate-slide-in-left"
            />
            <div className="section-text">
              <h2 className="text-2xl font-semibold mb-2">Our Journey</h2>
              <p>
                In the very center of Curaçao, Sinusta Taxi was born out of a humble dream: to turn every trip around our lovely island into an unforgettable adventure. From the vibrant streets of Willemstad to the peaceful beaches of Jan Thiel, we have embodied the spirit of “Dushi Korsou” since day one. Attracted by the friendly hospitality that characterizes Curaçao, our founders created a service that goes beyond mere transportation—it is a festive celebration of the heart of our island.
              </p>
            </div>
          </section>

          <section className="about-section flex flex-wrap items-center mb-10 reverse">
            <img
              src="/logo.jpg"
              alt="Sinusta Taxi Mission"
              className="section-image animate-slide-in-right"
            />
            <div className="section-text">
              <h2 className="text-2xl font-semibold mb-2">Our Promise</h2>
              <p>
                At Sinusta Taxi, we're committed to making your visit to Curaçao even more enjoyable. What do we do? We want to offer you efficient, courteous, and hassle-free trips 24/7. Whether you're off to Hato Airport to board a flight, discovering the west coast's secrets, or relaxing while you watch the sunset at Mambo Beach, we're on your side. With our "TX"-plated cars and drivers who know the island like the back of their hand, we promise you reliability, comfort, and a dash of our island local charm in every trip.
              </p>
            </div>
          </section>

          <section className="about-section flex flex-wrap items-center mb-10">
            <img
              src="/logo.jpg"
              alt="Sinusta Taxi Team"
              className="section-image animate-slide-in-left"
            />
            <div className="section-text">
              <h2 className="text-2xl font-semibold mb-2">Our Family</h2>
              <p>
                We're more than a taxi company; we're a family united through our love of providing outstanding service. Our drivers are the best of Curaçao: bilingual, knowledgeable, and always energetic. Whether they're offering insider advice on where to find the best snorkeling spots or making sure you get to the cruise ship on time, they're the island experts in a four-wheeled vehicle. At Sinusta Taxi, each and every guest is treated like royalty—this is just the Sinusta way.
              </p>
            </div>
          </section>

          {/* Team Section */}
          <section className="team-section mb-10">
            <h2 className="text-2xl font-semibold mb-6 text-center">Meet Our Team</h2>
            <div className="team-grid grid grid-cols-1 md:grid-cols-3 gap-6">
              {teamData.map((member, index) => (
                <div className="team-member text-center animate-team-card" key={index}>
                  <img
                    src={member.image}
                    alt={member.name}
                    className="team-image mx-auto mb-4"
                  />
                  <h3 className="text-lg font-bold">{member.name}</h3>
                  <p className="text-md text-gray-600">{member.title}</p>
                  <p className="text-sm mt-2">{member.bio}</p>
                  <p className="text-sm italic mt-1">
                    Favorite Spot: {member.favoriteSpot}
                  </p>
                </div>
              ))}
            </div>
          </section>
        </div>
      </div>
      <Footer />
    </>
  );
};

export default AboutUs;
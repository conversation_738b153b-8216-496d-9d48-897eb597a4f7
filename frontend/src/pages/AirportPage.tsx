import React, { useState, useCallback, useEffect } from 'react';
import GoogleMap from '../components/GoogleMapAirport';
import Navbar from "../components/blades/navbar";
import Footer from "../components/blades/footer";
import '../styles/pages/AirportPages.scss';

interface FareResponse {
  fare?: number;
  error?: string;
}

const AIRPORT_COORDS = { lat: 12.18889, lng: -68.95972 }; // Hato Airport coordinates
const AIRPORT_ADDRESS = "Curaçao International Airport, Willemstad, Curaçao";

function useDebounce<T extends (...args: any[]) => void>(callback: T, delay: number) {
  const [timeoutId, setTimeoutId] = useState<number | null>(null);

  const debouncedFunction = useCallback((...args: Parameters<T>) => {
    if (timeoutId !== null) {
      clearTimeout(timeoutId);
    }
    const id = setTimeout(() => callback(...args), delay);
    setTimeoutId(id);
  }, [callback, delay]);

  return debouncedFunction;
}

const AirportPage: React.FC = () => {
  const [userOrigin, setUserOrigin] = useState<string>(''); 
  const [distance, setDistance] = useState<number | null>(null);
  const [fare, setFare] = useState<FareResponse | null>(null);
  const [passengerCount, setPassengerCount] = useState<number>(1);
  const [luggageCount, setLuggageCount] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const [timeoutOccurred, setTimeoutOccurred] = useState<boolean>(false);
  const [showSuccess, setShowSuccess] = useState<boolean>(false);
  const [mapKey, setMapKey] = useState<number>(0);
  const [showRoute, setShowRoute] = useState<boolean>(false); // New state for route display

  const handleLocationUpdate = useCallback((location: { lat: number; lng: number }) => {
    setUserOrigin(`${location.lat},${location.lng}`);
    setError('');
  }, []);

  // 1. Modify calculateDistance to return a Promise<number>
  const calculateDistance = useCallback(async (origin: string): Promise<number> => {
    if (!origin || !window.google?.maps) {
      const errorMsg = 'Please wait for Google Maps to load or ensure location is available.';
      setError(errorMsg);
      return Promise.reject(new Error(errorMsg)); // Reject the promise
    }

    setIsLoading(true);
    setError('');
    setDistance(null);
    setFare(null);
    setTimeoutOccurred(false);
    setShowSuccess(false);
    setShowRoute(false); // Reset route display

    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Request timed out')), 10000)
    );

    const distancePromise = new Promise<number>((resolve, reject) => { 
      const service = new google.maps.DistanceMatrixService();
      service.getDistanceMatrix(
        {
          origins: [origin],
          destinations: [`${AIRPORT_COORDS.lat},${AIRPORT_COORDS.lng}`],
          travelMode: google.maps.TravelMode.DRIVING,
        },
        (response, status) => {
          if (status === 'OK' && response?.rows[0]?.elements[0]?.status === 'OK') {
            const meters = response.rows[0].elements[0].distance.value;
            const km = meters / 1000;
            console.log(`Calculated distance: ${km} km`);
            // Don't set state here directly if handled by caller
            resolve(km); // Resolve the promise with the distance
          } else {
            reject(new Error('Unable to calculate distance.'));
          }
        }
      );
    });

    try {
      // await Promise.race([distancePromise, timeoutPromise]); // We need the result, so await the specific promise
      const calculatedKm = await Promise.race([distancePromise, timeoutPromise]) as number;
      setDistance(calculatedKm); // Set state on success
      setIsLoading(false);
      return calculatedKm; // Return the distance
    } catch (err: unknown) {
      let errorMsg = 'Error calculating distance.';
      if (err instanceof Error && err.message === 'Request timed out') {
        errorMsg = 'Request timed out. Please try again.';
        setTimeoutOccurred(true);
      } 
      setError(errorMsg);
      setIsLoading(false);
      return Promise.reject(new Error(errorMsg)); // Reject on error
    }
  }, []);

  // 2. Add 'displayRoute' parameter to calculateFare
  const calculateFare = useCallback(async (distanceKm: number, displayRoute: boolean = false) => {
    setIsLoading(true);
    setError('');
    setFare(null);

    const now = new Date();
    const tripTime = `${now.getHours().toString().padStart(2, '0')}:${now
      .getMinutes()
      .toString()
      .padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;

    const params = new URLSearchParams({
      distance_km: distanceKm.toString(),
      passenger_count: passengerCount.toString(),
      luggage_count: luggageCount.toString(),
      trip_time: tripTime,
      is_holiday: 'false',
    });

    try {
      const apiUrl = import.meta.env.VITE_BACKEND_API_URL;
      const response = await fetch(`${apiUrl}/fareCalc?${params}`);
      const data = await response.json();
      console.log('API Response:', data);
      setFare(data);
      if (displayRoute) {
          setShowRoute(true); // Show route only if requested
      }
    } catch (err) {
      setError('Failed to calculate fare.');
    } finally {
      setIsLoading(false);
    }
  }, [passengerCount, luggageCount]); // Removed setShowRoute from dependencies if it was there

  const debouncedCalculateFare = useDebounce(calculateFare, 500);

  // 3. Update handleSubmit to chain calls
  const handleSubmit = () => {
    if (!userOrigin) {
      setError('Please wait for your location to be detected.');
      return;
    }
    // Reset states before starting
    setError('');
    setDistance(null);
    setFare(null);
    setShowRoute(false);
    setTimeoutOccurred(false);
    
    calculateDistance(userOrigin)
      .then((calculatedDistance) => {
        // Distance calculated successfully, now calculate fare and request route display
        calculateFare(calculatedDistance, true); 
      })
      .catch((err) => {
        // Error occurred during distance calculation (already handled inside calculateDistance)
        console.error("Distance calculation failed:", err.message); 
      });
  };

  const handleRetry = () => {
    setMapKey(prev => prev + 1);
    calculateDistance(userOrigin).then(() => {
      if (!timeoutOccurred && !error) {
        setShowSuccess(true);
        setTimeout(() => {
          setShowSuccess(false);
          setTimeoutOccurred(false);
          // Should retry also show the route? Maybe not, let handleSubmit do that.
          // If yes, call calculateFare(distance, true) here too.
        }, 2000);
      }
    });
  };

  // 4. Update useEffect to call calculateFare with default displayRoute: false
  useEffect(() => {
    if (distance && !isLoading) {
      // Recalculate fare when inputs change, but don't show route automatically
      debouncedCalculateFare(distance); // displayRoute defaults to false
    }
  }, [distance, passengerCount, luggageCount, debouncedCalculateFare]);

  return (
    <>
      <Navbar />
      <div className="airport-page">
        <h1>Airport Taxi Fare</h1>
        <p className="destination">
          Destination: <strong>{AIRPORT_ADDRESS}</strong>
        </p>

        <GoogleMap
          key={mapKey}
          onLocationUpdate={handleLocationUpdate}
          userOrigin={userOrigin}
          showRoute={showRoute}
        />

        <div className="options-section">
          <label>
            Passengers:
            <input
              type="number"
              min={1}
              max={8}
              value={passengerCount}
              onChange={(e) => setPassengerCount(Number(e.target.value))}
              disabled={isLoading}
            />
          </label>
          <label>
            Luggage:
            <input
              type="number"
              min={0}
              value={luggageCount}
              onChange={(e) => setLuggageCount(Number(e.target.value))}
              disabled={isLoading}
            />
          </label>
        </div>

        <div className="button-container">
          <button
            className="fare-button"
            onClick={handleSubmit}
            disabled={isLoading || !userOrigin}
          >
            {isLoading ? (
              <>
                <span className="loading-spinner"></span>
                Calculating...
              </>
            ) : (
              'Calculate Fare'
            )}
          </button>
          {timeoutOccurred && (
            <button
              className="retry-button"
              onClick={handleRetry}
              disabled={isLoading}
            >
              Retry
            </button>
          )}
          {showSuccess && <span className="success-indicator">Success</span>}
        </div>

        {distance && (
          <p className="result">
            Distance to Airport: <strong>{distance.toFixed(2)} km</strong>
          </p>
        )}

        {error && <p className="error">{error}</p>}
        {fare?.fare && (
          <p className="result">
            Estimated Fare: <strong>${fare.fare.toFixed(2)}</strong>
          </p>
        )}
      </div>
      <Footer />
    </>
  );
};

export default AirportPage;
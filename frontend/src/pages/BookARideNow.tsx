// frontend/src/pages/BookARideNow.tsx
import React, { useState } from 'react';
import Navbar from "../components/blades/navbar";
import Footer from "../components/blades/footer";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import '../styles/pages/BookARideNow.scss';

const RideNow: React.FC = () => {
  const [pickupLocation, setPickupLocation] = useState<string>('');
  const [dropoffLocation, setDropoffLocation] = useState<string>('');
  const [date, setDate] = useState<Date | null>(new Date()); // Default to today
  const [time, setTime] = useState<Date | null>(new Date()); // Default to now
  const [passengerCount, setPassengerCount] = useState<number>(1);
  const [luggageCount, setLuggageCount] = useState<number>(0);
  const [isFetchingLocation, setIsFetchingLocation] = useState<boolean>(false);

  const handleGetCurrentLocation = () => {
    if (navigator.geolocation) {
      setIsFetchingLocation(true);
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;
  
          // Check if API key is missing
          if (!apiKey) {
            alert('API key is missing. Please check your configuration.');
            setIsFetchingLocation(false);
            return;
          }
  
          const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${apiKey}`;
  
          fetch(url)
            .then((response) => response.json())
            .then((data) => {
              if (data.status === 'OK') {
                if (data.results && data.results.length > 0) {
                  const address = data.results[0].formatted_address;
                  setPickupLocation(address);
                } else {
                  alert('No address found for your location.');
                }
              } else {
                // Log the full response for debugging
                console.error('Geocoding API error:', data);
                switch (data.status) {
                  case 'ZERO_RESULTS':
                    alert('No address found for your location.');
                    break;
                  case 'OVER_QUERY_LIMIT':
                    alert('API quota exceeded. Please try again later.');
                    break;
                  case 'REQUEST_DENIED':
                    alert('API request denied. Please check your API key.');
                    break;
                  case 'INVALID_REQUEST':
                    alert('Invalid request. Please try again.');
                    break;
                  case 'UNKNOWN_ERROR':
                    alert('An unknown error occurred. Please try again.');
                    break;
                  default:
                    alert('Unable to fetch address. Please try again.');
                    break;
                }
              }
            })
            .catch((error) => {
              console.error('Error fetching address:', error);
              alert('Error fetching address. Please check your internet connection and try again.');
            })
            .finally(() => {
              setIsFetchingLocation(false);
            });
        },
        (error) => {
          setIsFetchingLocation(false);
          switch (error.code) {
            case error.PERMISSION_DENIED:
              alert('Permission denied. Please allow location access.');
              break;
            case error.POSITION_UNAVAILABLE:
              alert('Location information is unavailable.');
              break;
            case error.TIMEOUT:
              alert('The request to get user location timed out.');
              break;
            default:
              alert('An unknown error occurred.');
              break;
          }
        }
      );
    } else {
      alert('Geolocation is not supported by this browser.');
    }
  };

  const handleBookRide = () => {
    console.log('Booking ride:', {
      pickupLocation,
      dropoffLocation,
      date: date?.toLocaleDateString(),
      time: time?.toLocaleTimeString(),
      passengerCount,
      luggageCount
    });
    // Add booking logic here later
  };

  return (
    <>
      <Navbar />
      <div className="ridenow-page">
        <section className="ride-booking">
          <h1>Book Your Ride Now!</h1>
          <p className="subtitle">Get around Curaçao with ease and comfort</p>

          <div className="ride-form">
            <div className="input-group">
              <label htmlFor="pickup">Pickup Location</label>
              <div className="location-input-wrapper">
                <input
                  id="pickup"
                  type="text"
                  placeholder="Enter pickup location"
                  value={pickupLocation}
                  onChange={(e) => setPickupLocation(e.target.value)}
                />
                <button
                  type="button"
                  onClick={handleGetCurrentLocation}
                  disabled={isFetchingLocation}
                >
                  {isFetchingLocation ? 'Fetching...' : 'Use My Location'}
                </button>
              </div>
            </div>
            <div className="input-group">
              <label htmlFor="dropoff">Dropoff Location</label>
              <input
                id="dropoff"
                type="text"
                placeholder="Enter dropoff location"
                value={dropoffLocation}
                onChange={(e) => setDropoffLocation(e.target.value)}
              />
            </div>
            <div className="options-group">
              <div className="input-group">
                <label htmlFor="passengers">Passengers</label>
                <input
                  id="passengers"
                  type="number"
                  min="1"
                  max="8"
                  value={passengerCount}
                  onChange={(e) => setPassengerCount(Number(e.target.value))}
                />
              </div>
              <div className="input-group">
                <label htmlFor="luggage">Luggage</label>
                <input
                  id="luggage"
                  type="number"
                  min="0"
                  value={luggageCount}
                  onChange={(e) => setLuggageCount(Number(e.target.value))}
                />
              </div>
            </div>
            <div className="datetime-group">
              <div className="input-group">
                <label htmlFor="date">Date</label>
                <div className="picker-wrapper">
                  <DatePicker
                    selected={date}
                    onChange={(date) => setDate(date)}
                    dateFormat="MMMM d, yyyy"
                    placeholderText="Select date"
                  />
                  <button type="button" onClick={() => setDate(new Date())}>Today</button>
                </div>
              </div>
              <div className="input-group">
                <label htmlFor="time">Time</label>
                <div className="picker-wrapper">
                  <DatePicker
                    selected={time}
                    onChange={(time) => setTime(time)}
                    showTimeSelect
                    showTimeSelectOnly
                    timeIntervals={15}
                    timeCaption="Time"
                    dateFormat="h:mm aa"
                    placeholderText="Select time"
                  />
                  <button type="button" onClick={() => setTime(new Date())}>Now</button>
                </div>
              </div>
            </div>
            <button className="book-button" onClick={handleBookRide}>
              Book Ride
            </button>
          </div>
        </section>
      </div>
      <Footer />
    </>
  );
};

export default RideNow;
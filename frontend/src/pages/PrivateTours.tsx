// frontend/src/pages/PrivateTours.tsx
import React from "react";
import { Link } from "react-router-dom"; // Import Link for navigation
import Navbar from "../components/blades/navbar";
import Footer from "../components/blades/footer";
import "../styles/pages/PrivateTours.scss";
import "../styles/pages/PrivateToursAnimations.scss";

const toursData = [
  {
    title: "West Coast Explorer",
    duration: "6 hours",
    driverRole: "Expert Driver",
    price: "$75 per person",
    description:
      "Discover the rugged beauty of Curaçao’s west coast with stops at pristine beaches, hidden caves, and stunning viewpoints. Perfect for nature lovers and adventurers.",
    includes: ["Pickup/Dropoff", "Bottled Water", "Local Snacks", "Beach Stops", "Campfire"],
    cancellation: "Free cancellation up to 24 hours before",
  },
  {
    title: "Willemstad Cultural Tour",
    duration: "4 hours",
    driverRole: "Cultural Guide",
    price: "$50 per person",
    description:
      "Immerse yourself in the vibrant streets of Willemstad, exploring historic landmarks, colorful architecture, and local markets with a knowledgeable guide.",
    includes: ["Pickup/Dropoff", "Bottled Water", "Street Food Tasting", "City Highlights"],
    cancellation: "$10 cancellation fee within 24 hours",
  },
  {
    title: "East End Snorkel Adventure",
    duration: "5 hours",
    driverRole: "Expert Driver",
    price: "$65 per person",
    description:
      "Dive into the crystal-clear waters of Curaçao’s east end, snorkeling at top spots like Tugboat Beach and Director’s Bay. A marine paradise awaits!",
    includes: ["Pickup/Dropoff", "Bottled Water", "Snorkel Gear", "Beach Time", "Light Lunch"],
    cancellation: "Free cancellation up to 48 hours before",
  },
  {
    title: "Hato Caves & History Tour",
    duration: "3.5 hours",
    driverRole: "History Specialist",
    price: "$45 per person",
    description:
      "Uncover Curaçao’s past with a visit to the Hato Caves, ancient rock formations, and a drive through historic plantations. A journey through time.",
    includes: ["Pickup/Dropoff", "Bottled Water", "Cave Entry", "Historical Insights"],
    cancellation: "$5 cancellation fee within 24 hours",
  },
  {
    title: "Sunset Beach Hop",
    duration: "4 hours",
    driverRole: "Expert Driver",
    price: "$60 per person",
    description:
      "Chase the sunset across Curaçao’s best beaches—Cas Abao, Playa PortoMari, and more—ending with a scenic twilight picnic as the sun dips below the horizon.",
    includes: ["Pickup/Dropoff", "Bottled Water", "Picnic Dinner", "Beach Towels"],
    cancellation: "Free cancellation up to 24 hours before",
  },
  {
    title: "Nightlife & Cocktails Tour",
    duration: "5 hours",
    driverRole: "Nightlife Expert",
    price: "$80 per person",
    description:
      "Experience Curaçao after dark with stops at trendy bars, live music spots, and a taste of local cocktails like Blue Curaçao. A night to remember!",
    includes: ["Pickup/Dropoff", "Bottled Water", "1 Cocktail Voucher", "Nightlife Guide"],
    cancellation: "$15 cancellation fee within 48 hours",
  },
];

const PrivateTours: React.FC = () => {
  return (
    <>
      <Navbar />
      <main className="private-tours">
        <h1>🚖 Private Island Tours with Sinusta Taxi</h1>
        <div className="tours-container">
          {toursData.map((tour, index) => (
            <div className="tour-card" key={index}>
              <div className="tour-image">
                <img src="/logo-no-background.png" alt={`${tour.title} Tour`} />
              </div>
              <div className="tour-details">
                <div className="tour-header">
                  <h2>{tour.title}</h2>
                  <div className="tour-meta">
                    <p className="meta-item">
                      <i className="fas fa-clock"></i> {tour.duration}
                    </p>
                    <p className="meta-item">
                      <i className="fas fa-user-tie"></i> {tour.driverRole}
                    </p>
                    <p className="meta-item price">
                      <i className="fas fa-tag"></i> {tour.price}
                    </p>
                  </div>
                </div>
                <div className="tour-description">
                  <p>{tour.description}</p>
                </div>
                <div className="tour-includes">
                  <h3>Includes:</h3>
                  <ul>
                    {tour.includes.map((item, idx) => (
                      <li key={idx}>{item}</li>
                    ))}
                  </ul>
                </div>
                <div className="tour-footer">
                  <p className="cancellation">{tour.cancellation}</p>
                  <Link to="/wip">
                    <button className="more-info-btn">
                      More Info <i className="fas fa-arrow-right"></i>
                    </button>
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      </main>
      <Footer />
    </>
  );
};

export default PrivateTours;
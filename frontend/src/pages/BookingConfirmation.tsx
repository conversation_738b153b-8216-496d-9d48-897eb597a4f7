// frontend/src/pages/BookingConfirmation.tsx
import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import { FaCheckCircle, FaMapMarkerAlt, FaClock, FaUser, FaPhone, FaRoute, FaArrowRight } from 'react-icons/fa';
import Navbar from '../components/blades/navbar';
import Footer from '../components/blades/footer';
import '../styles/pages/BookingConfirmation.scss';

interface BookingDetails {
  id: number;
  tracking_id: string;  // Add tracking_id from backend
  pickup_location: string;
  dropoff_location: string;
  ride_datetime: string;
  passenger_count: number;
  luggage_count: number;
  rider_name: string;
  rider_phone: string;
  distance_km?: number;
  calculated_fare?: number;
  status: string;
}

const BookingConfirmation: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [bookingDetails, setBookingDetails] = useState<BookingDetails | null>(null);
  const [countdown, setCountdown] = useState(10);

  useEffect(() => {
    // Prevent going back to the form with browser back button
    window.history.replaceState(null, '', window.location.href);
    
    // Get booking details from navigation state
    const bookingData = location.state?.bookingData;
    
    if (!bookingData) {
      // If no booking data, redirect to home page
      navigate('/', { replace: true });
      return;
    }

    setBookingDetails(bookingData);

    // Start countdown for automatic redirect to tracking
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          // Redirect to tracking page with tracking ID
          const trackingUrl = `/find-my-ride?tracking_id=${encodeURIComponent(bookingData.tracking_id)}`;
          navigate(trackingUrl, { replace: true });
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [location.state, navigate]);

  // Use tracking ID from backend response

  // Format date for display
  const formatDateTime = (dateString: string): string => {
    return new Date(dateString).toLocaleString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Format currency
  const formatCurrency = (amount: number | undefined): string => {
    if (!amount) return 'Calculating...';
    return `$${amount.toFixed(2)}`;
  };

  if (!bookingDetails) {
    return (
      <div>
        <Navbar />
        <div className="booking-error">
          <h2>Booking information not found</h2>
          <p>Redirecting to home page...</p>
        </div>
        <Footer />
      </div>
    );
  }

  const trackingRef = bookingDetails.tracking_id;

  return (
    <div>
      <Navbar />
      
      <main className="booking-confirmation">
        <div className="confirmation-container">
          {/* Success Header */}
          <div className="success-header">
            <FaCheckCircle className="success-icon" />
            <h1>Booking Confirmed!</h1>
            <p>Your ride has been successfully booked and is now being processed.</p>
          </div>

          {/* Booking Details Card */}
          <div className="booking-details-card">
            <div className="booking-header">
              <div className="booking-id">
                <h2>Ride #{bookingDetails.id}</h2>
                <span className="status-badge pending">Processing</span>
              </div>
              <div className="tracking-ref">
                <span className="label">Tracking Reference:</span>
                <code className="ref-code">{trackingRef}</code>
              </div>
            </div>

            <div className="booking-content">
              {/* Route Information */}
              <div className="route-section">
                <h3><FaRoute className="section-icon" /> Your Route</h3>
                <div className="route-display">
                  <div className="route-point pickup">
                    <FaMapMarkerAlt className="location-icon pickup-icon" />
                    <div className="location-details">
                      <span className="location-label">Pickup</span>
                      <span className="location-address">{bookingDetails.pickup_location}</span>
                    </div>
                  </div>
                  
                  <div className="route-line">
                    <FaArrowRight className="arrow-icon" />
                  </div>
                  
                  <div className="route-point dropoff">
                    <FaMapMarkerAlt className="location-icon dropoff-icon" />
                    <div className="location-details">
                      <span className="location-label">Dropoff</span>
                      <span className="location-address">{bookingDetails.dropoff_location}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Trip Details */}
              <div className="trip-details">
                <div className="detail-item">
                  <FaClock className="detail-icon" />
                  <div className="detail-content">
                    <span className="detail-label">Scheduled Time</span>
                    <span className="detail-value">{formatDateTime(bookingDetails.ride_datetime)}</span>
                  </div>
                </div>

                <div className="detail-item">
                  <FaUser className="detail-icon" />
                  <div className="detail-content">
                    <span className="detail-label">Passengers</span>
                    <span className="detail-value">{bookingDetails.passenger_count}</span>
                  </div>
                </div>

                {bookingDetails.luggage_count > 0 && (
                  <div className="detail-item">
                    <span className="detail-label">Luggage</span>
                    <span className="detail-value">{bookingDetails.luggage_count} pieces</span>
                  </div>
                )}

                {bookingDetails.distance_km && (
                  <div className="detail-item">
                    <span className="detail-label">Distance</span>
                    <span className="detail-value">{bookingDetails.distance_km.toFixed(1)} km</span>
                  </div>
                )}

                <div className="detail-item fare-item">
                  <span className="detail-label">Estimated Fare</span>
                  <span className="detail-value fare-amount">{formatCurrency(bookingDetails.calculated_fare)}</span>
                </div>
              </div>

              {/* Contact Information */}
              <div className="contact-section">
                <h3><FaPhone className="section-icon" /> Contact Information</h3>
                <div className="contact-details">
                  <div className="contact-item">
                    <span className="contact-label">Name:</span>
                    <span className="contact-value">{bookingDetails.rider_name}</span>
                  </div>
                  <div className="contact-item">
                    <span className="contact-label">Phone:</span>
                    <span className="contact-value">{bookingDetails.rider_phone}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Next Steps */}
          <div className="next-steps">
            <h3>What happens next?</h3>
            <div className="steps-list">
              <div className="step">
                <div className="step-number">1</div>
                <div className="step-content">
                  <h4>Assignment</h4>
                  <p>We're finding the best available driver for your ride</p>
                </div>
              </div>
              <div className="step">
                <div className="step-number">2</div>
                <div className="step-content">
                  <h4>Driver Contact</h4>
                  <p>Your assigned driver will contact you with vehicle details</p>
                </div>
              </div>
              <div className="step">
                <div className="step-number">3</div>
                <div className="step-content">
                  <h4>Pickup</h4>
                  <p>Your driver will arrive at the scheduled time and location</p>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="action-buttons">
            <Link
              to={`/find-my-ride?tracking_id=${encodeURIComponent(bookingDetails.tracking_id)}`}
              className="btn btn-primary track-btn"
            >
              <FaMapMarkerAlt />
              Track Your Ride
            </Link>
            
            <Link to="/" className="btn btn-secondary">
              Book Another Ride
            </Link>
          </div>

          {/* Auto-redirect Notice */}
          <div className="auto-redirect">
            <p>
              You'll be automatically redirected to track your ride in <strong>{countdown}</strong> seconds
            </p>
            <button 
              onClick={() => setCountdown(0)} 
              className="skip-btn"
            >
              Go Now
            </button>
          </div>

          {/* Important Notice */}
          <div className="important-notice">
            <h4>Important Information</h4>
            <ul>
              <li>Save your tracking reference: <strong>{trackingRef}</strong></li>
              <li>Keep your phone accessible for driver contact</li>
              <li>Be ready 5 minutes before your scheduled pickup time</li>
              <li>Contact us at +************ if you need to make changes</li>
            </ul>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default BookingConfirmation;
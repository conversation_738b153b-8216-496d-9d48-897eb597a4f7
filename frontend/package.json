{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@types/chart.js": "^2.9.41", "axios": "^1.8.3", "canvas-confetti": "^1.9.3", "chart.js": "^4.4.9", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-datepicker": "^8.2.1", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-router-dom": "^7.2.0", "sass": "^1.85.0"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/canvas-confetti": "^1.9.0", "@types/google.maps": "^3.58.1", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.19.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0"}}
# yaml-language-server: $schema=https://coderabbit.ai/integrations/schema.v2.json
language: "en-US"
early_access: true
reviews:
  profile: "assertive"  # Rigorous review profile for detailed code analysis
  request_changes_workflow: true  # Automatically request changes during reviews if needed
  high_level_summary: true  # Provides a high-level summary of review results
  poem: false  # Keeps reviews professional by disabling poetic summaries
  review_status: true  # Enables status messages indicating review status
  collapse_walkthrough: false  # Keeps walkthrough of code changes expanded for easier reading
  auto_review:
    enabled: true  # Enable automatic reviews for specific branches
    drafts: true  # Include drafts in automatic reviews
    include_branches:  # List of branches where auto reviews are enabled
      - "main"
      - "develop"
      - "bugfix/*"  # Include bugfix branches for automatic reviews
      - "feature/*"  # Auto review feature branches
    exclude_files:
      - "*.md"  # Exclude Markdown files from reviews
      - "*.json"  # Exclude JSON files from reviews
      - "*.logs"  # Exclude logs from reviews
      - "*.log"  # Exclude log files from reviews
      - "package-lock.json"  # Exclude lock files to reduce noise
    exclude_folder:
      - "node_modules"  # Exclude node_modules folder from reviews
      - "dist"  # Exclude dist folder from reviews
      - "build"  # Exclude build folder from reviews
      - "data"  # Exclude data folder from reviews
      - "test"  # Exclude test folder from reviews
      - "vendor"  # Exclude third-party libraries or vendor folders
    # max_reviews_per_commit: 5  # Limit the number of reviews per commit to avoid overloading

chat:
  auto_reply: true  # Automatically reply to questions or comments in CodeRabbit reviews
  enable_feedback: true  # Allows feedback on CodeRabbit's responses during conversations
  # max_messages: 10  # Limit automated responses in a single thread to avoid spamming
# Use an official lightweight Python image
FROM python:3.11

# Set the working directory in the container
WORKDIR /app

# Copy only necessary files (better for caching)
COPY requirements.txt manage.py /app/
COPY taxiWebApp/ /app/taxiWebApp/
COPY taxiAPI/ /app/taxiAPI/

# Install dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Expose port for Gunicorn
EXPOSE 8000

# Run the Django app
# Need to find a way to make my docker shows debug info like when i do python3 manage.py runserver
# See https://stackoverflow.com/a/41485234/1129915
CMD ["gunicorn", "--bind", "0.0.0.0:8000", "--log-level", "debug", "taxiWebApp.wsgi:application"]
